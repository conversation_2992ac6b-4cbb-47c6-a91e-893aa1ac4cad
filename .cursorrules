# Cursor Rules for Webinar Application

## Core Principles

### 1. Server-Side Rendering First
- **Default to Server Components**: All components should be server components by default unless they require client-side features
- **Only use "use client" when necessary**: 
  - Browser APIs (window, document, localStorage, etc.)
  - Event handlers (onClick, onChange, etc.)
  - React hooks (useState, useEffect, useRef, etc.)
  - Third-party libraries that require client-side (e.g., Daily.co SDK)
  - Real-time features (WebSockets, EventSource)
- **Data Fetching**: Fetch data on the server using async/await in server components. Pass data as props to client components.
- **API Routes**: Keep API routes for mutations and complex server-side logic. Use server components for data fetching when possible.

### 2. Mobile-First Design
- **80% of traffic is mobile**: Design and develop with mobile as the primary target
- **Tailwind Breakpoints**: Always start with mobile styles, then enhance for larger screens:
  - Mobile: Base styles (no prefix)
  - Tablet: `sm:` (640px+)
  - Desktop: `md:` (768px+)
  - Large Desktop: `lg:` (1024px+)
- **Touch Targets**: Minimum 44x44px for interactive elements (buttons, links, inputs)
- **Typography**: Use readable font sizes on mobile (minimum 14px base)
- **Spacing**: Generous padding/margins on mobile for touch interaction
- **Layouts**: 
  - Stack vertically on mobile
  - Use horizontal layouts only on larger screens (`md:` and above)
  - Full-width cards on mobile, constrained widths on desktop
- **Forms**: 
  - Full-width inputs on mobile
  - Vertical form layouts on mobile
  - Consider horizontal layouts only on `md:` and above

### 3. Data Fetching with TanStack Query
- **Client-Side Data Fetching**: Use `@tanstack/react-query` for all client-side data fetching
- **Query Hooks**: Create reusable query hooks in `lib/queries/` directory
- **Naming Convention**: 
  - Queries: `useWebinars`, `useWebinar`, `useExperiences`
  - Mutations: `useCreateWebinar`, `useUpdateWebinar`, `useDeleteWebinar`
- **Caching Strategy**:
  - Default staleTime: 1 minute for frequently changing data
  - Default cacheTime: 5 minutes
  - Use appropriate refetch intervals for real-time data
- **Error Handling**: Always handle errors in queries and mutations
- **Loading States**: Use `isLoading` and `isFetching` appropriately
- **Optimistic Updates**: Use optimistic updates for mutations when appropriate
- **Query Invalidation**: Invalidate related queries after mutations

### 4. Code Maintainability

#### Component Structure
- **Single Responsibility**: Each component should have one clear purpose
- **Composition**: Prefer composition over large, monolithic components
- **Server/Client Separation**: Clearly separate server and client components:
  - Server components: Fetch data, render static content
  - Client components: Handle interactions, manage UI state
  - Pattern: Server component fetches data → passes to client component wrapper
- **File Organization**: 
  - Co-locate related components
  - Group by feature, not by type
  - Use index files for exports when appropriate

#### TypeScript
- **Strict Typing**: Always use TypeScript types/interfaces
- **Type Safety**: Avoid `any` type. Use `unknown` when type is truly unknown
- **Interface Naming**: Use PascalCase for interfaces and types
- **Props Types**: Define explicit props interfaces for all components
- **Return Types**: Explicitly type function return types when not obvious

#### Naming Conventions
- **Components**: PascalCase (e.g., `WebinarCard`, `CreateWebinarForm`)
- **Files**: Match component name (e.g., `WebinarCard.tsx`)
- **Hooks**: Start with `use` (e.g., `useWebinars`, `useCreateWebinar`)
- **Functions**: camelCase (e.g., `fetchWebinars`, `formatDate`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `EVENT_TYPE_LABELS`, `MAX_DURATION`)
- **Types/Interfaces**: PascalCase (e.g., `Webinar`, `WebinarCardProps`)

#### Code Quality
- **DRY Principle**: Don't repeat yourself. Extract reusable logic into hooks or utilities
- **Comments**: Comment complex logic, not obvious code
- **Error Handling**: Always handle errors appropriately
- **Validation**: Validate inputs at boundaries (API routes, form submissions)
- **Accessibility**: Use semantic HTML, ARIA labels when needed, keyboard navigation
- **Performance**: 
  - Minimize client-side JavaScript bundle
  - Use React.memo for expensive components
  - Lazy load heavy components when appropriate

### 5. Specific Patterns

#### Server Component Pattern
```tsx
// Server Component (default export)
export default async function Page() {
  const data = await fetchData();
  return <ClientWrapper data={data} />;
}

// Client Component (separate file or marked with "use client")
"use client";
export function ClientWrapper({ data }) {
  const [state, setState] = useState();
  // Handle interactions
}
```

#### Query Hook Pattern
```tsx
// lib/queries/webinars.ts
export function useWebinars(experienceId: string) {
  return useQuery({
    queryKey: ['webinars', experienceId],
    queryFn: () => fetchWebinars(experienceId),
    staleTime: 60000, // 1 minute
  });
}
```

#### Mobile-First Styling Pattern
```tsx
// Mobile-first: base styles for mobile, then enhance
<div className="flex flex-col gap-4 p-4 md:flex-row md:gap-6 md:p-6">
  <button className="w-full py-3 sm:w-auto sm:px-6">
    Action
  </button>
</div>
```

### 6. Project-Specific Guidelines

#### Supabase Integration
- Use `supabaseServer` for server-side operations
- Use `supabase` client only in client components when necessary
- Always handle errors from Supabase queries

#### Whop SDK Integration
- Verify user tokens on server side
- Check access permissions before rendering protected content
- Use Whop SDK components from `@whop/react/components` for consistent UI

#### Daily.co Integration
- Keep WebinarRoom component as client component (requires browser APIs)
- Handle video/audio permissions gracefully
- Provide clear error messages for media access issues

### 7. Testing Considerations
- Write tests for critical business logic
- Test server components separately from client components
- Mock API calls in tests
- Test mobile and desktop layouts

### 8. Performance Best Practices
- Use Next.js Image component for images
- Code split large dependencies
- Optimize bundle size
- Use Suspense boundaries appropriately
- Implement proper loading states

### 9. Accessibility
- Semantic HTML elements
- ARIA labels for interactive elements
- Keyboard navigation support
- Focus management
- Screen reader compatibility
- Color contrast ratios

### 10. Git Workflow
- Descriptive commit messages
- Small, focused commits
- Review code before committing
- Use branches for features

## Summary Checklist

When creating or modifying components:
- [ ] Is this a server component? (default)
- [ ] If client component, is "use client" necessary?
- [ ] Is data fetching done server-side when possible?
- [ ] Are TanStack Query hooks used for client-side data fetching?
- [ ] Are styles mobile-first?
- [ ] Are touch targets at least 44x44px?
- [ ] Are TypeScript types defined?
- [ ] Is error handling implemented?
- [ ] Is the component maintainable and follows single responsibility?
- [ ] Is accessibility considered?

