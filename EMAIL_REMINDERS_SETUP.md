# Email Reminders System Setup

## Overview
This system provides email reminders, Whop DM reminders, customizable email templates, attendance tracking, and segmented follow-up emails based on attendee behavior.

## Database Setup

1. Run the SQL schema file in Supabase SQL Editor:
   - `supabase-email-schema.sql`

## Environment Variables

Add these to your `.env.local` and Railway environment:

```
RESEND_API_KEY=your_resend_api_key
RESEND_FROM_EMAIL=<EMAIL>
```

## Railway Worker Setup

### Email Reminders Worker

Create a Railway cron job that runs every 15 minutes:

**Command:**
```bash
tsx workers/email-reminders.ts
```

**Schedule:** `*/15 * * * *` (every 15 minutes)

### Follow-up Emails Worker

Create a Railway cron job that runs every hour:

**Command:**
```bash
tsx workers/follow-up-emails.ts
```

**Schedule:** `0 * * * *` (every hour)

## Railway Configuration

In Railway dashboard:
1. Create a new service for each worker
2. Set up cron schedules as specified above
3. Ensure environment variables are set for each worker service
4. Workers will automatically process queued emails

## Features

- **Email Templates**: Create customizable templates for confirmations, reminders, attended, and missed emails
- **Reminder Schedules**: Configure multiple reminder schedules with customizable timing (hours before webinar)
- **Attendance Tracking**: Automatically tracks when users join/leave Daily.co rooms
- **Follow-up Emails**: Automatically sends segmented emails based on attendance (attended/missed)
- **Whop DM Support**: Send reminders via Whop direct messages (requires Whop SDK implementation)

## Usage

### Creating Email Templates

1. Navigate to `/dashboard/[companyId]/email-templates`
2. Create templates for different types (confirmation, reminder, attended, missed)
3. Use template variables like `{{name}}`, `{{webinar_title}}`, etc.

### Setting Up Reminders

1. Navigate to webinar registration page
2. Go to "Reminders" tab
3. Add reminder schedules with custom timing
4. Choose reminder type (email, DM, or both)
5. Optionally select a custom template

### Viewing Attendance

1. Navigate to webinar registration page
2. Go to "Attendance" tab
3. View attendance statistics and filter by status

## Template Variables

Available variables in email templates:
- `{{name}}` - Registrant's name
- `{{email}}` - Registrant's email
- `{{webinar_title}}` - Webinar title
- `{{webinar_date}}` - Formatted webinar date
- `{{webinar_time}}` - Formatted webinar time
- `{{join_url}}` - URL to join webinar
- `{{registration_date}}` - Registration date
- `{{custom_field.field_id}}` - Custom field values

