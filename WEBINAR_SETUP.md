# Webinar Admin Dashboard Setup Guide

## Overview
This implementation adds an admin dashboard for scheduling webinars and a user-facing page to view and join webinars.

## Setup Steps

### 1. Environment Variables
Add these to your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=https://brxerddrjfdotstsggeb.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJyeGVyZGRyamZkb3RzdHNnZ2ViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NjE5NDUxNjAsImV4cCI6MjA3NzUyMTE2MH0.iNXdPx7wz8-nDYmGvX6bROWuPBZd73N5chCRmZV8wSE
```

### 2. Database Schema
Run the SQL script in `supabase-schema.sql` in your Supabase SQL Editor:
1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase-schema.sql`
4. Execute the script

This will create:
- `webinars` table with all necessary columns
- Indexes for performance
- Row Level Security (RLS) policies

### 3. Routes

**Admin Dashboard:**
- `/dashboard/[companyId]` - View and manage scheduled webinars for a company

**User Experience:**
- `/experiences/[experienceId]` - View upcoming/current webinars and join them
- `/experiences/[experienceId]?webinarId=[id]` - Join a specific webinar

### 4. API Routes

**Webinars API:**
- `GET /api/webinars?experienceId=...` - List webinars for an experience
- `GET /api/webinars?companyId=...` - List webinars for a company
- `POST /api/webinars` - Create a new webinar (admin only)
- `GET /api/webinars/[webinarId]` - Get a single webinar
- `PATCH /api/webinars/[webinarId]` - Update a webinar (admin only)
- `DELETE /api/webinars/[webinarId]` - Delete a webinar (admin only)

### 5. Features

**Admin Dashboard:**
- Schedule new webinars with title, description, date/time, duration
- Assign hosts (user IDs)
- Edit existing webinars
- Delete webinars
- View all scheduled webinars for a company

**User Experience Page:**
- View current/live webinar prominently
- View all upcoming webinars
- Join webinars (disabled until start time)
- Automatic status updates (scheduled → live → completed)

### 6. Components Created

**Dashboard Components:**
- `app/dashboard/[companyId]/components/WebinarList.tsx`
- `app/dashboard/[companyId]/components/WebinarCard.tsx`
- `app/dashboard/[companyId]/components/CreateWebinarForm.tsx`

**Experience Components:**
- `app/experiences/[experienceId]/components/WebinarListView.tsx`
- `app/experiences/[experienceId]/components/WebinarCard.tsx`
- `app/experiences/[experienceId]/components/CurrentWebinarBanner.tsx`
- `app/experiences/[experienceId]/components/ExperienceContent.tsx`

### 7. Notes

- Webinar status is automatically updated based on scheduled time and duration
- The system uses the existing Daily.co room creation logic
- Each experience can have multiple scheduled webinars
- Users must have access to the experience to view webinars
- Only company members can create/edit/delete webinars

