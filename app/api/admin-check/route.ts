import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const experienceId = searchParams.get("experienceId");

		if (!experienceId) {
			return NextResponse.json(
				{ error: "Missing experienceId parameter" },
				{ status: 400 },
			);
		}

		// Verify user has access to the experience
		const access = await whopsdk.users.checkAccess(experienceId, { id: userId });
		if (!access.has_access) {
			return NextResponse.json(
				{ error: "You do not have access to this experience" },
				{ status: 403 },
			);
		}

		// Fetch experience data
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const user = await whopsdk.users.retrieve(userId);

		// Check various admin indicators
		const accessObj = access as any;
		const experienceAny = experience as any;
		const isAdmin =
			experienceAny.created_by === userId ||
			(experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage");

		return NextResponse.json({
			isAdmin,
			userId,
			experienceCreatedBy: experienceAny.created_by,
			experienceCompanyId: experienceAny.company_id,
			userCompanyId: user.id,
			accessObject: accessObj,
		});
	} catch (error) {
		console.error("Error checking admin status:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

