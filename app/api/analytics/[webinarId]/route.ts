import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import {
	getRegistrationStats,
	getAttendanceStats,
	getWatchTimeStats,
	getDropoffAnalysis,
	getDeviceAnalytics,
	getOfferAnalytics,
	getConversionAnalytics,
	getRevenueAnalytics,
	compareLiveVsReplay,
	generateCRMTags,
} from "@/lib/analytics";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		const experienceAny = experience as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);

		const accessObj = access as any;
		const companyAny = company as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { webinarId } = await params;

		// Get webinar to check access
		const { supabaseServer } = await import("@/lib/supabase");
		const { data: webinar, error: webinarError } = await supabaseServer
			.from("webinars")
			.select("company_id, experience_id")
			.eq("id", webinarId)
			.single();

		if (webinarError || !webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const hasAccess = await checkAdminAccess(
			userId,
			webinar.company_id,
			webinar.experience_id,
		);

		if (!hasAccess) {
			return NextResponse.json(
				{ error: "Unauthorized" },
				{ status: 403 },
			);
		}

		const searchParams = request.nextUrl.searchParams;
		const metric = searchParams.get("metric");

		if (!metric) {
			return NextResponse.json(
				{ error: "Missing metric parameter" },
				{ status: 400 },
			);
		}

		let result;

		switch (metric) {
			case "registration":
				result = await getRegistrationStats(webinarId);
				break;
			case "attendance":
				result = await getAttendanceStats(webinarId);
				break;
			case "watch-time":
				result = await getWatchTimeStats(webinarId);
				break;
			case "dropoffs":
				result = await getDropoffAnalysis(webinarId);
				break;
			case "devices":
				result = await getDeviceAnalytics(webinarId);
				break;
			case "offers":
				result = await getOfferAnalytics(webinarId);
				break;
			case "conversions":
				result = await getConversionAnalytics(webinarId);
				break;
			case "revenue":
				result = await getRevenueAnalytics(webinarId);
				break;
			case "live-vs-replay":
				result = await compareLiveVsReplay(webinarId);
				break;
			case "crm-tags":
				result = await generateCRMTags(webinarId);
				break;
			default:
				return NextResponse.json(
					{ error: `Invalid metric: ${metric}` },
					{ status: 400 },
				);
		}

		return NextResponse.json(result);
	} catch (error) {
		console.error("Error fetching analytics:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

