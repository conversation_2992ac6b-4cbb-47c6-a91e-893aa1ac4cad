import { NextRequest, NextResponse } from "next/server";
import { trackDeviceInfo } from "@/lib/analytics";

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const {
			attendanceId,
			webinarId,
			userId,
			deviceType,
			platform,
			browser,
			userAgent,
			screenWidth,
			screenHeight,
		} = body;

		if (!attendanceId || !webinarId || !deviceType) {
			return NextResponse.json(
				{ error: "Missing required fields" },
				{ status: 400 },
			);
		}

		const device = await trackDeviceInfo({
			attendanceId,
			webinarId,
			userId,
			deviceType,
			platform,
			browser,
			userAgent,
			screenWidth,
			screenHeight,
		});

		return NextResponse.json({ device });
	} catch (error) {
		console.error("Error tracking device info:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

