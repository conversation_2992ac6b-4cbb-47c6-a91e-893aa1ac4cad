import { NextRequest, NextResponse } from "next/server";
import { trackDropoff } from "@/lib/analytics";

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const {
			attendanceId,
			webinarId,
			userId,
			submissionId,
			leftAtMinute,
			webinarDurationMinutes,
			reason,
		} = body;

		if (!attendanceId || !webinarId || leftAtMinute === undefined) {
			return NextResponse.json(
				{ error: "Missing required fields" },
				{ status: 400 },
			);
		}

		const dropoff = await trackDropoff({
			attendanceId,
			webinarId,
			userId,
			submissionId,
			leftAtMinute,
			webinarDurationMinutes,
			reason,
		});

		return NextResponse.json({ dropoff });
	} catch (error) {
		console.error("Error tracking dropoff:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

