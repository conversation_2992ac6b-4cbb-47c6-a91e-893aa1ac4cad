import { NextRequest, NextResponse } from "next/server";
import { trackEvent } from "@/lib/analytics";

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const {
			webinarId,
			userId,
			submissionId,
			eventType,
			eventData,
			deviceInfo,
			browserInfo,
		} = body;

		if (!webinarId || !eventType) {
			return NextResponse.json(
				{ error: "Missing required fields: webinarId, eventType" },
				{ status: 400 },
			);
		}

		const event = await trackEvent({
			webinarId,
			userId,
			submissionId,
			eventType,
			eventData,
			deviceInfo,
			browserInfo,
		});

		return NextResponse.json({ event });
	} catch (error) {
		console.error("Error tracking event:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

