import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import { trackJoin, trackLeave } from "@/lib/attendance";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		const experienceAny = experience as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);

		const accessObj = access as any;
		const companyAny = company as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { action, webinarId, submissionId, userId, email, dailySessionId, attendanceId } = body;

		if (action === "join") {
			if (!webinarId || !email) {
				return NextResponse.json(
					{ error: "Missing required fields: webinarId, email" },
					{ status: 400 },
				);
			}

			// If submissionId not provided, try to find it by email and webinarId
			let finalSubmissionId = submissionId;
			if (!finalSubmissionId) {
				const { data: submission } = await supabaseServer
					.from("registration_submissions")
					.select("id")
					.eq("webinar_id", webinarId)
					.eq("email", email)
					.order("created_at", { ascending: false })
					.limit(1)
					.single();

				if (submission) {
					finalSubmissionId = submission.id;
				}
			}

			// If no submission found, still track attendance but without submission_id
			// This handles cases where users join without registering
			const attendanceData: any = {
				webinar_id: webinarId,
				email,
				daily_session_id: dailySessionId || null,
				status: "attended",
			};

			if (finalSubmissionId) {
				attendanceData.submission_id = finalSubmissionId;
			}

			if (userId) {
				attendanceData.user_id = userId;
			}

			const { data, error } = await supabaseServer
				.from("webinar_attendance")
				.insert([attendanceData])
				.select()
				.single();

			if (error) {
				console.error("Error tracking attendance:", error);
				return NextResponse.json(
					{ error: "Failed to track attendance", details: error.message },
					{ status: 500 },
				);
			}

			return NextResponse.json({ attendance: data });
		}

		if (action === "leave") {
			if (!attendanceId) {
				return NextResponse.json(
					{ error: "Missing required field: attendanceId" },
					{ status: 400 },
				);
			}

			const attendance = await trackLeave({ attendanceId });

			return NextResponse.json({ attendance });
		}

		return NextResponse.json(
			{ error: "Invalid action. Use 'join' or 'leave'" },
			{ status: 400 },
		);
	} catch (error) {
		console.error("Error tracking attendance:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const webinarId = searchParams.get("webinarId");
		const submissionId = searchParams.get("submissionId");

		if (submissionId) {
			// Get attendance for specific submission
			const { data, error } = await supabaseServer
				.from("webinar_attendance")
				.select("*")
				.eq("submission_id", submissionId)
				.order("joined_at", { ascending: false })
				.limit(1)
				.single();

			if (error) {
				if (error.code === "PGRST116") {
					return NextResponse.json({ attendance: null });
				}
				return NextResponse.json(
					{ error: "Failed to fetch attendance", details: error.message },
					{ status: 500 },
				);
			}

			return NextResponse.json({ attendance: data });
		}

		if (!webinarId) {
			return NextResponse.json(
				{ error: "Missing webinarId parameter" },
				{ status: 400 },
			);
		}

		// Get webinar to check permissions
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("company_id, experience_id")
			.eq("id", webinarId)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const isAdmin = await checkAdminAccess(
			userId,
			webinar.company_id,
			webinar.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { data, error } = await supabaseServer
			.from("webinar_attendance")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("joined_at", { ascending: false });

		if (error) {
			return NextResponse.json(
				{ error: "Failed to fetch attendance", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ attendance: data || [] });
	} catch (error) {
		console.error("Error fetching attendance:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

