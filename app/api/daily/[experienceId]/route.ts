import { NextRequest, NextResponse } from "next/server";
import {
	getOrCreateRoom,
	createMeetingTokenWithDomainFallback,
	DailyApiError,
} from "@/lib/daily";
import { whopsdk } from "@/lib/whop-sdk";
import { supabaseServer } from "@/lib/supabase";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ experienceId: string }> },
) {
	try {
		const { experienceId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const webinarId = searchParams.get("webinarId");
		const joinToken = searchParams.get("token"); // Join link token

		if (!experienceId) {
			return NextResponse.json(
				{ error: "Missing experienceId" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		// For non-hosts joining a webinar, validate join link token
		if (webinarId && joinToken) {
			// Validate join link
			const { data: joinLink, error: linkError } = await supabaseServer
				.from("webinar_join_links")
				.select("*")
				.eq("token", joinToken)
				.eq("webinar_id", webinarId)
				.single();

			if (linkError || !joinLink) {
				return NextResponse.json(
					{ error: "Invalid join link" },
					{ status: 403 },
				);
			}

			// Check expiration
			const now = new Date();
			if (joinLink.expires_at && new Date(joinLink.expires_at) < now) {
				return NextResponse.json(
					{ error: "Join link has expired" },
					{ status: 403 },
				);
			}

			// Check access window
			if (joinLink.access_window_start && new Date(joinLink.access_window_start) > now) {
				return NextResponse.json(
					{ error: "Access window not yet open" },
					{ status: 403 },
				);
			}

			if (joinLink.access_window_end && new Date(joinLink.access_window_end) < now) {
				return NextResponse.json(
					{ error: "Access window has closed" },
					{ status: 403 },
				);
			}

			// Check ban status
			const { data: banAction } = await supabaseServer
				.from("webinar_moderation_actions")
				.select("id")
				.eq("webinar_id", webinarId)
				.eq("target_user_id", userId)
				.eq("action_type", "ban")
				.eq("is_active", true)
				.single();

			if (banAction) {
				return NextResponse.json(
					{ error: "You have been banned from this webinar" },
					{ status: 403 },
				);
			}
		}

		const [experience, user, access] = await Promise.all([
			whopsdk.experiences.retrieve(experienceId),
			whopsdk.users.retrieve(userId),
			whopsdk.users.checkAccess(experienceId, { id: userId }),
		]);

		if (!access.has_access) {
			return NextResponse.json(
				{ error: "You do not have access to this experience" },
				{ status: 403 },
			);
		}

		const displayName = user.name || `@${user.username}`;
		
		// Check if user is host/admin for the experience
		const experienceAny = experience as any;
		let isHost =
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id);
		
		// Check access object for admin indicators
		if (!isHost) {
			const accessObj = access as any;
			isHost =
				accessObj.is_admin === true ||
				accessObj.role === "admin" ||
				accessObj.can_admin === true ||
				accessObj.permissions?.includes("admin") ||
				accessObj.permissions?.includes("manage");
		}

		// Check if user is a co-host for the specific webinar
		let isCoHost = false;
		let userRole: "host" | "co-host" | "moderator" | "attendee" = "attendee";
		
		if (webinarId) {
			const { data: webinar } = await supabaseServer
				.from("webinars")
				.select("host_ids, presenter_roles")
				.eq("id", webinarId)
				.single();

			if (webinar) {
				// Check if user is in host_ids array
				if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
					isCoHost = true;
					isHost = true; // Co-hosts get host permissions
					
					// Check presenter_roles for specific role
					if (webinar.presenter_roles && typeof webinar.presenter_roles === "object") {
						const role = (webinar.presenter_roles as Record<string, string>)[userId];
						if (role === "co-host" || role === "moderator") {
							userRole = role;
						} else if (role === "host") {
							userRole = "host";
						} else {
							userRole = "co-host";
						}
					} else {
						userRole = "co-host";
					}
				}
			}
		}

		// Set role based on host status
		if (isHost && !isCoHost) {
			userRole = "host";
		}

		const domainOverride =
			process.env.DAILY_TOKEN_DOMAIN_OVERRIDE || request.nextUrl.hostname || undefined;

		const { room } = await getOrCreateRoom(experienceId);
		const tokenResponse = await createMeetingTokenWithDomainFallback({
			roomName: room.name,
			userName: displayName,
			userId,
			isHost,
			domain: domainOverride,
		});

		return NextResponse.json({
			roomUrl: room.url,
			roomName: room.name,
			token: tokenResponse.token,
			isHost,
			isCoHost,
			userRole,
			userId,
			userName: displayName,
		});
	} catch (error: unknown) {
		if (error instanceof DailyApiError) {
			console.error("Daily API error while managing room:", error);
			return NextResponse.json(
				{
					error: error.message,
					details: error.data,
				},
				{ status: error.status },
			);
		}

		console.error("Error managing Daily room join flow:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

