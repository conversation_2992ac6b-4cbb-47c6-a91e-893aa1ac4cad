import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { EmailTemplate } from "@/lib/supabase";

/**
 * Check if user is admin for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	const access = await whopsdk.users.checkAccess(companyId, { id: userId });
	const user = await whopsdk.users.retrieve(userId);
	const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

	const accessObj = access as any;
	return (
		companyAny.created_by === userId ||
		company.id === user.id ||
		accessObj.is_admin === true ||
		accessObj.role === "admin" ||
		accessObj.can_admin === true ||
		accessObj.permissions?.includes("admin") ||
		accessObj.permissions?.includes("manage")
	);
}

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ templateId: string }> },
) {
	try {
		const { templateId } = await params;

		const { data, error } = await supabaseServer
			.from("email_templates")
			.select("*")
			.eq("id", templateId)
			.single();

		if (error) {
			return NextResponse.json(
				{ error: "Failed to fetch template", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ template: data });
	} catch (error) {
		console.error("Error fetching email template:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ templateId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { templateId } = await params;
		const body = await request.json();

		// Get existing template to check permissions
		const { data: existingTemplate, error: fetchError } = await supabaseServer
			.from("email_templates")
			.select("*")
			.eq("id", templateId)
			.single();

		if (fetchError || !existingTemplate) {
			return NextResponse.json(
				{ error: "Template not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, existingTemplate.company_id);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const updateData: Partial<EmailTemplate> = {
			...body,
			updated_at: new Date().toISOString(),
		};

		const { data, error } = await supabaseServer
			.from("email_templates")
			.update(updateData)
			.eq("id", templateId)
			.select()
			.single();

		if (error) {
			console.error("Supabase error updating template:", error);
			return NextResponse.json(
				{
					error: "Failed to update template",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ template: data });
	} catch (error) {
		console.error("Error updating email template:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ templateId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { templateId } = await params;

		// Get existing template to check permissions
		const { data: existingTemplate, error: fetchError } = await supabaseServer
			.from("email_templates")
			.select("*")
			.eq("id", templateId)
			.single();

		if (fetchError || !existingTemplate) {
			return NextResponse.json(
				{ error: "Template not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, existingTemplate.company_id);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("email_templates")
			.delete()
			.eq("id", templateId);

		if (error) {
			console.error("Supabase error deleting template:", error);
			return NextResponse.json(
				{
					error: "Failed to delete template",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error deleting email template:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

