import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { EmailTemplate } from "@/lib/supabase";

/**
 * Check if user is admin for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	const access = await whopsdk.users.checkAccess(companyId, { id: userId });
	const user = await whopsdk.users.retrieve(userId);
	const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

	const accessObj = access as any;
	return (
		companyAny.created_by === userId ||
		company.id === user.id ||
		accessObj.is_admin === true ||
		accessObj.role === "admin" ||
		accessObj.can_admin === true ||
		accessObj.permissions?.includes("admin") ||
		accessObj.permissions?.includes("manage")
	);
}

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const companyId = searchParams.get("companyId");
		const templateId = searchParams.get("templateId");

		if (templateId) {
			// Get single template
			const { data, error } = await supabaseServer
				.from("email_templates")
				.select("*")
				.eq("id", templateId)
				.single();

			if (error) {
				return NextResponse.json(
					{ error: "Failed to fetch template", details: error.message },
					{ status: 500 },
				);
			}

			return NextResponse.json({ template: data });
		}

		if (!companyId) {
			return NextResponse.json(
				{ error: "Missing companyId parameter" },
				{ status: 400 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, companyId);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { data, error } = await supabaseServer
			.from("email_templates")
			.select("*")
			.eq("company_id", companyId)
			.order("created_at", { ascending: false });

		if (error) {
			return NextResponse.json(
				{ error: "Failed to fetch templates", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ templates: data || [] });
	} catch (error) {
		console.error("Error fetching email templates:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const body = await request.json();

		const {
			company_id,
			name,
			type,
			subject,
			body_html,
			body_text,
			variables,
			is_active,
		} = body;

		if (!company_id || !name || !type || !subject || !body_html) {
			return NextResponse.json(
				{
					error:
						"Missing required fields: company_id, name, type, subject, body_html",
				},
				{ status: 400 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, company_id);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const templateData: Partial<EmailTemplate> = {
			company_id,
			name,
			type,
			subject,
			body_html,
			body_text: body_text || null,
			variables: variables || [],
			is_active: is_active !== undefined ? is_active : true,
			created_by: userId,
		};

		const { data, error } = await supabaseServer
			.from("email_templates")
			.insert([templateData])
			.select()
			.single();

		if (error) {
			console.error("Supabase error creating template:", error);
			return NextResponse.json(
				{
					error: "Failed to create template",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ template: data }, { status: 201 });
	} catch (error) {
		console.error("Error creating email template:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

