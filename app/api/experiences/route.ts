import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import { supabaseServer } from "@/lib/supabase";

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const companyId = searchParams.get("companyId");
		const currentExperienceId = searchParams.get("experienceId");

		if (!companyId) {
			return NextResponse.json(
				{ error: "Missing companyId parameter" },
				{ status: 400 },
			);
		}

		// Verify user has access to the company
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		if (!access.has_access) {
			return NextResponse.json(
				{ error: "You do not have access to this company" },
				{ status: 403 },
			);
		}

		// Try to get experiences from existing webinars in Supabase
		const { data: webinars } = await supabaseServer
			.from("webinars")
			.select("experience_id")
			.eq("company_id", companyId)
			.order("created_at", { ascending: false })
			.limit(100);

		// Get unique experience IDs
		const uniqueExperienceIds = [
			...new Set((webinars || []).map((w) => w.experience_id)),
		];

		// Add current experience ID if provided and not already in list
		if (currentExperienceId && !uniqueExperienceIds.includes(currentExperienceId)) {
			uniqueExperienceIds.unshift(currentExperienceId);
		}

		// Fetch experience details for each ID
		const experiences = await Promise.all(
			uniqueExperienceIds.map(async (id) => {
				try {
					const exp = await whopsdk.experiences.retrieve(id);
					return {
						id: exp.id,
						name: exp.name || exp.id,
					};
				} catch {
					return null;
				}
			}),
		);

		// Filter out nulls and return
		const validExperiences = experiences.filter(
			(exp): exp is { id: string; name: string } => exp !== null,
		);

		return NextResponse.json({ experiences: validExperiences });
	} catch (error) {
		console.error("Error fetching experiences:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}
