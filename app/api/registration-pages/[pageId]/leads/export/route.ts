import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import { generateCSV, generateCSVFilename } from "@/lib/csv-export";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ pageId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { pageId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const format = searchParams.get("format") || "csv";

		// Get registration page to check permissions
		const { data: registrationPage, error: pageError } = await supabaseServer
			.from("registration_pages")
			.select("*")
			.eq("id", pageId)
			.single();

		if (pageError || !registrationPage) {
			return NextResponse.json(
				{ error: "Registration page not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(
			userId,
			registrationPage.company_id,
			registrationPage.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		// Get all registrations for this page
		const { data: registrations, error: regError } = await supabaseServer
			.from("registration_submissions")
			.select("*")
			.eq("registration_page_id", pageId)
			.order("created_at", { ascending: false });

		if (regError) {
			console.error("Supabase error fetching registrations:", regError);
			return NextResponse.json(
				{
					error: "Failed to fetch registrations",
					details: regError.message,
				},
				{ status: 500 },
			);
		}

		if (format === "csv") {
			// Get webinar title for filename
			const { data: webinar } = await supabaseServer
				.from("webinars")
				.select("title")
				.eq("id", registrationPage.webinar_id)
				.single();

			const webinarTitle = webinar?.title || "webinar";
			const csvContent = generateCSV(registrations || []);
			const filename = generateCSVFilename(webinarTitle);

			return new NextResponse(csvContent, {
				headers: {
					"Content-Type": "text/csv;charset=utf-8",
					"Content-Disposition": `attachment; filename="${filename}"`,
				},
			});
		}

		return NextResponse.json(
			{ error: "Unsupported format. Use 'csv'" },
			{ status: 400 },
		);
	} catch (error) {
		console.error("Error exporting leads:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

