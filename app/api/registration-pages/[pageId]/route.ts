import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { RegistrationPage } from "@/lib/supabase";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ pageId: string }> },
) {
	try {
		const { pageId } = await params;

		const { data, error } = await supabaseServer
			.from("registration_pages")
			.select("*")
			.eq("id", pageId)
			.single();

		if (error) {
			return NextResponse.json(
				{ error: "Failed to fetch registration page", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ registrationPage: data });
	} catch (error) {
		console.error("Error fetching registration page:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ pageId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { pageId } = await params;
		const body = await request.json();

		// Get existing page to check permissions
		const { data: existingPage, error: fetchError } = await supabaseServer
			.from("registration_pages")
			.select("*")
			.eq("id", pageId)
			.single();

		if (fetchError || !existingPage) {
			return NextResponse.json(
				{ error: "Registration page not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(
			userId,
			existingPage.company_id,
			existingPage.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		// Check if slug is being changed and if it's unique
		if (body.slug && body.slug !== existingPage.slug) {
			const { data: existingSlug } = await supabaseServer
				.from("registration_pages")
				.select("id")
				.eq("slug", body.slug)
				.single();

			if (existingSlug) {
				return NextResponse.json(
					{
						error: "Slug already exists. Please choose a different slug.",
					},
					{ status: 400 },
				);
			}
		}

		const updateData: Partial<RegistrationPage> = {
			...body,
			updated_at: new Date().toISOString(),
		};

		const { data, error } = await supabaseServer
			.from("registration_pages")
			.update(updateData)
			.eq("id", pageId)
			.select()
			.single();

		if (error) {
			console.error("Supabase error updating registration page:", error);
			return NextResponse.json(
				{
					error: "Failed to update registration page",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ registrationPage: data });
	} catch (error) {
		console.error("Error updating registration page:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ pageId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { pageId } = await params;

		// Get existing page to check permissions
		const { data: existingPage, error: fetchError } = await supabaseServer
			.from("registration_pages")
			.select("*")
			.eq("id", pageId)
			.single();

		if (fetchError || !existingPage) {
			return NextResponse.json(
				{ error: "Registration page not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(
			userId,
			existingPage.company_id,
			existingPage.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("registration_pages")
			.delete()
			.eq("id", pageId);

		if (error) {
			console.error("Supabase error deleting registration page:", error);
			return NextResponse.json(
				{
					error: "Failed to delete registration page",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error deleting registration page:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

