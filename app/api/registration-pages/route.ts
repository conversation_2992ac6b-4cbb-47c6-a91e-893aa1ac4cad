import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { RegistrationPage } from "@/lib/supabase";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function GET(request: NextRequest) {
	try {
		const searchParams = request.nextUrl.searchParams;
		const webinarId = searchParams.get("webinarId");
		const companyId = searchParams.get("companyId");
		const pageId = searchParams.get("pageId");
		const slug = searchParams.get("slug");

		if (pageId) {
			// Get single page by ID
			const { data, error } = await supabaseServer
				.from("registration_pages")
				.select("*")
				.eq("id", pageId)
				.single();

			if (error) {
				return NextResponse.json(
					{ error: "Failed to fetch registration page", details: error.message },
					{ status: 500 },
				);
			}

			return NextResponse.json({ registrationPage: data });
		}

		if (slug) {
			// Get single page by slug (public access)
			const { data, error } = await supabaseServer
				.from("registration_pages")
				.select("*")
				.eq("slug", slug)
				.eq("is_active", true)
				.single();

			if (error) {
				return NextResponse.json(
					{ error: "Registration page not found", details: error.message },
					{ status: 404 },
				);
			}

			return NextResponse.json({ registrationPage: data });
		}

		// List pages
		let query = supabaseServer.from("registration_pages").select("*");

		if (webinarId) {
			query = query.eq("webinar_id", webinarId);
		}

		if (companyId) {
			query = query.eq("company_id", companyId);
		}

		query = query.order("created_at", { ascending: false });

		const { data, error } = await query;

		if (error) {
			return NextResponse.json(
				{ error: "Failed to fetch registration pages", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ registrationPages: data || [] });
	} catch (error) {
		console.error("Error fetching registration pages:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const body = await request.json();

		const {
			webinar_id,
			experience_id,
			company_id,
			slug,
			template_id,
			title,
			description,
			custom_fields,
			thank_you_page_config,
			is_active,
			requires_auth,
		} = body;

		// Validate required fields
		if (!webinar_id || !experience_id || !company_id || !slug || !title) {
			return NextResponse.json(
				{
					error:
						"Missing required fields: webinar_id, experience_id, company_id, slug, title",
				},
				{ status: 400 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, company_id, experience_id);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		// Check if slug is unique
		const { data: existingPage } = await supabaseServer
			.from("registration_pages")
			.select("id")
			.eq("slug", slug)
			.single();

		if (existingPage) {
			return NextResponse.json(
				{ error: "Slug already exists. Please choose a different slug." },
				{ status: 400 },
			);
		}

		// Verify webinar exists
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("id")
			.eq("id", webinar_id)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const pageData: Partial<RegistrationPage> = {
			webinar_id,
			experience_id,
			company_id,
			slug,
			template_id: template_id || "standard",
			title,
			description: description || null,
			custom_fields: custom_fields || [],
			thank_you_page_config: thank_you_page_config || {},
			is_active: is_active !== undefined ? is_active : true,
			requires_auth: requires_auth !== undefined ? requires_auth : false,
			created_by: userId,
		};

		const { data, error } = await supabaseServer
			.from("registration_pages")
			.insert([pageData])
			.select()
			.single();

		if (error) {
			console.error("Supabase error creating registration page:", error);
			return NextResponse.json(
				{
					error: "Failed to create registration page",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ registrationPage: data }, { status: 201 });
	} catch (error) {
		console.error("Error creating registration page:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

