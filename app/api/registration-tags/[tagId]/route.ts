import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ tagId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { tagId } = await params;
		const body = await request.json();

		// Get existing tag to check permissions
		const { data: existingTag, error: fetchError } = await supabaseServer
			.from("registration_tags")
			.select("*")
			.eq("id", tagId)
			.single();

		if (fetchError || !existingTag) {
			return NextResponse.json(
				{ error: "Tag not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, existingTag.company_id);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		// Check if name is being changed and if it's unique
		if (body.name && body.name !== existingTag.name) {
			const { data: existingName } = await supabaseServer
				.from("registration_tags")
				.select("id")
				.eq("company_id", existingTag.company_id)
				.eq("name", body.name)
				.single();

			if (existingName) {
				return NextResponse.json(
					{
						error:
							"Tag name already exists for this company. Please choose a different name.",
					},
					{ status: 400 },
				);
			}
		}

		const { data, error } = await supabaseServer
			.from("registration_tags")
			.update(body)
			.eq("id", tagId)
			.select()
			.single();

		if (error) {
			console.error("Supabase error updating tag:", error);
			return NextResponse.json(
				{ error: "Failed to update tag", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ tag: data });
	} catch (error) {
		console.error("Error updating tag:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ tagId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { tagId } = await params;

		// Get existing tag to check permissions
		const { data: existingTag, error: fetchError } = await supabaseServer
			.from("registration_tags")
			.select("*")
			.eq("id", tagId)
			.single();

		if (fetchError || !existingTag) {
			return NextResponse.json(
				{ error: "Tag not found" },
				{ status: 404 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, existingTag.company_id);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("registration_tags")
			.delete()
			.eq("id", tagId);

		if (error) {
			console.error("Supabase error deleting tag:", error);
			return NextResponse.json(
				{ error: "Failed to delete tag", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error deleting tag:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

