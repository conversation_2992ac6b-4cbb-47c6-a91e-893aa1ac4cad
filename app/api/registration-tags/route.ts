import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { RegistrationTag } from "@/lib/supabase";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const companyId = searchParams.get("companyId");

		if (!companyId) {
			return NextResponse.json(
				{ error: "Missing companyId parameter" },
				{ status: 400 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, companyId);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { data, error } = await supabaseServer
			.from("registration_tags")
			.select("*")
			.eq("company_id", companyId)
			.order("created_at", { ascending: false });

		if (error) {
			console.error("Supabase error fetching tags:", error);
			return NextResponse.json(
				{ error: "Failed to fetch tags", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ tags: data || [] });
	} catch (error) {
		console.error("Error fetching tags:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const body = await request.json();

		const { company_id, name, color } = body;

		if (!company_id || !name) {
			return NextResponse.json(
				{ error: "Missing required fields: company_id, name" },
				{ status: 400 },
			);
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, company_id);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const tagData: Partial<RegistrationTag> = {
			company_id,
			name,
			color: color || "#3B82F6",
			created_by: userId,
		};

		const { data, error } = await supabaseServer
			.from("registration_tags")
			.insert([tagData])
			.select()
			.single();

		if (error) {
			console.error("Supabase error creating tag:", error);
			return NextResponse.json(
				{ error: "Failed to create tag", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ tag: data }, { status: 201 });
	} catch (error) {
		console.error("Error creating tag:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

