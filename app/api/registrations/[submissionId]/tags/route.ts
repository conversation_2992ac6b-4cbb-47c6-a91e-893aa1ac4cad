import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ submissionId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { submissionId } = await params;
		const body = await request.json();

		const { tag_ids } = body;

		if (!Array.isArray(tag_ids)) {
			return NextResponse.json(
				{ error: "tag_ids must be an array" },
				{ status: 400 },
			);
		}

		// Get submission to check permissions
		const { data: submission, error: fetchError } = await supabaseServer
			.from("registration_submissions")
			.select(
				"*, registration_pages!inner(company_id, experience_id)",
			)
			.eq("id", submissionId)
			.single();

		if (fetchError || !submission) {
			return NextResponse.json(
				{ error: "Registration submission not found" },
				{ status: 404 },
			);
		}

		const page = (submission as any).registration_pages;
		const isAdmin = await checkAdminAccess(
			userId,
			page.company_id,
			page.experience_id,
		);

		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		// Verify all tags belong to the same company
		if (tag_ids.length > 0) {
			const { data: tags } = await supabaseServer
				.from("registration_tags")
				.select("id, company_id")
				.in("id", tag_ids);

			const invalidTags = tags?.filter(
				(tag) => tag.company_id !== page.company_id,
			);

			if (invalidTags && invalidTags.length > 0) {
				return NextResponse.json(
					{ error: "Some tags do not belong to this company" },
					{ status: 400 },
				);
			}
		}

		// Remove existing tag assignments for this submission
		await supabaseServer
			.from("registration_tag_assignments")
			.delete()
			.eq("submission_id", submissionId);

		// Create new tag assignments
		if (tag_ids.length > 0) {
			const assignments = tag_ids.map((tagId: string) => ({
				submission_id: submissionId,
				tag_id: tagId,
			}));

			const { error: assignError } = await supabaseServer
				.from("registration_tag_assignments")
				.insert(assignments);

			if (assignError) {
				console.error("Supabase error assigning tags:", assignError);
				return NextResponse.json(
					{
						error: "Failed to assign tags",
						details: assignError.message,
					},
					{ status: 500 },
				);
			}
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error assigning tags:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

