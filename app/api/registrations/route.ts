import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { RegistrationSubmission } from "@/lib/supabase";
import { queueConfirmationEmail } from "@/lib/email-queue";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();

		const {
			registration_page_id,
			webinar_id,
			email,
			name,
			phone,
			custom_field_data,
			source,
		} = body;

		// Validate required fields
		if (!registration_page_id || !webinar_id || !email || !name || !source) {
			return NextResponse.json(
				{
					error:
						"Missing required fields: registration_page_id, webinar_id, email, name, source",
				},
				{ status: 400 },
			);
		}

		// Validate source
		if (source !== "public" && source !== "internal") {
			return NextResponse.json(
				{ error: "Invalid source. Must be 'public' or 'internal'" },
				{ status: 400 },
			);
		}

		// Get registration page to verify it exists and is active
		const { data: registrationPage, error: pageError } = await supabaseServer
			.from("registration_pages")
			.select("*")
			.eq("id", registration_page_id)
			.single();

		if (pageError || !registrationPage) {
			return NextResponse.json(
				{ error: "Registration page not found" },
				{ status: 404 },
			);
		}

		if (!registrationPage.is_active) {
			return NextResponse.json(
				{ error: "Registration page is not active" },
				{ status: 400 },
			);
		}

		// If internal source, verify user token
		let userId: string | null = null;
		if (source === "internal") {
			try {
				const { userId: verifiedUserId } = await whopsdk.verifyUserToken(
					request.headers,
				);
				userId = verifiedUserId;
			} catch {
				return NextResponse.json(
					{ error: "Authentication required for internal registrations" },
					{ status: 401 },
				);
			}
		}

		const submissionData: Partial<RegistrationSubmission> = {
			registration_page_id,
			webinar_id,
			email,
			name,
			phone: phone || null,
			custom_field_data: custom_field_data || {},
			source,
			user_id: userId,
		};

		const { data, error } = await supabaseServer
			.from("registration_submissions")
			.insert([submissionData])
			.select()
			.single();

		if (error) {
			console.error("Supabase error creating registration:", error);
			return NextResponse.json(
				{
					error: "Failed to create registration",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		// Queue confirmation email
		try {
			// Get webinar and check for confirmation template
			const [webinarResult, templateResult] = await Promise.all([
				supabaseServer
					.from("webinars")
					.select("*")
					.eq("id", webinar_id)
					.single(),
				supabaseServer
					.from("registration_pages")
					.select("company_id")
					.eq("id", registration_page_id)
					.single(),
			]);

			const webinar = webinarResult.data;
			const page = templateResult.data;

			// Get confirmation template for the company
			let confirmationTemplate = null;
			if (page?.company_id) {
				const { data: template } = await supabaseServer
					.from("email_templates")
					.select("*")
					.eq("company_id", page.company_id)
					.eq("type", "confirmation")
					.eq("is_active", true)
					.order("created_at", { ascending: false })
					.limit(1)
					.single();

				if (template) {
					confirmationTemplate = template;
				}
			}

			if (webinar) {
				await queueConfirmationEmail(
					data,
					webinar,
					confirmationTemplate || undefined,
				);
			}
		} catch (emailError) {
			// Don't fail registration if email fails
			console.error("Failed to queue confirmation email:", emailError);
		}

		// Generate unique join link token for this registration
		try {
			// Get webinar to determine access window settings
			const { data: webinar } = await supabaseServer
				.from("webinars")
				.select("start_time, end_time")
				.eq("id", webinar_id)
				.single();

			if (webinar) {
				// Generate secure token using crypto
				const token = crypto.randomUUID() + "-" + Buffer.from(Date.now().toString()).toString("base64url");
				
				// Calculate access window (default: 15 minutes before start, 1 hour after end)
				const startTime = new Date(webinar.start_time);
				const endTime = webinar.end_time ? new Date(webinar.end_time) : null;
				const accessWindowStart = new Date(startTime.getTime() - 15 * 60 * 1000); // 15 min before
				const accessWindowEnd = endTime 
					? new Date(endTime.getTime() + 60 * 60 * 1000) // 1 hour after end
					: new Date(startTime.getTime() + 4 * 60 * 60 * 1000); // 4 hours after start if no end time
				const expiresAt = accessWindowEnd;

				// Create join link
				await supabaseServer
					.from("webinar_join_links")
					.insert({
						webinar_id: webinar_id,
						submission_id: data.id,
						token,
						expires_at: expiresAt.toISOString(),
						access_window_start: accessWindowStart.toISOString(),
						access_window_end: accessWindowEnd.toISOString(),
						is_one_time_use: false, // Can be configured per webinar
					});
			}
		} catch (linkError) {
			// Don't fail registration if join link creation fails
			console.error("Failed to create join link:", linkError);
		}

		return NextResponse.json({ registration: data }, { status: 201 });
	} catch (error) {
		console.error("Error creating registration:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const pageId = searchParams.get("pageId");
		const webinarId = searchParams.get("webinarId");

		if (!pageId && !webinarId) {
			return NextResponse.json(
				{ error: "Missing pageId or webinarId parameter" },
				{ status: 400 },
			);
		}

		// Get registration page to check admin access
		let companyId: string | undefined;
		let experienceId: string | undefined;

		if (pageId) {
			const { data: page } = await supabaseServer
				.from("registration_pages")
				.select("company_id, experience_id")
				.eq("id", pageId)
				.single();

			if (page) {
				companyId = page.company_id;
				experienceId = page.experience_id;
			}
		}

		// Check admin access
		const isAdmin = await checkAdminAccess(userId, companyId, experienceId);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		let query = supabaseServer
			.from("registration_submissions")
			.select("*")
			.order("created_at", { ascending: false });

		if (pageId) {
			query = query.eq("registration_page_id", pageId);
		}

		if (webinarId) {
			query = query.eq("webinar_id", webinarId);
		}

		const { data, error } = await query;

		if (error) {
			console.error("Supabase error fetching registrations:", error);
			return NextResponse.json(
				{
					error: "Failed to fetch registrations",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ registrations: data || [] });
	} catch (error) {
		console.error("Error fetching registrations:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

