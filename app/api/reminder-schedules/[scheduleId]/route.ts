import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { ReminderSchedule } from "@/lib/supabase";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ scheduleId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { scheduleId } = await params;
		const body = await request.json();

		// Get existing schedule to check permissions
		const { data: existingSchedule, error: fetchError } = await supabaseServer
			.from("reminder_schedules")
			.select("*, webinars!inner(company_id, experience_id)")
			.eq("id", scheduleId)
			.single();

		if (fetchError || !existingSchedule) {
			return NextResponse.json(
				{ error: "Reminder schedule not found" },
				{ status: 404 },
			);
		}

		const webinar = (existingSchedule as any).webinars;
		const isAdmin = await checkAdminAccess(
			userId,
			webinar.company_id,
			webinar.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const updateData: Partial<ReminderSchedule> = {
			...body,
		};

		const { data, error } = await supabaseServer
			.from("reminder_schedules")
			.update(updateData)
			.eq("id", scheduleId)
			.select()
			.single();

		if (error) {
			console.error("Supabase error updating reminder schedule:", error);
			return NextResponse.json(
				{
					error: "Failed to update reminder schedule",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ schedule: data });
	} catch (error) {
		console.error("Error updating reminder schedule:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ scheduleId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { scheduleId } = await params;

		// Get existing schedule to check permissions
		const { data: existingSchedule, error: fetchError } = await supabaseServer
			.from("reminder_schedules")
			.select("*, webinars!inner(company_id, experience_id)")
			.eq("id", scheduleId)
			.single();

		if (fetchError || !existingSchedule) {
			return NextResponse.json(
				{ error: "Reminder schedule not found" },
				{ status: 404 },
			);
		}

		const webinar = (existingSchedule as any).webinars;
		const isAdmin = await checkAdminAccess(
			userId,
			webinar.company_id,
			webinar.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("reminder_schedules")
			.delete()
			.eq("id", scheduleId);

		if (error) {
			console.error("Supabase error deleting reminder schedule:", error);
			return NextResponse.json(
				{
					error: "Failed to delete reminder schedule",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error deleting reminder schedule:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

