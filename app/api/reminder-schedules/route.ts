import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { ReminderSchedule } from "@/lib/supabase";

/**
 * Check if user is admin for a company or experience
 */
async function checkAdminAccess(
	userId: string,
	companyId?: string,
	experienceId?: string,
): Promise<boolean> {
	if (experienceId) {
		const access = await whopsdk.users.checkAccess(experienceId, {
			id: userId,
		});
		const experience = await whopsdk.experiences.retrieve(experienceId);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(experienceAny.company_id &&
				experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	if (companyId) {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	}

	return false;
}

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const webinarId = searchParams.get("webinarId");

		if (!webinarId) {
			return NextResponse.json(
				{ error: "Missing webinarId parameter" },
				{ status: 400 },
			);
		}

		// Get webinar to check permissions
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("company_id, experience_id")
			.eq("id", webinarId)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const isAdmin = await checkAdminAccess(
			userId,
			webinar.company_id,
			webinar.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const { data, error } = await supabaseServer
			.from("reminder_schedules")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("timing_hours_before", { ascending: true });

		if (error) {
			return NextResponse.json(
				{ error: "Failed to fetch reminder schedules", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ schedules: data || [] });
	} catch (error) {
		console.error("Error fetching reminder schedules:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const body = await request.json();

		const {
			webinar_id,
			registration_page_id,
			reminder_type,
			timing_hours_before,
			is_active,
			template_id,
		} = body;

		if (!webinar_id || !reminder_type || timing_hours_before === undefined) {
			return NextResponse.json(
				{
					error:
						"Missing required fields: webinar_id, reminder_type, timing_hours_before",
				},
				{ status: 400 },
			);
		}

		// Get webinar to check permissions
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("company_id, experience_id")
			.eq("id", webinar_id)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const isAdmin = await checkAdminAccess(
			userId,
			webinar.company_id,
			webinar.experience_id,
		);
		if (!isAdmin) {
			return NextResponse.json(
				{ error: "You do not have admin access" },
				{ status: 403 },
			);
		}

		const scheduleData: Partial<ReminderSchedule> = {
			webinar_id,
			registration_page_id: registration_page_id || null,
			reminder_type,
			timing_hours_before,
			is_active: is_active !== undefined ? is_active : true,
			template_id: template_id || null,
			created_by: userId,
		};

		const { data, error } = await supabaseServer
			.from("reminder_schedules")
			.insert([scheduleData])
			.select()
			.single();

		if (error) {
			console.error("Supabase error creating reminder schedule:", error);
			return NextResponse.json(
				{
					error: "Failed to create reminder schedule",
					details: error.message,
				},
				{ status: 500 },
			);
		}

		return NextResponse.json({ schedule: data }, { status: 201 });
	} catch (error) {
		console.error("Error creating reminder schedule:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

