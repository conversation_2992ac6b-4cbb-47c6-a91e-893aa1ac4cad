import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import { getBranding, updateBranding } from "@/lib/team-management";

/**
 * Check if user is admin/owner for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	try {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	} catch (error) {
		console.error("Error checking admin access:", error);
		return false;
	}
}

/**
 * GET /api/team/[companyId]/branding
 * Get branding settings
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ companyId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { companyId } = await params;

		// Check admin access
		const hasAccess = await checkAdminAccess(userId, companyId);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const branding = await getBranding(companyId);

		return NextResponse.json({ branding });
	} catch (error) {
		console.error("Error fetching branding:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * PATCH /api/team/[companyId]/branding
 * Update branding settings
 */
export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ companyId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { companyId } = await params;
		const body = await request.json();
		const {
			logo_url,
			background_color,
			text_color,
			accent_color,
			favicon_url,
			custom_css,
		} = body;

		// Check admin access
		const hasAccess = await checkAdminAccess(userId, companyId);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const branding = await updateBranding({
			companyId,
			logoUrl: logo_url,
			backgroundColor: background_color,
			textColor: text_color,
			accentColor: accent_color,
			faviconUrl: favicon_url,
			customCss: custom_css,
			updatedBy: userId,
		});

		return NextResponse.json({ branding });
	} catch (error) {
		console.error("Error updating branding:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

