import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import { updateTeamMember, removeTeamMember } from "@/lib/team-management";

/**
 * Check if user is admin/owner for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	try {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	} catch (error) {
		console.error("Error checking admin access:", error);
		return false;
	}
}

/**
 * PATCH /api/team/[companyId]/members/[memberId]
 * Update team member role
 */
export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ companyId: string; memberId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { companyId, memberId } = await params;
		const body = await request.json();
		const { role, permissions } = body;

		// Check admin access
		const hasAccess = await checkAdminAccess(userId, companyId);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const member = await updateTeamMember({
			memberId,
			role,
			permissions,
		});

		return NextResponse.json({ member });
	} catch (error) {
		console.error("Error updating team member:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/team/[companyId]/members/[memberId]
 * Remove team member
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ companyId: string; memberId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { companyId } = await params;
		const { memberId } = await params;

		// Check admin access
		const hasAccess = await checkAdminAccess(userId, companyId);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		await removeTeamMember(memberId);

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error removing team member:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

