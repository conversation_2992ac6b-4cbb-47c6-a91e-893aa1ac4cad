import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import {
	getTeamMembers,
	addTeamMember,
	updateTeamMember,
	removeTeamMember,
	getBranding,
	updateBranding,
} from "@/lib/team-management";

/**
 * Check if user is admin/owner for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	try {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	} catch (error) {
		console.error("Error checking admin access:", error);
		return false;
	}
}

/**
 * GET /api/team/[companyId]/members
 * List team members
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ companyId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { companyId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const experienceId = searchParams.get("experienceId");

		// Check admin access
		const hasAccess = await checkAdminAccess(userId, companyId);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const members = await getTeamMembers(companyId, experienceId || undefined);

		return NextResponse.json({ members });
	} catch (error) {
		console.error("Error fetching team members:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * POST /api/team/[companyId]/members
 * Add team member
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ companyId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { companyId } = await params;
		const body = await request.json();
		const { user_id, experience_id, role, permissions } = body;

		if (!user_id || !role) {
			return NextResponse.json(
				{ error: "Missing user_id or role" },
				{ status: 400 },
			);
		}

		// Check admin access
		const hasAccess = await checkAdminAccess(userId, companyId);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const member = await addTeamMember({
			companyId,
			experienceId: experience_id,
			userId: user_id,
			role,
			permissions,
			createdBy: userId,
		});

		return NextResponse.json({ member });
	} catch (error) {
		console.error("Error adding team member:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

