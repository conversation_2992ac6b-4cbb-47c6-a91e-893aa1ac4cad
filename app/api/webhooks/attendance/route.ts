import { waitUntil } from "@vercel/functions";
import type { NextRequest } from "next/server";
import { trackEvent } from "@/lib/analytics";

export async function POST(request: NextRequest): Promise<Response> {
	try {
		const body = await request.json();
		const { event, data } = body;

		if (event === "registration.created" || event === "registration.updated") {
			waitUntil(handleRegistrationEvent(event, data));
		}

		return new Response("OK", { status: 200 });
	} catch (error) {
		console.error("Error processing registration webhook:", error);
		return new Response("OK", { status: 200 });
	}
}

async function handleRegistrationEvent(event: string, data: any) {
	try {
		if (!data.webinar_id) {
			console.warn("Missing webinar_id in registration event");
			return;
		}

		await trackEvent({
			webinarId: data.webinar_id,
			userId: data.user_id || undefined,
			submissionId: data.submission_id || undefined,
			eventType: event === "registration.created" ? "join" : "join",
			eventData: {
				registration_id: data.submission_id || data.id,
				email: data.email,
				name: data.name,
			},
		});
	} catch (error) {
		console.error("Error handling registration event:", error);
	}
}

