import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";

/**
 * POST /api/webhooks/daily-recording
 * Handle Daily.co recording completion webhook
 */
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();

		// Daily.co webhook payload structure
		const { type, payload } = body;

		if (type !== "recording.ready") {
			return NextResponse.json({ received: true });
		}

		const { room_name, recording_id, recording_url, duration_sec, start_ts, end_ts } = payload;

		// Find webinar by room name
		// Room names are typically formatted as "experience-{experienceId}-{webinarId}"
		const roomNameParts = room_name.split("-");
		if (roomNameParts.length < 3) {
			console.error("Unable to parse room name:", room_name);
			return NextResponse.json({ received: true });
		}

		const webinarId = roomNameParts[roomNameParts.length - 1]; // Last part is webinar ID

		// Find active recording for this webinar
		const { data: recording } = await supabaseServer
			.from("webinar_recordings")
			.select("id")
			.eq("webinar_id", webinarId)
			.eq("daily_recording_id", recording_id)
			.single();

		if (!recording) {
			// Try to find by webinar_id and status
			const { data: activeRecording } = await supabaseServer
				.from("webinar_recordings")
				.select("id")
				.eq("webinar_id", webinarId)
				.eq("status", "processing")
				.order("created_at", { ascending: false })
				.limit(1)
				.single();

			if (activeRecording) {
				// Update recording with Daily.co data
				await supabaseServer
					.from("webinar_recordings")
					.update({
						recording_url: recording_url,
						daily_recording_id: recording_id,
						status: "completed",
						duration_seconds: duration_sec || null,
						ended_at: end_ts ? new Date(end_ts).toISOString() : new Date().toISOString(),
					})
					.eq("id", activeRecording.id);

				// Create replay automatically
				await supabaseServer
					.from("webinar_replays")
					.insert({
						webinar_id: webinarId,
						recording_id: activeRecording.id,
						replay_mode: "on-demand",
						is_active: true,
					});
			}
		} else {
			// Update existing recording
			await supabaseServer
				.from("webinar_recordings")
				.update({
					recording_url: recording_url,
					status: "completed",
					duration_seconds: duration_sec || null,
					ended_at: end_ts ? new Date(end_ts).toISOString() : new Date().toISOString(),
				})
				.eq("id", recording.id);
		}

		return NextResponse.json({ received: true });
	} catch (error) {
		console.error("Error processing Daily.co webhook:", error);
		// Still return success to prevent webhook retries
		return NextResponse.json({ received: true });
	}
}

