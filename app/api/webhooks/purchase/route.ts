import { waitUntil } from "@vercel/functions";
import type { NextRequest } from "next/server";
import { trackEvent } from "@/lib/analytics";

export async function POST(request: NextRequest): Promise<Response> {
	try {
		const body = await request.json();
		const { event, data } = body;

		if (event === "attendee.joined" || event === "attendee.left") {
			waitUntil(handleAttendanceEvent(event, data));
		}

		return new Response("OK", { status: 200 });
	} catch (error) {
		console.error("Error processing attendance webhook:", error);
		return new Response("OK", { status: 200 });
	}
}

async function handleAttendanceEvent(event: string, data: any) {
	try {
		if (!data.webinar_id) {
			console.warn("Missing webinar_id in attendance event");
			return;
		}

		const eventType = event === "attendee.joined" ? "join" : "leave";

		await trackEvent({
			webinarId: data.webinar_id,
			userId: data.user_id || undefined,
			submissionId: data.submission_id || undefined,
			eventType,
			eventData: {
				attendance_id: data.attendance_id || data.id,
				duration_minutes: data.duration_minutes,
				status: data.status,
			},
		});
	} catch (error) {
		console.error("Error handling attendance event:", error);
	}
}

