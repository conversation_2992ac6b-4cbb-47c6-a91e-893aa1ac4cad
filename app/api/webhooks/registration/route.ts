import { waitUntil } from "@vercel/functions";
import type { Payment } from "@whop/sdk/resources.js";
import type { NextRequest } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import { trackEvent, trackConversion } from "@/lib/analytics";
import { handleWhopPayment } from "@/lib/whop-integration";

export async function POST(request: NextRequest): Promise<Response> {
	try {
		// Validate the webhook to ensure it's from Whop
		const requestBodyText = await request.text();
		const headers = Object.fromEntries(request.headers);
		const webhookData = whopsdk.webhooks.unwrap(requestBodyText, { headers });

		// Handle the webhook event
		const webhookAny = webhookData as any;
		if (webhookAny.type === "payment.succeeded") {
			waitUntil(handlePaymentSucceeded(webhookAny.data));
		} else if (webhookAny.type === "payment.refunded") {
			waitUntil(handlePaymentRefunded(webhookAny.data));
		}

		// Make sure to return a 2xx status code quickly. Otherwise the webhook will be retried.
		return new Response("OK", { status: 200 });
	} catch (error) {
		console.error("Error processing webhook:", error);
		// Still return 200 to prevent retries for invalid webhooks
		return new Response("OK", { status: 200 });
	}
}

async function handlePaymentSucceeded(payment: Payment) {
	try {
		console.log("[PAYMENT SUCCEEDED]", payment);

		// Handle Whop payment and create transaction record
		const transaction = await handleWhopPayment(payment.id, payment as any);

		// Track conversion event
		if (transaction.webinar_id && transaction.user_id) {
			await trackConversion({
				webinarId: transaction.webinar_id,
				userId: transaction.user_id,
				conversionType: "purchase",
				productId: transaction.whop_product_id,
				amount: transaction.amount,
				currency: transaction.currency,
				whopPaymentId: transaction.whop_payment_id,
			});

			// Track purchase event
			await trackEvent({
				webinarId: transaction.webinar_id,
				userId: transaction.user_id,
				eventType: "purchase",
				eventData: {
					product_id: transaction.whop_product_id,
					amount: transaction.amount,
					currency: transaction.currency,
				},
			});
		}
	} catch (error) {
		console.error("Error handling payment succeeded:", error);
	}
}

async function handlePaymentRefunded(payment: Payment) {
	try {
		console.log("[PAYMENT REFUNDED]", payment);

		// Update transaction status
		const { supabaseServer } = await import("@/lib/supabase");
		await supabaseServer
			.from("webinar_whop_transactions")
			.update({ status: "refunded" })
			.eq("whop_payment_id", payment.id);
	} catch (error) {
		console.error("Error handling payment refunded:", error);
	}
}

