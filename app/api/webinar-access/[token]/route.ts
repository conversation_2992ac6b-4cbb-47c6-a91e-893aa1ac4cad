import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";

/**
 * GET /api/webinar-access/[token]
 * Validate join link token and return access grant
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ token: string }> },
) {
	try {
		const { token } = await params;

		if (!token) {
			return NextResponse.json(
				{ error: "Token is required" },
				{ status: 400 },
			);
		}

		// Find join link
		const { data: joinLink, error: linkError } = await supabaseServer
			.from("webinar_join_links")
			.select("*")
			.eq("token", token)
			.single();

		if (linkError || !joinLink) {
			return NextResponse.json(
				{ error: "Invalid or expired join link" },
				{ status: 404 },
			);
		}

		const now = new Date();

		// Check expiration
		if (joinLink.expires_at && new Date(joinLink.expires_at) < now) {
			return NextResponse.json(
				{ error: "Join link has expired" },
				{ status: 403 },
			);
		}

		// Check one-time use
		if (joinLink.is_one_time_use && joinLink.used_at) {
			return NextResponse.json(
				{ error: "Join link has already been used" },
				{ status: 403 },
			);
		}

		// Check access window
		if (joinLink.access_window_start && new Date(joinLink.access_window_start) > now) {
			const waitTime = Math.ceil((new Date(joinLink.access_window_start).getTime() - now.getTime()) / 1000 / 60);
			return NextResponse.json(
				{ 
					error: "Access window not yet open",
					waitMinutes: waitTime,
					accessWindowStart: joinLink.access_window_start,
				},
				{ status: 403 },
			);
		}

		if (joinLink.access_window_end && new Date(joinLink.access_window_end) < now) {
			return NextResponse.json(
				{ error: "Access window has closed" },
				{ status: 403 },
			);
		}

		// Get webinar details
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("id, title, start_time, end_time, status")
			.eq("id", joinLink.webinar_id)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		// Mark as used (if one-time use)
		if (joinLink.is_one_time_use && !joinLink.used_at) {
			await supabaseServer
				.from("webinar_join_links")
				.update({
					used_at: now.toISOString(),
					use_count: (joinLink.use_count || 0) + 1,
				})
				.eq("id", joinLink.id);
		} else if (!joinLink.is_one_time_use) {
			// Increment use count
			await supabaseServer
				.from("webinar_join_links")
				.update({
					use_count: (joinLink.use_count || 0) + 1,
				})
				.eq("id", joinLink.id);
		}

		return NextResponse.json({
			valid: true,
			webinarId: joinLink.webinar_id,
			submissionId: joinLink.submission_id,
			accessGranted: true,
			webinar,
		});
	} catch (error) {
		console.error("Error validating join link:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

