import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		// Fetch webinar
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		// Check if user is in host_ids
		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		// Check experience admin access
		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-chat/[webinarId]
 * Get chat messages for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const messageType = searchParams.get("type") || "public"; // 'public' or 'private'
		const recipientUserId = searchParams.get("recipientUserId"); // For private messages

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		let query = supabaseServer
			.from("webinar_chat_messages")
			.select("*")
			.eq("webinar_id", webinarId)
			.eq("is_deleted", false)
			.order("created_at", { ascending: true });

		if (messageType === "private") {
			if (!recipientUserId) {
				return NextResponse.json(
					{ error: "recipientUserId is required for private messages" },
					{ status: 400 },
				);
			}
			// Get messages where user is sender or recipient
			query = query.or(
				`and(user_id.eq.${userId},recipient_user_id.eq.${recipientUserId}),and(user_id.eq.${recipientUserId},recipient_user_id.eq.${userId})`,
			);
		} else {
			query = query.eq("message_type", "public");
		}

		const { data: messages, error } = await query;

		if (error) {
			console.error("Error fetching chat messages:", error);
			return NextResponse.json(
				{ error: "Failed to fetch chat messages" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ messages: messages || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-chat:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-chat/[webinarId]
 * Send a chat message
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { message, messageType = "public", recipientUserId, userRole = "attendee", dailySessionId } = body;

		if (!message || !message.trim()) {
			return NextResponse.json(
				{ error: "Message is required" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;
		const user = await whopsdk.users.retrieve(userId);
		const userName = user.name || `@${user.username}`;

		// Check if user is muted or blocked
		const { data: moderationActions } = await supabaseServer
			.from("webinar_moderation_actions")
			.select("*")
			.eq("webinar_id", webinarId)
			.eq("target_user_id", userId)
			.eq("is_active", true)
			.in("action_type", ["mute", "block"]);

		if (moderationActions && moderationActions.length > 0) {
			const blockAction = moderationActions.find((a) => a.action_type === "block");
			if (blockAction) {
				return NextResponse.json(
					{ error: "You have been blocked from sending messages" },
					{ status: 403 },
				);
			}
			// Mute is handled in Daily.co, but we can also prevent messages here if needed
		}

		// Determine if message should be highlighted (hosts/moderators)
		const isHighlighted = userRole === "host" || userRole === "co-host" || userRole === "moderator";

		const { data: chatMessage, error } = await supabaseServer
			.from("webinar_chat_messages")
			.insert({
				webinar_id: webinarId,
				user_id: userId,
				user_name: userName,
				message: message.trim(),
				message_type: messageType,
				recipient_user_id: recipientUserId || null,
				user_role: userRole,
				is_highlighted: isHighlighted,
				daily_session_id: dailySessionId || null,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating chat message:", error);
			return NextResponse.json(
				{ error: "Failed to send message" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ message: chatMessage });
	} catch (error) {
		console.error("Error in POST /api/webinar-chat:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/webinar-chat/[webinarId]?messageId=...
 * Delete a chat message (moderator only)
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const messageId = searchParams.get("messageId");

		if (!messageId) {
			return NextResponse.json(
				{ error: "messageId query parameter is required" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		// Check if user has moderator access
		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);

		// Get message to check if user is the sender
		const { data: message } = await supabaseServer
			.from("webinar_chat_messages")
			.select("user_id")
			.eq("id", messageId)
			.single();

		if (!message) {
			return NextResponse.json(
				{ error: "Message not found" },
				{ status: 404 },
			);
		}

		// Allow deletion if user is moderator OR if user is the sender
		if (!hasModeratorAccess && message.user_id !== userId) {
			return NextResponse.json(
				{ error: "You do not have permission to delete this message" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("webinar_chat_messages")
			.update({
				is_deleted: true,
				deleted_at: new Date().toISOString(),
				deleted_by: userId,
			})
			.eq("id", messageId);

		if (error) {
			console.error("Error deleting chat message:", error);
			return NextResponse.json(
				{ error: "Failed to delete message" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error in DELETE /api/webinar-chat:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

