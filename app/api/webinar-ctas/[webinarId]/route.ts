import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-ctas/[webinarId]
 * Get CTAs for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const activeOnly = searchParams.get("activeOnly") === "true";

		let query = supabaseServer
			.from("webinar_ctas")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("created_at", { ascending: false });

		if (activeOnly) {
			query = query.eq("is_active", true);
		}

		const { data: ctas, error } = await query;

		if (error) {
			console.error("Error fetching CTAs:", error);
			return NextResponse.json(
				{ error: "Failed to fetch CTAs" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ ctas: ctas || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-ctas:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-ctas/[webinarId]
 * Create or trigger a CTA
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { 
			action, 
			ctaId, 
			title, 
			description, 
			buttonText, 
			buttonUrl, 
			imageUrl, 
			scheduledTime, 
			durationSeconds, 
			countdownSeconds, 
			showCountdown,
			triggerType 
		} = body;

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		// If action is "show", activate a CTA
		if (action === "show" && ctaId) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to show CTAs" },
					{ status: 403 },
				);
			}

			// Deactivate all other CTAs first
			await supabaseServer
				.from("webinar_ctas")
				.update({ is_active: false })
				.eq("webinar_id", webinarId)
				.eq("is_active", true);

			// Get current CTA to increment times_shown
			const { data: currentCTA } = await supabaseServer
				.from("webinar_ctas")
				.select("times_shown")
				.eq("id", ctaId)
				.single();

			const { data: cta, error } = await supabaseServer
				.from("webinar_ctas")
				.update({
					is_active: true,
					times_shown: (currentCTA?.times_shown || 0) + 1,
				})
				.eq("id", ctaId)
				.eq("webinar_id", webinarId)
				.select()
				.single();

			if (error) {
				console.error("Error showing CTA:", error);
				return NextResponse.json(
					{ error: "Failed to show CTA" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ cta });
		}

		// If action is "hide", deactivate a CTA
		if (action === "hide" && ctaId) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to hide CTAs" },
					{ status: 403 },
				);
			}

			const { error } = await supabaseServer
				.from("webinar_ctas")
				.update({ is_active: false })
				.eq("id", ctaId)
				.eq("webinar_id", webinarId);

			if (error) {
				console.error("Error hiding CTA:", error);
				return NextResponse.json(
					{ error: "Failed to hide CTA" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ success: true });
		}

		// If action is "track", track an interaction
		if (action === "track" && ctaId) {
			const { interactionType } = body;
			if (!interactionType) {
				return NextResponse.json(
					{ error: "interactionType is required" },
					{ status: 400 },
				);
			}

			await supabaseServer
				.from("webinar_cta_interactions")
				.insert({
					cta_id: ctaId,
					user_id: userId,
					interaction_type: interactionType,
				});

			return NextResponse.json({ success: true });
		}

		// Otherwise, create a new CTA
		if (!title || !buttonText || !buttonUrl) {
			return NextResponse.json(
				{ error: "Title, buttonText, and buttonUrl are required" },
				{ status: 400 },
			);
		}

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to create CTAs" },
				{ status: 403 },
			);
		}

		const { data: cta, error } = await supabaseServer
			.from("webinar_ctas")
			.insert({
				webinar_id: webinarId,
				created_by: userId,
				title,
				description: description || null,
				button_text: buttonText,
				button_url: buttonUrl,
				image_url: imageUrl || null,
				scheduled_time: scheduledTime ? new Date(scheduledTime).toISOString() : null,
				duration_seconds: durationSeconds || 30,
				countdown_seconds: countdownSeconds || 60,
				show_countdown: showCountdown !== false,
				trigger_type: triggerType || "manual",
				is_scheduled: !!scheduledTime,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating CTA:", error);
			return NextResponse.json(
				{ error: "Failed to create CTA" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ cta });
	} catch (error) {
		console.error("Error in POST /api/webinar-ctas:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/webinar-ctas/[webinarId]?ctaId=...
 * Delete a CTA
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const ctaId = searchParams.get("ctaId");

		if (!ctaId) {
			return NextResponse.json(
				{ error: "ctaId query parameter is required" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to delete CTAs" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("webinar_ctas")
			.delete()
			.eq("id", ctaId)
			.eq("webinar_id", webinarId);

		if (error) {
			console.error("Error deleting CTA:", error);
			return NextResponse.json(
				{ error: "Failed to delete CTA" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error in DELETE /api/webinar-ctas:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

