import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-handouts/[webinarId]
 * Get handouts for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;

		const { data: handouts, error } = await supabaseServer
			.from("webinar_handouts")
			.select("*")
			.eq("webinar_id", webinarId)
			.eq("is_visible", true)
			.order("created_at", { ascending: false });

		if (error) {
			console.error("Error fetching handouts:", error);
			return NextResponse.json(
				{ error: "Failed to fetch handouts" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ handouts: handouts || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-handouts:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-handouts/[webinarId]
 * Upload a handout (file URL should be provided after upload to storage)
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { fileName, fileUrl, fileType, fileSize, description } = body;

		if (!fileName || !fileUrl) {
			return NextResponse.json(
				{ error: "fileName and fileUrl are required" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to upload handouts" },
				{ status: 403 },
			);
		}

		const { data: handout, error } = await supabaseServer
			.from("webinar_handouts")
			.insert({
				webinar_id: webinarId,
				uploaded_by: userId,
				file_name: fileName,
				file_url: fileUrl,
				file_type: fileType || "document",
				file_size: fileSize || null,
				description: description || null,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating handout:", error);
			return NextResponse.json(
				{ error: "Failed to create handout" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ handout });
	} catch (error) {
		console.error("Error in POST /api/webinar-handouts:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/webinar-handouts/[webinarId]?handoutId=...
 * Delete a handout
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const handoutId = searchParams.get("handoutId");

		if (!handoutId) {
			return NextResponse.json(
				{ error: "handoutId query parameter is required" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to delete handouts" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("webinar_handouts")
			.delete()
			.eq("id", handoutId)
			.eq("webinar_id", webinarId);

		if (error) {
			console.error("Error deleting handout:", error);
			return NextResponse.json(
				{ error: "Failed to delete handout" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error in DELETE /api/webinar-handouts:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

