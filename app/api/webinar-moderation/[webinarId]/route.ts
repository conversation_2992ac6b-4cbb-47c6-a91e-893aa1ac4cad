import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * POST /api/webinar-moderation/[webinarId]
 * Perform moderation actions (mute, unmute, block, unblock, kick, ban)
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { actionType, targetUserId, reason, expiresAt } = body;

		if (!actionType || !targetUserId) {
			return NextResponse.json(
				{ error: "actionType and targetUserId are required" },
				{ status: 400 },
			);
		}

		const validActions = ["mute", "unmute", "block", "unblock", "kick", "ban"];
		if (!validActions.includes(actionType)) {
			return NextResponse.json(
				{ error: "Invalid action type" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		// Check if user has moderator access
		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to perform moderation actions" },
				{ status: 403 },
			);
		}

		// Check if target user is also a moderator (prevent moderators from moderating each other)
		const targetIsModerator = await checkModeratorAccess(targetUserId, webinarId);
		if (targetIsModerator && actionType !== "unmute" && actionType !== "unblock") {
			return NextResponse.json(
				{ error: "Cannot moderate other moderators" },
				{ status: 403 },
			);
		}

		// For unmute/unblock, deactivate existing actions
		if (actionType === "unmute" || actionType === "unblock") {
			const { error: updateError } = await supabaseServer
				.from("webinar_moderation_actions")
				.update({ is_active: false })
				.eq("webinar_id", webinarId)
				.eq("target_user_id", targetUserId)
				.eq("action_type", actionType === "unmute" ? "mute" : "block");

			if (updateError) {
				console.error("Error updating moderation action:", updateError);
				return NextResponse.json(
					{ error: "Failed to update moderation action" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ success: true });
		}

		// For mute/block, create new action
		// First deactivate any existing actions of the same type
		await supabaseServer
			.from("webinar_moderation_actions")
			.update({ is_active: false })
			.eq("webinar_id", webinarId)
			.eq("target_user_id", targetUserId)
			.eq("action_type", actionType);

		const { data: moderationAction, error } = await supabaseServer
			.from("webinar_moderation_actions")
			.insert({
				webinar_id: webinarId,
				target_user_id: targetUserId,
				action_type: actionType,
				performed_by: userId,
				reason: reason || null,
				expires_at: expiresAt ? new Date(expiresAt).toISOString() : null,
				is_active: true,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating moderation action:", error);
			return NextResponse.json(
				{ error: "Failed to perform moderation action" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ action: moderationAction });
	} catch (error) {
		console.error("Error in POST /api/webinar-moderation:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * GET /api/webinar-moderation/[webinarId]
 * Get moderation actions for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const targetUserId = searchParams.get("targetUserId");

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		// Check if user has moderator access
		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to view moderation actions" },
				{ status: 403 },
			);
		}

		let query = supabaseServer
			.from("webinar_moderation_actions")
			.select("*")
			.eq("webinar_id", webinarId)
			.eq("is_active", true)
			.order("created_at", { ascending: false });

		if (targetUserId) {
			query = query.eq("target_user_id", targetUserId);
		}

		const { data: actions, error } = await query;

		if (error) {
			console.error("Error fetching moderation actions:", error);
			return NextResponse.json(
				{ error: "Failed to fetch moderation actions" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ actions: actions || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-moderation:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

