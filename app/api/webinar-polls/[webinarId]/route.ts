import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-polls/[webinarId]
 * Get polls for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const includeResults = searchParams.get("includeResults") === "true";

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const { data: polls, error } = await supabaseServer
			.from("webinar_polls")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("created_at", { ascending: false });

		if (error) {
			console.error("Error fetching polls:", error);
			return NextResponse.json(
				{ error: "Failed to fetch polls" },
				{ status: 500 },
			);
		}

		// Get responses if requested
		if (includeResults && polls && polls.length > 0) {
			const pollIds = polls.map((p) => p.id);
			const { data: responses } = await supabaseServer
				.from("webinar_poll_responses")
				.select("*")
				.in("poll_id", pollIds);

			// Calculate results for each poll
			const pollsWithResults = polls.map((poll) => {
				const pollResponses = responses?.filter((r) => r.poll_id === poll.id) || [];
				const optionCounts: Record<string, number> = {};
				
				pollResponses.forEach((response) => {
					const selectedOptions = response.selected_options as string[];
					selectedOptions.forEach((optionId) => {
						optionCounts[optionId] = (optionCounts[optionId] || 0) + 1;
					});
				});

				const options = (poll.options as any[]) || [];
				const optionsWithCounts = options.map((opt) => ({
					...opt,
					count: optionCounts[opt.id] || 0,
					percentage: pollResponses.length > 0 
						? Math.round((optionCounts[opt.id] || 0) / pollResponses.length * 100)
						: 0,
				}));

				return {
					...poll,
					totalResponses: pollResponses.length,
					options: optionsWithCounts,
					userHasResponded: pollResponses.some((r) => r.user_id === userId),
				};
			});

			return NextResponse.json({ polls: pollsWithResults });
		}

		// Check if user has responded to each poll
		if (polls && polls.length > 0) {
			const pollIds = polls.map((p) => p.id);
			const { data: userResponses } = await supabaseServer
				.from("webinar_poll_responses")
				.select("poll_id")
				.eq("user_id", userId)
				.in("poll_id", pollIds);

			const respondedPollIds = new Set(userResponses?.map((r) => r.poll_id) || []);
			const pollsWithStatus = polls.map((poll) => ({
				...poll,
				userHasResponded: respondedPollIds.has(poll.id),
			}));

			return NextResponse.json({ polls: pollsWithStatus });
		}

		return NextResponse.json({ polls: polls || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-polls:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-polls/[webinarId]
 * Create a poll or respond to a poll
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { action, pollId, selectedOptions, title, question, pollType, options, isAnonymous, showResults } = body;

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;
		const user = await whopsdk.users.retrieve(userId);
		const userName = user.name || `@${user.username}`;

		// If action is "respond", add a response
		if (action === "respond" && pollId && selectedOptions) {
			const { error } = await supabaseServer
				.from("webinar_poll_responses")
				.upsert(
					{
						poll_id: pollId,
						user_id: userId,
						user_name: userName,
						selected_options: selectedOptions,
					},
					{ onConflict: "poll_id,user_id" },
				);

			if (error) {
				console.error("Error responding to poll:", error);
				return NextResponse.json(
					{ error: "Failed to submit response" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ success: true });
		}

		// If action is "start", activate a poll
		if (action === "start" && pollId) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to start polls" },
					{ status: 403 },
				);
			}

			// Deactivate all other polls first
			await supabaseServer
				.from("webinar_polls")
				.update({ is_active: false })
				.eq("webinar_id", webinarId)
				.eq("is_active", true);

			const { data: poll, error } = await supabaseServer
				.from("webinar_polls")
				.update({
					is_active: true,
					started_at: new Date().toISOString(),
				})
				.eq("id", pollId)
				.eq("webinar_id", webinarId)
				.select()
				.single();

			if (error) {
				console.error("Error starting poll:", error);
				return NextResponse.json(
					{ error: "Failed to start poll" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ poll });
		}

		// If action is "stop", deactivate a poll
		if (action === "stop" && pollId) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to stop polls" },
					{ status: 403 },
				);
			}

			const { error } = await supabaseServer
				.from("webinar_polls")
				.update({
					is_active: false,
					ended_at: new Date().toISOString(),
				})
				.eq("id", pollId)
				.eq("webinar_id", webinarId);

			if (error) {
				console.error("Error stopping poll:", error);
				return NextResponse.json(
					{ error: "Failed to stop poll" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ success: true });
		}

		// Otherwise, create a new poll
		if (!title || !question || !options || !Array.isArray(options) || options.length < 2) {
			return NextResponse.json(
				{ error: "Title, question, and at least 2 options are required" },
				{ status: 400 },
			);
		}

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to create polls" },
				{ status: 403 },
			);
		}

		// Format options with IDs
		const formattedOptions = options.map((opt: string, index: number) => ({
			id: `opt_${Date.now()}_${index}`,
			text: opt,
			order: index,
		}));

		const { data: poll, error } = await supabaseServer
			.from("webinar_polls")
			.insert({
				webinar_id: webinarId,
				created_by: userId,
				title,
				question,
				poll_type: pollType || "single",
				options: formattedOptions,
				is_anonymous: isAnonymous || false,
				show_results: showResults !== false,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating poll:", error);
			return NextResponse.json(
				{ error: "Failed to create poll" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ poll });
	} catch (error) {
		console.error("Error in POST /api/webinar-polls:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/webinar-polls/[webinarId]?pollId=...
 * Delete a poll
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const pollId = searchParams.get("pollId");

		if (!pollId) {
			return NextResponse.json(
				{ error: "pollId query parameter is required" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to delete polls" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("webinar_polls")
			.delete()
			.eq("id", pollId)
			.eq("webinar_id", webinarId);

		if (error) {
			console.error("Error deleting poll:", error);
			return NextResponse.json(
				{ error: "Failed to delete poll" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error in DELETE /api/webinar-polls:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

