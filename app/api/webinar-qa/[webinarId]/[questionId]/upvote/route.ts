import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * POST /api/webinar-qa/[webinarId]/[questionId]/upvote
 * Upvote or remove upvote from a question
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string; questionId: string }> },
) {
	try {
		const { webinarId, questionId } = await params;
		const body = await request.json();
		const { action } = body; // 'upvote' or 'remove'

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		// Verify question exists and belongs to webinar
		const { data: question } = await supabaseServer
			.from("webinar_questions")
			.select("id")
			.eq("id", questionId)
			.eq("webinar_id", webinarId)
			.single();

		if (!question) {
			return NextResponse.json(
				{ error: "Question not found" },
				{ status: 404 },
			);
		}

		if (action === "remove") {
			// Remove upvote
			const { error } = await supabaseServer
				.from("webinar_question_upvotes")
				.delete()
				.eq("question_id", questionId)
				.eq("user_id", userId);

			if (error) {
				console.error("Error removing upvote:", error);
				return NextResponse.json(
					{ error: "Failed to remove upvote" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ success: true });
		}

		// Add upvote (or update if exists)
		const { error } = await supabaseServer
			.from("webinar_question_upvotes")
			.upsert(
				{
					question_id: questionId,
					user_id: userId,
				},
				{ onConflict: "question_id,user_id" },
			);

		if (error) {
			console.error("Error adding upvote:", error);
			return NextResponse.json(
				{ error: "Failed to upvote question" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error in POST /api/webinar-qa upvote:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

