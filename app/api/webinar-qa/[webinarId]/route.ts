import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-qa/[webinarId]
 * Get Q&A questions for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const status = searchParams.get("status"); // 'pending', 'answered', 'dismissed', or null for all

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		let query = supabaseServer
			.from("webinar_questions")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("created_at", { ascending: false });

		if (status) {
			query = query.eq("status", status);
		}

		const { data: questions, error } = await query;

		if (error) {
			console.error("Error fetching questions:", error);
			return NextResponse.json(
				{ error: "Failed to fetch questions" },
				{ status: 500 },
			);
		}

		// Get upvote counts for each question
		if (questions && questions.length > 0) {
			const questionIds = questions.map((q) => q.id);
			const { data: upvotes } = await supabaseServer
				.from("webinar_question_upvotes")
				.select("question_id")
				.in("question_id", questionIds);

			// Count upvotes per question
			const upvoteCounts: Record<string, number> = {};
			if (upvotes) {
				upvotes.forEach((upvote) => {
					upvoteCounts[upvote.question_id] = (upvoteCounts[upvote.question_id] || 0) + 1;
				});
			}

			// Check if user has upvoted each question
			const { data: userUpvotes } = await supabaseServer
				.from("webinar_question_upvotes")
				.select("question_id")
				.eq("user_id", userId)
				.in("question_id", questionIds);

			const userUpvotedIds = new Set(userUpvotes?.map((u) => u.question_id) || []);

			// Add upvote counts and user upvote status to questions
			const questionsWithUpvotes = questions.map((q) => ({
				...q,
				upvotes: upvoteCounts[q.id] || 0,
				userHasUpvoted: userUpvotedIds.has(q.id),
			}));

			return NextResponse.json({ questions: questionsWithUpvotes });
		}

		return NextResponse.json({ questions: questions || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-qa:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-qa/[webinarId]
 * Create a question or answer a question
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { question, questionId, answer, action } = body;

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;
		const user = await whopsdk.users.retrieve(userId);
		const userName = user.name || `@${user.username}`;

		// If action is "answer", update existing question
		if (action === "answer" && questionId) {
			if (!answer || !answer.trim()) {
				return NextResponse.json(
					{ error: "Answer is required" },
					{ status: 400 },
				);
			}

			// Check if user has moderator access
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to answer questions" },
					{ status: 403 },
				);
			}

			const { data: updatedQuestion, error } = await supabaseServer
				.from("webinar_questions")
				.update({
					status: "answered",
					answer: answer.trim(),
					answered_by_user_id: userId,
					answered_by_name: userName,
					answered_at: new Date().toISOString(),
				})
				.eq("id", questionId)
				.eq("webinar_id", webinarId)
				.select()
				.single();

			if (error) {
				console.error("Error answering question:", error);
				return NextResponse.json(
					{ error: "Failed to answer question" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ question: updatedQuestion });
		}

		// If action is "dismiss", mark question as dismissed
		if (action === "dismiss" && questionId) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to dismiss questions" },
					{ status: 403 },
				);
			}

			const { error } = await supabaseServer
				.from("webinar_questions")
				.update({
					status: "dismissed",
				})
				.eq("id", questionId)
				.eq("webinar_id", webinarId);

			if (error) {
				console.error("Error dismissing question:", error);
				return NextResponse.json(
					{ error: "Failed to dismiss question" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ success: true });
		}

		// Otherwise, create a new question
		if (!question || !question.trim()) {
			return NextResponse.json(
				{ error: "Question is required" },
				{ status: 400 },
			);
		}

		const { data: newQuestion, error } = await supabaseServer
			.from("webinar_questions")
			.insert({
				webinar_id: webinarId,
				asked_by_user_id: userId,
				asked_by_name: userName,
				question: question.trim(),
				status: "pending",
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating question:", error);
			return NextResponse.json(
				{ error: "Failed to submit question" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ question: newQuestion });
	} catch (error) {
		console.error("Error in POST /api/webinar-qa:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

