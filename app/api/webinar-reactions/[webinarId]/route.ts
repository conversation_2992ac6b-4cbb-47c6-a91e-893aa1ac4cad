import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * POST /api/webinar-reactions/[webinarId]
 * Create a reaction
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { reactionType, durationMs } = body;

		if (!reactionType) {
			return NextResponse.json(
				{ error: "reactionType is required" },
				{ status: 400 },
			);
		}

		const validReactions = ["👍", "❤️", "😂", "🎉", "🔥", "👏", "💯", "🚀"];
		if (!validReactions.includes(reactionType)) {
			return NextResponse.json(
				{ error: "Invalid reaction type" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;
		const user = await whopsdk.users.retrieve(userId);
		const userName = user.name || `@${user.username}`;

		const { data: reaction, error } = await supabaseServer
			.from("webinar_reactions")
			.insert({
				webinar_id: webinarId,
				user_id: userId,
				user_name: userName,
				reaction_type: reactionType,
				duration_ms: durationMs || 3000,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating reaction:", error);
			return NextResponse.json(
				{ error: "Failed to create reaction" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ reaction });
	} catch (error) {
		console.error("Error in POST /api/webinar-reactions:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * GET /api/webinar-reactions/[webinarId]
 * Get recent reactions (for display)
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const limit = parseInt(searchParams.get("limit") || "50");

		const { data: reactions, error } = await supabaseServer
			.from("webinar_reactions")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("created_at", { ascending: false })
			.limit(limit);

		if (error) {
			console.error("Error fetching reactions:", error);
			return NextResponse.json(
				{ error: "Failed to fetch reactions" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ reactions: reactions || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-reactions:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

