import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-recording/[webinarId]
 * Get recording status for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;

		const { data: recordings, error } = await supabaseServer
			.from("webinar_recordings")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("created_at", { ascending: false });

		if (error) {
			console.error("Error fetching recordings:", error);
			return NextResponse.json(
				{ error: "Failed to fetch recordings" },
				{ status: 500 },
			);
		}

		// Get active recording
		const activeRecording = recordings?.find((r) => r.status === "recording" || r.status === "processing");

		return NextResponse.json({
			recordings: recordings || [],
			activeRecording,
		});
	} catch (error) {
		console.error("Error in GET /api/webinar-recording:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-recording/[webinarId]
 * Start or stop recording
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { action, recordingType, dailyRecordingId } = body;

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to manage recordings" },
				{ status: 403 },
			);
		}

		if (action === "start") {
			// Check if there's already an active recording
			const { data: activeRecording } = await supabaseServer
				.from("webinar_recordings")
				.select("id")
				.eq("webinar_id", webinarId)
				.in("status", ["recording", "processing"])
				.single();

			if (activeRecording) {
				return NextResponse.json(
					{ error: "Recording is already in progress" },
					{ status: 400 },
				);
			}

			// Create recording record
			const { data: recording, error } = await supabaseServer
				.from("webinar_recordings")
				.insert({
					webinar_id: webinarId,
					recording_type: recordingType || "cloud",
					status: "recording",
					daily_recording_id: dailyRecordingId || null,
					started_at: new Date().toISOString(),
				})
				.select()
				.single();

			if (error) {
				console.error("Error creating recording:", error);
				return NextResponse.json(
					{ error: "Failed to start recording" },
					{ status: 500 },
				);
			}

			return NextResponse.json({
				success: true,
				recording,
			});
		}

		if (action === "stop") {
			// Find active recording
			const { data: activeRecording } = await supabaseServer
				.from("webinar_recordings")
				.select("id")
				.eq("webinar_id", webinarId)
				.eq("status", "recording")
				.single();

			if (!activeRecording) {
				return NextResponse.json(
					{ error: "No active recording found" },
					{ status: 400 },
				);
			}

			// Update recording status
			const { data: recording, error } = await supabaseServer
				.from("webinar_recordings")
				.update({
					status: "processing",
					ended_at: new Date().toISOString(),
				})
				.eq("id", activeRecording.id)
				.select()
				.single();

			if (error) {
				console.error("Error stopping recording:", error);
				return NextResponse.json(
					{ error: "Failed to stop recording" },
					{ status: 500 },
				);
			}

			return NextResponse.json({
				success: true,
				recording,
			});
		}

		return NextResponse.json(
			{ error: "Invalid action. Use 'start' or 'stop'" },
			{ status: 400 },
		);
	} catch (error) {
		console.error("Error in POST /api/webinar-recording:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

