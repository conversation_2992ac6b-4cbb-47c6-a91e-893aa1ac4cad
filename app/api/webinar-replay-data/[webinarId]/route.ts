import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";

/**
 * GET /api/webinar-replay-data/[webinarId]
 * Get all replay interaction data for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;

		const { data: interactions, error } = await supabaseServer
			.from("webinar_replay_interactions")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("timestamp_offset", { ascending: true });

		if (error) {
			console.error("Error fetching replay interactions:", error);
			return NextResponse.json(
				{ error: "Failed to fetch replay interactions" },
				{ status: 500 },
			);
		}

		return NextResponse.json({
			interactions: interactions || [],
		});
	} catch (error) {
		console.error("Error in GET /api/webinar-replay-data:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-replay-data/[webinarId]
 * Capture interaction data for replay
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();

		const {
			interactionType,
			interactionId,
			timestampOffset,
			interactionData,
			webinarStartTime,
		} = body;

		if (!interactionType || !interactionId || timestampOffset === undefined) {
			return NextResponse.json(
				{ error: "Missing required fields" },
				{ status: 400 },
			);
		}

		// Calculate timestamp offset from webinar start if provided
		let offset = timestampOffset;
		if (webinarStartTime) {
			const startTime = new Date(webinarStartTime);
			const now = new Date();
			offset = (now.getTime() - startTime.getTime()) / 1000; // seconds
		}

		// Get active replay for this webinar
		const { data: activeReplay } = await supabaseServer
			.from("webinar_replays")
			.select("id")
			.eq("webinar_id", webinarId)
			.eq("is_active", true)
			.order("created_at", { ascending: false })
			.limit(1)
			.single();

		const { data: interaction, error } = await supabaseServer
			.from("webinar_replay_interactions")
			.insert({
				webinar_id: webinarId,
				replay_id: activeReplay?.id || null,
				interaction_type: interactionType,
				interaction_id: interactionId,
				timestamp_offset: offset,
				interaction_data: interactionData || {},
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating replay interaction:", error);
			return NextResponse.json(
				{ error: "Failed to capture interaction" },
				{ status: 500 },
			);
		}

		return NextResponse.json({
			success: true,
			interaction,
		});
	} catch (error) {
		console.error("Error in POST /api/webinar-replay-data:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

