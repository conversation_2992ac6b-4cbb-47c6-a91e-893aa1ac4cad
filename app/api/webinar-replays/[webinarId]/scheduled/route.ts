import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-replays/[webinarId]/scheduled
 * Get scheduled replays for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;

		const { data: scheduledReplays, error } = await supabaseServer
			.from("webinar_replays")
			.select(`
				*,
				webinar_recordings (*)
			`)
			.eq("webinar_id", webinarId)
			.eq("replay_mode", "scheduled")
			.eq("is_active", true)
			.order("scheduled_start_time", { ascending: true });

		if (error) {
			console.error("Error fetching scheduled replays:", error);
			return NextResponse.json(
				{ error: "Failed to fetch scheduled replays" },
				{ status: 500 },
			);
		}

		// Filter out expired replays
		const now = new Date();
		const validReplays = (scheduledReplays || []).filter((replay) => {
			if (!replay.is_active) return false;
			if (replay.expires_at && new Date(replay.expires_at) < now) {
				return false;
			}
			return true;
		});

		return NextResponse.json({
			replays: validReplays,
		});
	} catch (error) {
		console.error("Error in GET /api/webinar-replays/scheduled:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-replays/[webinarId]/scheduled
 * Schedule a replay session
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();

		const {
			recordingId,
			scheduledStartTime,
			expiresAt,
			accessSettings,
		} = body;

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to schedule replays" },
				{ status: 403 },
			);
		}

		if (!recordingId || !scheduledStartTime) {
			return NextResponse.json(
				{ error: "recordingId and scheduledStartTime are required" },
				{ status: 400 },
			);
		}

		const { data: replay, error } = await supabaseServer
			.from("webinar_replays")
			.insert({
				webinar_id: webinarId,
				recording_id: recordingId,
				replay_mode: "scheduled",
				scheduled_start_time: new Date(scheduledStartTime).toISOString(),
				expires_at: expiresAt ? new Date(expiresAt).toISOString() : null,
				access_settings: accessSettings || {},
				is_active: true,
			})
			.select()
			.single();

		if (error) {
			console.error("Error scheduling replay:", error);
			return NextResponse.json(
				{ error: "Failed to schedule replay" },
				{ status: 500 },
			);
		}

		return NextResponse.json({
			success: true,
			replay,
		});
	} catch (error) {
		console.error("Error in POST /api/webinar-replays/scheduled:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

