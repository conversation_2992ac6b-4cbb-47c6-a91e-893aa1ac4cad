import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-videos/[webinarId]
 * Get videos for a webinar
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;

		const { data: videos, error } = await supabaseServer
			.from("webinar_videos")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("created_at", { ascending: false });

		if (error) {
			console.error("Error fetching videos:", error);
			return NextResponse.json(
				{ error: "Failed to fetch videos" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ videos: videos || [] });
	} catch (error) {
		console.error("Error in GET /api/webinar-videos:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-videos/[webinarId]
 * Create or control video playback
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { 
			action, 
			videoId, 
			videoUrl, 
			videoTitle, 
			videoThumbnailUrl, 
			durationSeconds,
			currentTime, // This is the playback position in seconds
			isPlaying 
		} = body;

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		// If action is "play", start playing a video
		if (action === "play" && videoId) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to play videos" },
					{ status: 403 },
				);
			}

			// Stop all other videos
			await supabaseServer
				.from("webinar_videos")
				.update({ is_playing: false })
				.eq("webinar_id", webinarId)
				.eq("is_playing", true);

			const { data: video, error } = await supabaseServer
				.from("webinar_videos")
				.update({
					is_playing: true,
					playback_position: currentTime || 0,
					played_at: new Date().toISOString(),
				})
				.eq("id", videoId)
				.eq("webinar_id", webinarId)
				.select()
				.single();

			if (error) {
				console.error("Error playing video:", error);
				return NextResponse.json(
					{ error: "Failed to play video" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ video });
		}

		// If action is "pause", pause a video
		if (action === "pause" && videoId) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to pause videos" },
					{ status: 403 },
				);
			}

			const { data: video, error } = await supabaseServer
				.from("webinar_videos")
				.update({
					is_playing: false,
					playback_position: currentTime || 0,
				})
				.eq("id", videoId)
				.eq("webinar_id", webinarId)
				.select()
				.single();

			if (error) {
				console.error("Error pausing video:", error);
				return NextResponse.json(
					{ error: "Failed to pause video" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ video });
		}

		// If action is "updateTime", update video playback time
		if (action === "updateTime" && videoId && currentTime !== undefined) {
			const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
			if (!hasModeratorAccess) {
				return NextResponse.json(
					{ error: "You do not have permission to control videos" },
					{ status: 403 },
				);
			}

			const { error } = await supabaseServer
				.from("webinar_videos")
				.update({
					playback_position: currentTime,
					is_playing: isPlaying !== undefined ? isPlaying : true,
				})
				.eq("id", videoId)
				.eq("webinar_id", webinarId);

			if (error) {
				console.error("Error updating video time:", error);
				return NextResponse.json(
					{ error: "Failed to update video" },
					{ status: 500 },
				);
			}

			return NextResponse.json({ success: true });
		}

		// Otherwise, create a new video
		if (!videoUrl || !videoTitle) {
			return NextResponse.json(
				{ error: "videoUrl and videoTitle are required" },
				{ status: 400 },
			);
		}

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to add videos" },
				{ status: 403 },
			);
		}

		const { data: video, error } = await supabaseServer
			.from("webinar_videos")
			.insert({
				webinar_id: webinarId,
				uploaded_by: userId,
				video_url: videoUrl,
				video_title: videoTitle,
				video_thumbnail_url: videoThumbnailUrl || null,
				duration_seconds: durationSeconds || null,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating video:", error);
			return NextResponse.json(
				{ error: "Failed to create video" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ video });
	} catch (error) {
		console.error("Error in POST /api/webinar-videos:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/webinar-videos/[webinarId]?videoId=...
 * Delete a video
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const videoId = searchParams.get("videoId");

		if (!videoId) {
			return NextResponse.json(
				{ error: "videoId query parameter is required" },
				{ status: 400 },
			);
		}

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const hasModeratorAccess = await checkModeratorAccess(userId, webinarId);
		if (!hasModeratorAccess) {
			return NextResponse.json(
				{ error: "You do not have permission to delete videos" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer
			.from("webinar_videos")
			.delete()
			.eq("id", videoId)
			.eq("webinar_id", webinarId);

		if (error) {
			console.error("Error deleting video:", error);
			return NextResponse.json(
				{ error: "Failed to delete video" },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error in DELETE /api/webinar-videos:", error);
		return NextResponse.json(
			{ error: error instanceof Error ? error.message : "Internal server error" },
			{ status: 500 },
		);
	}
}

