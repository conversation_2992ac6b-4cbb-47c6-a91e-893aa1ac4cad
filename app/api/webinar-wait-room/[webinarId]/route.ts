import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

/**
 * Check if user is host/moderator/admin for a webinar
 */
async function checkModeratorAccess(
	userId: string,
	webinarId: string,
): Promise<boolean> {
	try {
		const { data: webinar, error } = await supabaseServer
			.from("webinars")
			.select("host_ids, presenter_roles, experience_id, created_by")
			.eq("id", webinarId)
			.single();

		if (error || !webinar) {
			return false;
		}

		if (webinar.host_ids && Array.isArray(webinar.host_ids) && webinar.host_ids.includes(userId)) {
			return true;
		}

		const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
		const experience = await whopsdk.experiences.retrieve(webinar.experience_id);
		const experienceAny = experience as any;
		const user = await whopsdk.users.retrieve(userId);

		const accessObj = access as any;
		return (
			experienceAny.created_by === userId ||
			(!!experienceAny.company_id && experienceAny.company_id === user.id) ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage") ||
			webinar.created_by === userId
		);
	} catch (error) {
		console.error("Error checking moderator access:", error);
		return false;
	}
}

/**
 * GET /api/webinar-wait-room/[webinarId]
 * Get wait room status and queue
 */
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		const isModerator = await checkModeratorAccess(userId, webinarId);

		// Get webinar to check if it's started
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("start_time, status")
			.eq("id", webinarId)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const now = new Date();
		const startTime = new Date(webinar.start_time);
		const isStarted = webinar.status === "live" || now >= startTime;

		// Get wait room entries
		let query = supabaseServer
			.from("webinar_wait_room")
			.select("*")
			.eq("webinar_id", webinarId)
			.order("joined_at", { ascending: true });

		// Non-moderators can only see their own entry
		if (!isModerator) {
			query = query.eq("user_id", userId);
		}

		const { data: waitRoomEntries, error } = await query;

		if (error) {
			console.error("Error fetching wait room:", error);
			return NextResponse.json(
				{ error: "Failed to fetch wait room" },
				{ status: 500 },
			);
		}

		// Calculate queue positions
		const entries = (waitRoomEntries || []).map((entry, index) => ({
			...entry,
			queue_position: index + 1,
		}));

		// Update queue positions in database
		if (isModerator && entries.length > 0) {
			for (const entry of entries) {
				await supabaseServer
					.from("webinar_wait_room")
					.update({ queue_position: entry.queue_position })
					.eq("id", entry.id);
			}
		}

		const userEntry = entries.find((e) => e.user_id === userId);

		return NextResponse.json({
			isStarted,
			waitRoomMode: !isStarted || webinar.status === "scheduled",
			userEntry,
			queue: isModerator ? entries : undefined,
			totalWaiting: entries.length,
		});
	} catch (error) {
		console.error("Error in GET /api/webinar-wait-room:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * POST /api/webinar-wait-room/[webinarId]
 * Join wait room or manage wait room (admit/deny)
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { webinarId } = await params;
		const body = await request.json();
		const { action, userId: targetUserId, dailySessionId } = body;

		const userToken = await whopsdk.verifyUserToken(request.headers);
		const userId = userToken.userId;

		if (action === "join") {
			// User joining wait room
			// Get user email from registration submission
			const { data: submission } = await supabaseServer
				.from("registration_submissions")
				.select("email, name")
				.eq("webinar_id", webinarId)
				.eq("user_id", userId)
				.order("created_at", { ascending: false })
				.limit(1)
				.single();

			if (!submission) {
				return NextResponse.json(
					{ error: "Registration not found" },
					{ status: 404 },
				);
			}

			// Check if already in wait room
			const { data: existing } = await supabaseServer
				.from("webinar_wait_room")
				.select("id")
				.eq("webinar_id", webinarId)
				.eq("user_id", userId)
				.single();

			if (existing) {
				return NextResponse.json({
					success: true,
					waitRoomEntry: existing,
				});
			}

			// Add to wait room
			const { data: waitRoomEntry, error } = await supabaseServer
				.from("webinar_wait_room")
				.insert({
					webinar_id: webinarId,
					user_id: userId,
					user_email: submission.email,
					user_name: submission.name,
					daily_session_id: dailySessionId || null,
				})
				.select()
				.single();

			if (error) {
				console.error("Error joining wait room:", error);
				return NextResponse.json(
					{ error: "Failed to join wait room" },
					{ status: 500 },
				);
			}

			return NextResponse.json({
				success: true,
				waitRoomEntry,
			});
		}

		// Moderator actions
		if (action === "admit" || action === "deny") {
			const isModerator = await checkModeratorAccess(userId, webinarId);
			if (!isModerator) {
				return NextResponse.json(
					{ error: "You do not have permission to manage wait room" },
					{ status: 403 },
				);
			}

			if (!targetUserId) {
				return NextResponse.json(
					{ error: "targetUserId is required" },
					{ status: 400 },
				);
			}

			const { data: updated, error } = await supabaseServer
				.from("webinar_wait_room")
				.update({
					status: action === "admit" ? "admitted" : "denied",
					admitted_at: action === "admit" ? new Date().toISOString() : null,
				})
				.eq("webinar_id", webinarId)
				.eq("user_id", targetUserId)
				.select()
				.single();

			if (error) {
				console.error("Error updating wait room:", error);
				return NextResponse.json(
					{ error: "Failed to update wait room" },
					{ status: 500 },
				);
			}

			return NextResponse.json({
				success: true,
				waitRoomEntry: updated,
			});
		}

		return NextResponse.json(
			{ error: "Invalid action" },
			{ status: 400 },
		);
	} catch (error) {
		console.error("Error in POST /api/webinar-wait-room:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

