import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { webinarId } = await params;

		const { data, error } = await supabaseServer
			.from("webinars")
			.select("*")
			.eq("id", webinarId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "Webinar not found" }, { status: 404 });
			}
			console.error("Supabase error fetching webinar:", error);
			return NextResponse.json(
				{ error: "Failed to fetch webinar", details: error.message },
				{ status: 500 },
			);
		}

		// Update status based on current time
		const now = new Date();
		const scheduledAt = new Date(data.scheduled_at);
		const endTime = new Date(
			scheduledAt.getTime() + data.duration_minutes * 60 * 1000,
		);

		let status = data.status;
		if (status === "scheduled" && now >= scheduledAt && now <= endTime) {
			status = "live";
		} else if (status === "live" && now > endTime) {
			status = "completed";
		}

		// Convert price_amount from cents to dollars for display
		const displayPrice = data.price_amount && typeof data.price_amount === 'number' && data.price_amount > 0
			? (data.price_amount / 100).toFixed(2)
			: null;

		return NextResponse.json({ 
			webinar: { 
				...data, 
				status,
				price_amount: displayPrice ? parseFloat(displayPrice) : (data.price_amount || 0),
			} 
		});
	} catch (error) {
		console.error("Error fetching webinar:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { webinarId } = await params;
		const body = await request.json();

		// Fetch existing webinar to check permissions
		const { data: existingWebinar, error: fetchError } = await supabaseServer
			.from("webinars")
			.select("*")
			.eq("id", webinarId)
			.single();

		if (fetchError || !existingWebinar) {
			return NextResponse.json({ error: "Webinar not found" }, { status: 404 });
		}

		// Verify user has access to the experience (not company)
		const access = await whopsdk.users.checkAccess(existingWebinar.experience_id, {
			id: userId,
		});
		if (!access.has_access) {
			return NextResponse.json(
				{ error: "You do not have permission to update this webinar" },
				{ status: 403 },
			);
		}

		// Prepare update data
		const updateData: Record<string, unknown> = {};
		if (body.title !== undefined) updateData.title = body.title;
		if (body.description !== undefined) updateData.description = body.description;
		if (body.scheduled_at !== undefined)
			updateData.scheduled_at = new Date(body.scheduled_at).toISOString();
		if (body.duration_minutes !== undefined)
			updateData.duration_minutes = body.duration_minutes;
		if (body.host_ids !== undefined) updateData.host_ids = body.host_ids;
		if (body.status !== undefined) updateData.status = body.status;
		if (body.event_type !== undefined) updateData.event_type = body.event_type;
		if (body.recurrence_pattern !== undefined) updateData.recurrence_pattern = body.recurrence_pattern;
		if (body.recurrence_interval !== undefined) updateData.recurrence_interval = body.recurrence_interval;
		if (body.recurrence_end_date !== undefined)
			updateData.recurrence_end_date = body.recurrence_end_date 
				? new Date(body.recurrence_end_date).toISOString() 
				: null;
		if (body.recurrence_count !== undefined) updateData.recurrence_count = body.recurrence_count;
		if (body.room_name !== undefined) updateData.room_name = body.room_name;
		if (body.is_evergreen !== undefined) updateData.is_evergreen = body.is_evergreen;
		if (body.timezone !== undefined) updateData.timezone = body.timezone;
		if (body.password !== undefined) updateData.password = body.password;
		if (body.is_password_protected !== undefined) updateData.is_password_protected = body.is_password_protected;
		if (body.price_amount !== undefined) updateData.price_amount = typeof body.price_amount === 'number' ? Math.round(body.price_amount * 100) : body.price_amount; // Store in cents
		if (body.price_currency !== undefined) updateData.price_currency = body.price_currency;
		if (body.is_paid !== undefined) updateData.is_paid = body.is_paid;
		if (body.custom_branding_logo_url !== undefined) updateData.custom_branding_logo_url = body.custom_branding_logo_url;
		if (body.custom_branding_background_color !== undefined) updateData.custom_branding_background_color = body.custom_branding_background_color;
		if (body.custom_branding_text_color !== undefined) updateData.custom_branding_text_color = body.custom_branding_text_color;
		if (body.custom_branding_accent_color !== undefined) updateData.custom_branding_accent_color = body.custom_branding_accent_color;
		if (body.presenter_roles !== undefined) updateData.presenter_roles = body.presenter_roles;

		const { data, error } = await supabaseServer
			.from("webinars")
			.update(updateData)
			.eq("id", webinarId)
			.select()
			.single();

		if (error) {
			console.error("Supabase error updating webinar:", error);
			return NextResponse.json(
				{ error: "Failed to update webinar", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ webinar: data });
	} catch (error) {
		console.error("Error updating webinar:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ webinarId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { webinarId } = await params;

		// Fetch existing webinar to check permissions
		const { data: existingWebinar, error: fetchError } = await supabaseServer
			.from("webinars")
			.select("*")
			.eq("id", webinarId)
			.single();

		if (fetchError || !existingWebinar) {
			return NextResponse.json({ error: "Webinar not found" }, { status: 404 });
		}

		// Verify user has access to the experience (not company)
		const access = await whopsdk.users.checkAccess(existingWebinar.experience_id, {
			id: userId,
		});
		if (!access.has_access) {
			return NextResponse.json(
				{ error: "You do not have permission to delete this webinar" },
				{ status: 403 },
			);
		}

		const { error } = await supabaseServer.from("webinars").delete().eq("id", webinarId);

		if (error) {
			console.error("Supabase error deleting webinar:", error);
			return NextResponse.json(
				{ error: "Failed to delete webinar", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error deleting webinar:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

