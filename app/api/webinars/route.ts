import { NextRequest, NextResponse } from "next/server";
import { supabaseServer } from "@/lib/supabase";
import { whopsdk } from "@/lib/whop-sdk";
import type { Webinar } from "@/lib/supabase";

export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const experienceId = searchParams.get("experienceId");
		const companyId = searchParams.get("companyId");

		if (!experienceId && !companyId) {
			return NextResponse.json(
				{ error: "Missing experienceId or companyId parameter" },
				{ status: 400 },
			);
		}

		let query = supabaseServer.from("webinars").select("*");

		if (experienceId) {
			query = query.eq("experience_id", experienceId);
		}

		if (companyId) {
			query = query.eq("company_id", companyId);
		}

		// Order by scheduled_at ascending (upcoming first)
		query = query.order("scheduled_at", { ascending: true });

		const { data, error } = await query;

		if (error) {
			console.error("Supabase error fetching webinars:", error);
			return NextResponse.json(
				{ error: "Failed to fetch webinars", details: error.message },
				{ status: 500 },
			);
		}

		// Update status based on current time and event type
		const now = new Date();
		const updatedData = (data || []).map((webinar: Webinar) => {
			let status = webinar.status;

			// Handle different event types
			if (webinar.event_type === "always_on" || webinar.event_type === "evergreen_room") {
				// Always on and evergreen rooms are always available
				status = "live";
			} else if (webinar.event_type === "right_now") {
				// Right now events are live when created
				status = "live";
			} else {
				// For live and recurring events, check time-based status
				const scheduledAt = new Date(webinar.scheduled_at);
				const endTime = new Date(
					scheduledAt.getTime() + webinar.duration_minutes * 60 * 1000,
				);

				// Auto-update status based on time
				if (status === "scheduled" && now >= scheduledAt && now <= endTime) {
					status = "live";
				} else if (status === "live" && now > endTime) {
					status = "completed";
				}
			}

			// Convert price_amount from cents to dollars for display
			// Note: price_amount is stored in cents in the database
			const displayPrice = webinar.price_amount && typeof webinar.price_amount === 'number' && webinar.price_amount > 0
				? (webinar.price_amount / 100).toFixed(2) 
				: null;

			return { 
				...webinar, 
				status,
				price_amount: displayPrice ? parseFloat(displayPrice) : (webinar.price_amount || 0),
			};
		});

		return NextResponse.json({ webinars: updatedData });
	} catch (error) {
		console.error("Error fetching webinars:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const body = await request.json();

		const {
			experience_id,
			company_id,
			title,
			description,
			scheduled_at,
			duration_minutes,
			host_ids,
			event_type,
			recurrence_pattern,
			recurrence_interval,
			recurrence_end_date,
			recurrence_count,
			room_name,
			timezone,
			password,
			is_password_protected,
			price_amount,
			price_currency,
			is_paid,
			custom_branding_logo_url,
			custom_branding_background_color,
			custom_branding_text_color,
			custom_branding_accent_color,
			presenter_roles,
		} = body;

		// Validate required fields
		if (!experience_id || !title) {
			return NextResponse.json(
				{ error: "Missing required fields: experience_id, title" },
				{ status: 400 },
			);
		}

		// Validate event_type
		const validEventTypes = ["live", "right_now", "recurring", "always_on", "evergreen_room"];
		const finalEventType = event_type || "live";
		if (!validEventTypes.includes(finalEventType)) {
			return NextResponse.json(
				{ error: `Invalid event_type. Must be one of: ${validEventTypes.join(", ")}` },
				{ status: 400 },
			);
		}

		// Validate scheduled_at based on event_type
		let finalScheduledAt: string;
		if (finalEventType === "right_now" || finalEventType === "always_on" || finalEventType === "evergreen_room") {
			finalScheduledAt = scheduled_at || new Date().toISOString();
		} else if (!scheduled_at) {
			return NextResponse.json(
				{ error: "Missing required field: scheduled_at" },
				{ status: 400 },
			);
		} else {
			finalScheduledAt = scheduled_at;
		}

		// Validate duration based on event_type
		let finalDuration = duration_minutes;
		if (finalEventType === "always_on" || finalEventType === "evergreen_room") {
			finalDuration = 0; // No duration for always on/evergreen
		} else if (!finalDuration) {
			finalDuration = 60; // Default to 60 minutes
		}

		// Validate recurrence fields if recurring
		if (finalEventType === "recurring") {
			if (!recurrence_pattern) {
				return NextResponse.json(
					{ error: "Missing required field: recurrence_pattern for recurring events" },
					{ status: 400 },
				);
			}
			const validPatterns = ["daily", "weekly", "monthly"];
			if (!validPatterns.includes(recurrence_pattern)) {
				return NextResponse.json(
					{ error: `Invalid recurrence_pattern. Must be one of: ${validPatterns.join(", ")}` },
					{ status: 400 },
				);
			}
		}

		// Verify user has access to the experience
		const access = await whopsdk.users.checkAccess(experience_id, { id: userId });
		if (!access.has_access) {
			return NextResponse.json(
				{ error: "You do not have access to this experience" },
				{ status: 403 },
			);
		}

		// Get company_id from experience if not provided
		let finalCompanyId = company_id;
		if (!finalCompanyId) {
			const experience = await whopsdk.experiences.retrieve(experience_id);
		const experienceAny = experience as any;
			// Access company_id with type assertion since it may not be in TypeScript types
			finalCompanyId = experienceAny.company_id || "";
		}

		const webinarData: any = {
			experience_id,
			company_id: finalCompanyId,
			title,
			description: description || null,
			scheduled_at: new Date(finalScheduledAt).toISOString(),
			duration_minutes: finalDuration,
			host_ids: host_ids || [],
			created_by: userId,
			event_type: finalEventType,
			status: finalEventType === "right_now" || finalEventType === "always_on" || finalEventType === "evergreen_room" 
				? "live" as const 
				: "scheduled" as const,
			timezone: timezone || "UTC",
			is_password_protected: is_password_protected || false,
			password: (is_password_protected && password) ? password : null,
			is_paid: is_paid || false,
			price_amount: (is_paid && price_amount) ? Math.round(price_amount * 100) : 0, // Store in cents
			price_currency: (is_paid && price_currency) ? price_currency : "USD",
			custom_branding_logo_url: custom_branding_logo_url || null,
			custom_branding_background_color: custom_branding_background_color || null,
			custom_branding_text_color: custom_branding_text_color || null,
			custom_branding_accent_color: custom_branding_accent_color || null,
			presenter_roles: presenter_roles || {},
		};

		// Add recurrence fields if recurring
		if (finalEventType === "recurring") {
			webinarData.recurrence_pattern = recurrence_pattern;
			webinarData.recurrence_interval = recurrence_interval || 1;
			webinarData.recurrence_end_date = recurrence_end_date 
				? new Date(recurrence_end_date).toISOString() 
				: null;
			webinarData.recurrence_count = recurrence_count || null;
		}

		// Add room_name for evergreen rooms
		if (finalEventType === "evergreen_room") {
			webinarData.room_name = room_name || `evergreen-${experience_id}-${Date.now()}`;
			webinarData.is_evergreen = true;
		}

		// Set is_evergreen for always_on
		if (finalEventType === "always_on") {
			webinarData.is_evergreen = true;
		}

		const { data, error } = await supabaseServer
			.from("webinars")
			.insert([webinarData])
			.select()
			.single();

		if (error) {
			console.error("Supabase error creating webinar:", error);
			return NextResponse.json(
				{ error: "Failed to create webinar", details: error.message },
				{ status: 500 },
			);
		}

		return NextResponse.json({ webinar: data }, { status: 201 });
	} catch (error) {
		console.error("Error creating webinar:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

