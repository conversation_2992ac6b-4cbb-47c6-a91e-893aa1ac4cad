import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import { linkProductToWebinar, unlinkProductFromWebinar } from "@/lib/whop-integration";

/**
 * Check if user is admin for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	try {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	} catch (error) {
		console.error("Error checking admin access:", error);
		return false;
	}
}

/**
 * POST /api/whop-integration/products/[productId]/link
 * Link product to webinar
 */
export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ productId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { productId } = await params;
		const body = await request.json();
		const { webinarId, isRequiredForAccess, whopCompanyId } = body;

		if (!webinarId || !whopCompanyId) {
			return NextResponse.json(
				{ error: "Missing webinarId or whopCompanyId" },
				{ status: 400 },
			);
		}

		// Check admin access
		const { supabaseServer } = await import("@/lib/supabase");
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("company_id")
			.eq("id", webinarId)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const hasAccess = await checkAdminAccess(userId, webinar.company_id);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const productLink = await linkProductToWebinar({
			webinarId,
			whopProductId: productId,
			whopCompanyId,
			isRequiredForAccess: isRequiredForAccess ?? true,
			createdBy: userId,
		});

		return NextResponse.json({ product: productLink });
	} catch (error) {
		console.error("Error linking product:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/whop-integration/products/[productId]/link
 * Unlink product from webinar
 */
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ productId: string }> },
) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const { productId } = await params;
		const searchParams = request.nextUrl.searchParams;
		const webinarId = searchParams.get("webinarId");

		if (!webinarId) {
			return NextResponse.json(
				{ error: "Missing webinarId parameter" },
				{ status: 400 },
			);
		}

		// Check admin access
		const { supabaseServer } = await import("@/lib/supabase");
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("company_id")
			.eq("id", webinarId)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const hasAccess = await checkAdminAccess(userId, webinar.company_id);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		await unlinkProductFromWebinar(webinarId, productId);

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error("Error unlinking product:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

