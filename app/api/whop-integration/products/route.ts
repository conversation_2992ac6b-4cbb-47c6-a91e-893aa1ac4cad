import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import {
	getWhopProducts,
	linkProductToWebinar,
	createCheckoutSession,
	getLinkedProducts,
	getWhopTransactions,
	unlinkProductFromWebinar,
} from "@/lib/whop-integration";

/**
 * Check if user is admin for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	try {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	} catch (error) {
		console.error("Error checking admin access:", error);
		return false;
	}
}

/**
 * GET /api/whop-integration/products
 * List Whop products for company
 */
export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const companyId = searchParams.get("companyId");

		if (!companyId) {
			return NextResponse.json(
				{ error: "Missing companyId parameter" },
				{ status: 400 },
			);
		}

		// Check admin access
		const hasAccess = await checkAdminAccess(userId, companyId);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const products = await getWhopProducts(companyId);

		return NextResponse.json({ products });
	} catch (error) {
		console.error("Error fetching Whop products:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

/**
 * POST /api/whop-integration/checkout
 * Create checkout session
 */
export async function POST(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const body = await request.json();
		const { webinarId, productId } = body;

		if (!webinarId || !productId) {
			return NextResponse.json(
				{ error: "Missing webinarId or productId" },
				{ status: 400 },
			);
		}

		const checkout = await createCheckoutSession({
			webinarId,
			productId,
			userId,
		});

		return NextResponse.json(checkout);
	} catch (error) {
		console.error("Error creating checkout session:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

