import { NextRequest, NextResponse } from "next/server";
import { whopsdk } from "@/lib/whop-sdk";
import { getWhopTransactions } from "@/lib/whop-integration";

/**
 * Check if user is admin for a company
 */
async function checkAdminAccess(
	userId: string,
	companyId: string,
): Promise<boolean> {
	try {
		const access = await whopsdk.users.checkAccess(companyId, { id: userId });
		const user = await whopsdk.users.retrieve(userId);
		const company = await whopsdk.companies.retrieve(companyId);
		const companyAny = company as any;

		const accessObj = access as any;
		return (
			companyAny.created_by === userId ||
			company.id === user.id ||
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage")
		);
	} catch (error) {
		console.error("Error checking admin access:", error);
		return false;
	}
}

/**
 * GET /api/whop-integration/transactions
 * Get transactions for webinar
 */
export async function GET(request: NextRequest) {
	try {
		const { userId } = await whopsdk.verifyUserToken(request.headers);
		const searchParams = request.nextUrl.searchParams;
		const webinarId = searchParams.get("webinarId");
		const status = searchParams.get("status");

		if (!webinarId) {
			return NextResponse.json(
				{ error: "Missing webinarId parameter" },
				{ status: 400 },
			);
		}

		// Check admin access
		const { supabaseServer } = await import("@/lib/supabase");
		const { data: webinar } = await supabaseServer
			.from("webinars")
			.select("company_id")
			.eq("id", webinarId)
			.single();

		if (!webinar) {
			return NextResponse.json(
				{ error: "Webinar not found" },
				{ status: 404 },
			);
		}

		const hasAccess = await checkAdminAccess(userId, webinar.company_id);
		if (!hasAccess) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
		}

		const transactions = await getWhopTransactions(
			webinarId,
			status as any,
		);

		return NextResponse.json({ transactions });
	} catch (error) {
		console.error("Error fetching transactions:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "Internal server error",
			},
			{ status: 500 },
		);
	}
}

