"use client";

import { But<PERSON> } from "@whop/react/components";
import { Calendar, Download } from "lucide-react";
import { generateICSFile, downloadICSFile, getCalendarUrls } from "@/lib/calendar";
import type { Webinar } from "@/lib/supabase";

interface CalendarDownloadButtonProps {
	webinar: Webinar;
	registrationData: {
		name: string;
		email: string;
		phone?: string;
	};
	variant?: "button" | "icon";
	className?: string;
}

export function CalendarDownloadButton({
	webinar,
	registrationData,
	variant = "button",
	className = "",
}: CalendarDownloadButtonProps) {
	const handleDownloadICS = () => {
		const icsContent = generateICSFile(webinar, registrationData);
		const filename = `${webinar.title.replace(/[^a-z0-9]/gi, "_")}_${new Date().toISOString().split("T")[0]}.ics`;
		downloadICSFile(icsContent, filename);
	};

	const calendarUrls = getCalendarUrls(webinar, registrationData);

	if (variant === "icon") {
		return (
			<div className={`flex flex-col gap-2 sm:flex-row ${className}`}>
				<button
					type="button"
					onClick={handleDownloadICS}
					className="flex items-center gap-2 px-4 py-2 text-sm text-gray-11 hover:text-gray-12 transition-colors"
					aria-label="Download calendar file"
				>
					<Download className="w-4 h-4" />
					<span>Download .ics</span>
				</button>
				<a
					href={calendarUrls.google}
					target="_blank"
					rel="noopener noreferrer"
					className="flex items-center gap-2 px-4 py-2 text-sm text-gray-11 hover:text-gray-12 transition-colors"
				>
					<Calendar className="w-4 h-4" />
					<span>Add to Google</span>
				</a>
				<a
					href={calendarUrls.outlook}
					target="_blank"
					rel="noopener noreferrer"
					className="flex items-center gap-2 px-4 py-2 text-sm text-gray-11 hover:text-gray-12 transition-colors"
				>
					<Calendar className="w-4 h-4" />
					<span>Add to Outlook</span>
				</a>
			</div>
		);
	}

	return (
		<div className={`flex flex-col gap-3 ${className}`}>
			<Button
				type="button"
				onClick={handleDownloadICS}
				variant="ghost"
				className="w-full flex items-center justify-center gap-2"
			>
				<Download className="w-4 h-4" />
				Download Calendar File (.ics)
			</Button>
			<div className="flex flex-col gap-2 sm:flex-row">
				<a
					href={calendarUrls.google}
					target="_blank"
					rel="noopener noreferrer"
					className="flex-1"
				>
					<Button
						type="button"
						variant="ghost"
						className="w-full flex items-center justify-center gap-2"
					>
						<Calendar className="w-4 h-4" />
						Google Calendar
					</Button>
				</a>
				<a
					href={calendarUrls.outlook}
					target="_blank"
					rel="noopener noreferrer"
					className="flex-1"
				>
					<Button
						type="button"
						variant="ghost"
						className="w-full flex items-center justify-center gap-2"
					>
						<Calendar className="w-4 h-4" />
						Outlook
					</Button>
				</a>
			</div>
		</div>
	);
}

