"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, TextField, Card, Select } from "@whop/react/components";
import { X, Loader2 } from "lucide-react";
import { useCreateWebinar, useUpdateWebinar } from "@/lib/queries/webinars";
import { useExperiences } from "@/lib/queries/experiences";
import type { Webinar } from "@/lib/supabase";

interface CreateWebinarFormProps {
	companyId: string;
	experienceId?: string | null;
	initialData?: Webinar;
	onClose: () => void;
}

interface Experience {
	id: string;
	name: string;
}

export function CreateWebinarForm({
	companyId,
	experienceId,
	initialData,
	onClose,
}: CreateWebinarFormProps) {
	const createMutation = useCreateWebinar();
	const updateMutation = useUpdateWebinar();
	const { data: experiences = [], isLoading: loadingExperiences } = useExperiences(companyId, experienceId || undefined);
	const isLoading = createMutation.isPending || updateMutation.isPending;

	const [formData, setFormData] = useState({
		title: initialData?.title || "",
		description: initialData?.description || "",
		scheduled_at: initialData
			? new Date(initialData.scheduled_at).toISOString().slice(0, 16)
			: "",
		duration_minutes: initialData?.duration_minutes || 60,
		experience_id: initialData?.experience_id || experienceId || (experiences.length === 1 ? experiences[0].id : ""),
		host_ids: initialData?.host_ids || [],
	});

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		try {
			const submitData = {
				...formData,
				company_id: companyId,
				experience_id: formData.experience_id,
				scheduled_at: new Date(formData.scheduled_at).toISOString(),
			};

			if (initialData) {
				await updateMutation.mutateAsync({ id: initialData.id, ...submitData });
			} else {
				await createMutation.mutateAsync(submitData);
			}

			onClose();
		} catch (error) {
			console.error("Error saving webinar:", error);
			alert(error instanceof Error ? error.message : "Failed to save webinar");
		}
	};

	return (
		<Card className="p-4 sm:p-6 md:p-8 max-w-2xl mx-auto">
			<div className="flex items-center justify-between mb-4 sm:mb-6">
				<Heading size="5" weight="bold" className="sm:text-6">
					{initialData ? "Edit Webinar" : "Schedule New Webinar"}
				</Heading>
				<Button variant="ghost" size="2" onClick={onClose} className="min-h-[44px] min-w-[44px]">
					<X className="h-5 w-5" />
				</Button>
			</div>

			<form onSubmit={handleSubmit} className="flex flex-col gap-4 sm:gap-6">
				<div className="flex flex-col gap-2">
					<Text size="2" weight="semi-bold">
						Title *
					</Text>
					<TextField.Root>
						<TextField.Input
							value={formData.title}
							onChange={(e) => setFormData({ ...formData, title: e.target.value })}
							placeholder="Enter webinar title"
							required
							className="min-h-[44px]"
						/>
					</TextField.Root>
				</div>

				<div className="flex flex-col gap-2">
					<Text size="2" weight="semi-bold">
						Description
					</Text>
					<TextField.Root>
						<TextField.Input
							value={formData.description}
							onChange={(e) =>
								setFormData({ ...formData, description: e.target.value })
							}
							placeholder="Enter webinar description (optional)"
						/>
					</TextField.Root>
				</div>

				<div className="flex flex-col gap-2">
					<Text size="2" weight="semi-bold">
						Experience *
					</Text>
					{loadingExperiences ? (
						<div className="flex items-center gap-2 py-2">
							<Loader2 className="h-4 w-4 animate-spin text-gray-11" />
							<Text size="2" color="gray">
								Loading experiences...
							</Text>
						</div>
					) : experiences.length === 0 ? (
						<div className="flex flex-col gap-2">
							<TextField.Root>
								<TextField.Input
									value={formData.experience_id}
									onChange={(e) =>
										setFormData({ ...formData, experience_id: e.target.value })
									}
									placeholder="Enter Whop experience ID"
									required
								/>
							</TextField.Root>
							<Text size="1" color="gray">
								No experiences found. Enter the experience ID manually.
							</Text>
						</div>
					) : (
						<Select.Root
							value={formData.experience_id}
							onValueChange={(value) =>
								setFormData({ ...formData, experience_id: value })
							}
							required
						>
							<Select.Trigger className="min-h-[44px]" placeholder="Select an experience">
							</Select.Trigger>
							<Select.Content>
								{experiences.map((exp) => (
									<Select.Item key={exp.id} value={exp.id}>
										{exp.name}
									</Select.Item>
								))}
							</Select.Content>
						</Select.Root>
					)}
					{experienceId && (
						<Text size="1" color="blue">
							Automatically set from current experience context
						</Text>
					)}
					<Text size="1" color="gray">
						The Whop experience where this webinar will be hosted
					</Text>
				</div>

				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<div className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							Scheduled Date & Time *
						</Text>
						<TextField.Root>
							<TextField.Input
								type="datetime-local"
								value={formData.scheduled_at}
								onChange={(e) =>
									setFormData({ ...formData, scheduled_at: e.target.value })
								}
								required
								className="min-h-[44px]"
							/>
						</TextField.Root>
					</div>

					<div className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							Duration (minutes) *
						</Text>
						<TextField.Root>
							<TextField.Input
								type="number"
								value={formData.duration_minutes}
								onChange={(e) =>
									setFormData({
										...formData,
										duration_minutes: parseInt(e.target.value) || 60,
									})
								}
								min="1"
								required
								className="min-h-[44px]"
							/>
						</TextField.Root>
					</div>
				</div>

				<div className="flex flex-col gap-2">
					<Text size="2" weight="semi-bold">
						Host IDs (comma-separated)
					</Text>
					<TextField.Root>
						<TextField.Input
							value={formData.host_ids.join(", ")}
							onChange={(e) =>
								setFormData({
									...formData,
									host_ids: e.target.value
										.split(",")
										.map((id) => id.trim())
										.filter(Boolean),
								})
							}
							placeholder="Enter user IDs separated by commas"
						/>
					</TextField.Root>
					<Text size="1" color="gray">
						Whop user IDs of hosts/presenters (optional)
					</Text>
				</div>

				<div className="flex flex-col sm:flex-row gap-3 justify-end pt-4 border-t border-gray-a6">
					<Button variant="surface" onClick={onClose} disabled={isLoading} className="min-h-[44px] w-full sm:w-auto">
						Cancel
					</Button>
					<Button
						type="submit"
						variant="solid"
						color="blue"
						disabled={isLoading || !formData.title || !formData.scheduled_at || !formData.experience_id}
						className="min-h-[44px] w-full sm:w-auto"
					>
						{isLoading ? (
							<>
								<Loader2 className="h-4 w-4 animate-spin mr-2" />
								Saving...
							</>
						) : initialData ? (
							"Update Webinar"
						) : (
							"Schedule Webinar"
						)}
					</Button>
				</div>
			</form>
		</Card>
	);
}
