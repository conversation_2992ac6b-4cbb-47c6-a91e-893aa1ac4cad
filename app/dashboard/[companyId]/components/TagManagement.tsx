"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, TextField } from "@whop/react/components";
import { Plus, Trash2, Edit } from "lucide-react";
import {
	useRegistrationTags,
	useCreateTag,
	useUpdateTag,
	useDeleteTag,
} from "@/lib/queries/registrations";
import type { RegistrationTag } from "@/lib/supabase";

interface TagManagementProps {
	companyId: string;
}

export function TagManagement({ companyId }: TagManagementProps) {
	const { data: tags = [], isLoading } = useRegistrationTags(companyId);
	const createMutation = useCreateTag();
	const updateMutation = useUpdateTag();
	const deleteMutation = useDeleteTag();

	const [editingTag, setEditingTag] = useState<string | null>(null);
	const [newTagName, setNewTagName] = useState("");
	const [newTagColor, setNewTagColor] = useState("#3B82F6");

	const handleCreateTag = async () => {
		if (!newTagName.trim()) return;

		try {
			await createMutation.mutateAsync({
				company_id: companyId,
				name: newTagName.trim(),
				color: newTagColor,
			});
			setNewTagName("");
			setNewTagColor("#3B82F6");
		} catch (error) {
			console.error("Error creating tag:", error);
		}
	};

	const handleUpdateTag = async (tagId: string, name: string, color: string) => {
		try {
			await updateMutation.mutateAsync({
				id: tagId,
				name,
				color,
			});
			setEditingTag(null);
		} catch (error) {
			console.error("Error updating tag:", error);
		}
	};

	const handleDeleteTag = async (tagId: string) => {
		if (!confirm("Are you sure you want to delete this tag?")) return;

		try {
			await deleteMutation.mutateAsync(tagId);
		} catch (error) {
			console.error("Error deleting tag:", error);
		}
	};

	if (isLoading) {
		return <Text>Loading tags...</Text>;
	}

	return (
		<Card className="p-6">
			<Heading size="6" className="mb-4">
				Registration Tags
			</Heading>
			<Text className="text-gray-10 mb-6">
				Create tags to segment and organize your registrations.
			</Text>

			{/* Create New Tag */}
			<div className="flex flex-col sm:flex-row gap-4 mb-8 p-4 border rounded-md">
				<TextField.Root className="flex-1">
					<TextField.Input
						placeholder="Tag name"
						value={newTagName}
						onChange={(e) => setNewTagName(e.target.value)}
					/>
				</TextField.Root>
				<input
					type="color"
					value={newTagColor}
					onChange={(e) => setNewTagColor(e.target.value)}
					className="w-12 h-10 rounded border cursor-pointer"
				/>
				<Button
					onClick={handleCreateTag}
					disabled={!newTagName.trim() || createMutation.isPending}
				>
					<Plus className="w-4 h-4 mr-2" />
					Create Tag
				</Button>
			</div>

			{/* Tags List */}
			<div className="flex flex-col gap-2">
				{tags.length === 0 ? (
					<Text className="text-gray-10">No tags created yet.</Text>
				) : (
					tags.map((tag) => (
						<TagItem
							key={tag.id}
							tag={tag}
							isEditing={editingTag === tag.id}
							onEdit={() => setEditingTag(tag.id)}
							onCancel={() => setEditingTag(null)}
							onUpdate={handleUpdateTag}
							onDelete={handleDeleteTag}
						/>
					))
				)}
			</div>
		</Card>
	);
}

interface TagItemProps {
	tag: RegistrationTag;
	isEditing: boolean;
	onEdit: () => void;
	onCancel: () => void;
	onUpdate: (tagId: string, name: string, color: string) => void;
	onDelete: (tagId: string) => void;
}

function TagItem({
	tag,
	isEditing,
	onEdit,
	onCancel,
	onUpdate,
	onDelete,
}: TagItemProps) {
	const [name, setName] = useState(tag.name);
	const [color, setColor] = useState(tag.color);

	if (isEditing) {
		return (
			<div className="flex items-center gap-2 p-3 border rounded-md">
				<TextField.Root className="flex-1">
					<TextField.Input
						value={name}
						onChange={(e) => setName(e.target.value)}
					/>
				</TextField.Root>
				<input
					type="color"
					value={color}
					onChange={(e) => setColor(e.target.value)}
					className="w-10 h-10 rounded border cursor-pointer"
				/>
				<Button
					size="2"
					onClick={() => onUpdate(tag.id, name, color)}
					disabled={!name.trim()}
				>
					Save
				</Button>
				<Button size="2" variant="surface" onClick={onCancel}>
					Cancel
				</Button>
			</div>
		);
	}

	return (
		<div className="flex items-center justify-between p-3 border rounded-md">
			<div className="flex items-center gap-3">
				<div
					className="w-4 h-4 rounded-full"
					style={{ backgroundColor: tag.color }}
				/>
				<Text className="font-medium">{tag.name}</Text>
			</div>
			<div className="flex items-center gap-2">
				<Button size="2" variant="ghost" onClick={onEdit}>
					<Edit className="w-4 h-4" />
				</Button>
				<Button size="2" variant="ghost" onClick={() => onDelete(tag.id)}>
					<Trash2 className="w-4 h-4" />
				</Button>
			</div>
		</div>
	);
}

