"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON>, <PERSON>ing, Text, Badge, IconButton } from "@whop/react/components";
import { Calendar, Clock, Users, Edit, Trash2, Video } from "lucide-react";
import type { Webinar } from "@/lib/supabase";

interface WebinarCardProps {
	webinar: Webinar;
	onEdit: (webinar: Webinar) => void;
	onDelete: (webinarId: string) => void;
}

export function WebinarCard({ webinar, onEdit, onDelete }: WebinarCardProps) {
	const router = useRouter();
	const scheduledDate = new Date(webinar.scheduled_at);
	const endTime = new Date(
		scheduledDate.getTime() + webinar.duration_minutes * 60 * 1000,
	);
	const formattedDate = scheduledDate.toLocaleDateString("en-US", {
		month: "short",
		day: "numeric",
		year: "numeric",
	});
	const formattedTime = scheduledDate.toLocaleTimeString("en-US", {
		hour: "numeric",
		minute: "2-digit",
	});

	const getStatusColor = (status: string) => {
		switch (status) {
			case "live":
				return "blue";
			case "scheduled":
				return "blue";
			case "completed":
				return "gray";
			case "cancelled":
				return "gray";
			default:
				return "gray";
		}
	};

	return (
		<Card className="p-3 sm:p-4 bg-gray-a2/40 backdrop-blur-sm border border-white/10 hover:bg-gray-a3/50 hover:shadow-lg transition-all h-full flex flex-col relative shadow-none">
			{/* Delete icon button - top right corner with tooltip */}
			<div className="absolute top-2 right-2 z-10 group">
				<IconButton
					variant="soft"
					color="red"
					size="2"
					onClick={() => onDelete(webinar.id)}
					className="relative"
				>
					<Trash2 className="h-4 w-4" />
				</IconButton>
				{/* Tooltip */}
				<div className="absolute right-0 top-full mt-1 px-2 py-1 bg-gray-a2/95 backdrop-blur-md rounded border border-white/10 text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-[opacity] duration-75 pointer-events-none z-20 shadow-lg">
					Delete webinar
				</div>
			</div>
			<div className="flex flex-col gap-3 flex-1">
				<div className="flex flex-wrap items-center gap-2 pr-8">
					<Badge color={getStatusColor(webinar.status)} variant="soft" className="flex-shrink-0 bg-blue-a2/40 backdrop-blur-sm">
						{webinar.status.charAt(0).toUpperCase() + webinar.status.slice(1)}
					</Badge>
					<Heading size="3" weight="bold" className="sm:text-4 break-words flex-1">
						{webinar.title}
					</Heading>
				</div>
				{webinar.description && (
					<Text size="2" color="gray" className="line-clamp-2">
						{webinar.description}
					</Text>
				)}
				{/* Info grid */}
				<div className="flex flex-col gap-1.5">
					<div className="flex items-center gap-2 text-gray-11">
						<Calendar className="h-3.5 w-3.5 flex-shrink-0" />
						<Text size="1" className="sm:text-2">{formattedDate}</Text>
					</div>
					<div className="flex items-center gap-2 text-gray-11">
						<Clock className="h-3.5 w-3.5 flex-shrink-0" />
						<Text size="1" className="sm:text-2">
							{formattedTime} ({webinar.duration_minutes} min)
						</Text>
					</div>
					{webinar.host_ids.length > 0 && (
						<div className="flex items-center gap-2 text-gray-11">
							<Users className="h-3.5 w-3.5 flex-shrink-0" />
							<Text size="1" className="sm:text-2">{webinar.host_ids.length} host(s)</Text>
						</div>
					)}
				</div>
				{/* Buttons - Edit (surface) and Delete (join webinar style) */}
				<div className="flex flex-col gap-2 mt-auto">
					<Button
						variant="surface"
						size="2"
						onClick={() => onEdit(webinar)}
						className="flex items-center justify-center gap-2 w-full"
					>
						<Edit className="h-4 w-4" />
						Edit Webinar
					</Button>
					<Button
						variant="classic"
						color="blue"
						size="2"
						onClick={() => router.push(`/experiences/${webinar.experience_id}?webinarId=${webinar.id}`)}
						className="flex items-center justify-center gap-2 w-full transition-all font-semibold relative overflow-hidden bg-gradient-to-br from-blue-9 via-blue-10 to-blue-11 backdrop-blur-sm border border-white/20 px-4 py-2"
						style={{
							boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(0, 0, 0, 0.2)',
						}}
					>
						<span className="flex items-center gap-2 relative z-10">
							<Video className="h-4 w-4" />
							Join Webinar
						</span>
						{/* Frost overlay for depth */}
						<div className="absolute inset-0 bg-gradient-to-b from-white/20 via-transparent to-black/10 pointer-events-none rounded-lg" />
					</Button>
				</div>
			</div>
		</Card>
	);
}
