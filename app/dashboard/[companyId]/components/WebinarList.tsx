"use client";

import { useState } from "react";
import { But<PERSON>, Heading, Text } from "@whop/react/components";
import { Plus } from "lucide-react";
import { WebinarCard } from "./WebinarCard";
import { CreateWebinarForm } from "./CreateWebinarForm";
import { useWebinars, useDeleteWebinar } from "@/lib/queries/webinars";
import type { Webinar } from "@/lib/supabase";

interface WebinarListProps {
	companyId: string;
	experienceId?: string | null;
}

export function WebinarList({ companyId, experienceId }: WebinarListProps) {
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [editingWebinar, setEditingWebinar] = useState<Webinar | null>(null);

	const { data: webinars = [], isLoading } = useWebinars({ 
		companyId, 
		experienceId: experienceId || undefined 
	});
	const deleteMutation = useDeleteWebinar();

	const handleCreate = () => {
		setEditingWebinar(null);
		setShowCreateForm(true);
	};

	const handleEdit = (webinar: Webinar) => {
		setEditingWebinar(webinar);
		setShowCreateForm(true);
	};

	const handleDelete = async (webinarId: string) => {
		if (!confirm("Are you sure you want to delete this webinar?")) return;

		try {
			await deleteMutation.mutateAsync(webinarId);
		} catch (error) {
			console.error("Error deleting webinar:", error);
			alert("Failed to delete webinar. Please try again.");
		}
	};

	const handleFormClose = () => {
		setShowCreateForm(false);
		setEditingWebinar(null);
	};

	if (showCreateForm) {
		return (
			<CreateWebinarForm
				companyId={companyId}
				experienceId={experienceId}
				initialData={editingWebinar || undefined}
				onClose={handleFormClose}
			/>
		);
	}

	return (
		<div className="flex flex-col gap-4 sm:gap-6 p-4 sm:p-6">
			{/* Header - Mobile: stack, Desktop: side-by-side */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<Heading size="6" weight="bold" className="sm:text-7">
					Scheduled Webinars
				</Heading>
				<Button
					variant="surface"
					size="2"
					onClick={handleCreate}
					className="flex items-center justify-center gap-2 w-full sm:w-auto"
				>
					<Plus className="h-4 w-4" />
					Schedule Webinar
				</Button>
			</div>

			{isLoading ? (
				<div className="flex items-center justify-center py-8 sm:py-12">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-11" />
				</div>
			) : webinars.length === 0 ? (
				<div className="text-center py-8 sm:py-12 px-4 bg-gray-a2/40 backdrop-blur-sm border border-white/10 rounded-lg">
					<Text size="3" color="gray" className="sm:text-4">
						No webinars scheduled yet. Click "Schedule Webinar" to create your first one.
					</Text>
				</div>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
					{webinars.map((webinar) => (
						<WebinarCard
							key={webinar.id}
							webinar={webinar}
							onEdit={handleEdit}
							onDelete={handleDelete}
						/>
					))}
				</div>
			)}
		</div>
	);
}
