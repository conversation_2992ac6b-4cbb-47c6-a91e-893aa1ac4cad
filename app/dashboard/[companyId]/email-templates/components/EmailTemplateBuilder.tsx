"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, TextField, TextArea, Checkbox } from "@whop/react/components";
import { Save, X, Eye } from "lucide-react";
import {
	useEmailTemplate,
	useCreateEmailTemplate,
	useUpdateEmailTemplate,
} from "@/lib/queries/email-templates";
import { getAvailableTemplateVariables, STANDARD_TEMPLATE_VARIABLES } from "@/lib/template-variables";
import type { EmailTemplate } from "@/lib/supabase";

interface EmailTemplateBuilderProps {
	companyId: string;
	templateId?: string;
	type: "confirmation" | "reminder" | "attended" | "missed" | "custom";
	onClose: () => void;
}

export function EmailTemplateBuilder({
	companyId,
	templateId,
	type,
	onClose,
}: EmailTemplateBuilderProps) {
	const { data: existingTemplate } = useEmailTemplate(templateId || null);
	const createMutation = useCreateEmailTemplate();
	const updateMutation = useUpdateEmailTemplate();

	const [formData, setFormData] = useState<{
		name: string;
		subject: string;
		body_html: string;
		body_text: string;
		is_active: boolean;
	}>({
		name: "",
		subject: "",
		body_html: "",
		body_text: "",
		is_active: true,
	});

	const [showPreview, setShowPreview] = useState(false);
	const [insertVariable, setInsertVariable] = useState<string | null>(null);

	useEffect(() => {
		if (existingTemplate) {
			setFormData({
				name: existingTemplate.name,
				subject: existingTemplate.subject,
				body_html: existingTemplate.body_html,
				body_text: existingTemplate.body_text || "",
				is_active: existingTemplate.is_active,
			});
		} else {
			// Set default name based on type
			setFormData((prev) => ({
				...prev,
				name: type.charAt(0).toUpperCase() + type.slice(1) + " Email Template",
			}));
		}
	}, [existingTemplate, type]);

	const handleInsertVariable = (variable: string) => {
		const textarea = document.getElementById("body_html") as HTMLTextAreaElement;
		if (textarea) {
			const cursorPos = textarea.selectionStart;
			const textBefore = formData.body_html.substring(0, cursorPos);
			const textAfter = formData.body_html.substring(cursorPos);
			setFormData({
				...formData,
				body_html: textBefore + `{{${variable}}}` + textAfter,
			});
		}
		setInsertVariable(null);
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!formData.name || !formData.subject || !formData.body_html) {
			alert("Please fill in all required fields");
			return;
		}

		try {
			if (templateId) {
				await updateMutation.mutateAsync({
					id: templateId,
					...formData,
				});
			} else {
				await createMutation.mutateAsync({
					company_id: companyId,
					type,
					...formData,
					variables: getAvailableTemplateVariables(),
				});
			}
			onClose();
		} catch (error) {
			console.error("Error saving template:", error);
			alert("Failed to save template");
		}
	};

	return (
		<div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 overflow-y-auto">
			<Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
				<div className="p-6">
					<div className="flex items-center justify-between mb-6">
						<Heading size="6">
							{templateId ? "Edit Email Template" : "Create Email Template"}
						</Heading>
						<Button variant="ghost" onClick={onClose}>
							<X className="w-4 h-4" />
						</Button>
					</div>

					<form onSubmit={handleSubmit} className="flex flex-col gap-6">
						<div className="flex flex-col gap-2">
							<Text size="2" weight="semi-bold">
								Template Name <span className="text-red-500">*</span>
							</Text>
							<TextField.Root>
								<TextField.Input
									value={formData.name}
									onChange={(e) =>
										setFormData((prev) => ({ ...prev, name: e.target.value }))
									}
									required
								/>
							</TextField.Root>
						</div>

						<div className="flex flex-col gap-2">
							<Text size="2" weight="semi-bold">
								Subject <span className="text-red-500">*</span>
							</Text>
							<TextField.Root>
								<TextField.Input
									value={formData.subject}
									onChange={(e) =>
										setFormData((prev) => ({ ...prev, subject: e.target.value }))
									}
									placeholder="Email subject line"
									required
								/>
							</TextField.Root>
							<Text size="1" color="gray">
								Use variables like {"{{name}}"}, {"{{webinar_title}}"}, etc.
							</Text>
						</div>

						<div className="flex flex-col gap-2">
							<div className="flex items-center justify-between">
								<Text size="2" weight="semi-bold">
									HTML Body <span className="text-red-500">*</span>
								</Text>
								<div className="relative">
									<Button
										type="button"
										variant="ghost"
										size="2"
										onClick={() => setInsertVariable(insertVariable ? null : "variable")}
									>
										Insert Variable
									</Button>
									{insertVariable && (
										<div className="absolute right-0 top-full mt-2 bg-white border rounded-md shadow-lg p-2 z-10 max-h-60 overflow-y-auto w-64">
											{Object.entries(STANDARD_TEMPLATE_VARIABLES).map(([key, desc]) => (
												<Button
													key={key}
													type="button"
													variant="ghost"
													onClick={() => handleInsertVariable(key)}
													className="w-full text-left px-2 py-1 hover:bg-gray-100 text-sm"
												>
													<Text weight="bold">{"{{" + key + "}}"}</Text>
													<Text size="1" color="gray" className="block">{desc}</Text>
												</Button>
											))}
										</div>
									)}
								</div>
							</div>
							<TextArea
								id="body_html"
								value={formData.body_html}
								onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
									setFormData((prev) => ({ ...prev, body_html: e.target.value }))
								}
								placeholder="HTML email content..."
								rows={12}
								required
								className="font-mono text-sm"
							/>
						</div>

						<div className="flex flex-col gap-2">
							<Text size="2" weight="semi-bold">Plain Text Body (Optional)</Text>
							<TextArea
								value={formData.body_text}
								onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
									setFormData((prev) => ({ ...prev, body_text: e.target.value }))
								}
								placeholder="Plain text version..."
								rows={8}
								className="font-mono text-sm"
							/>
						</div>

						<div className="flex items-center gap-2">
							<Checkbox
								id="is-active"
								checked={formData.is_active}
								onCheckedChange={(checked) =>
									setFormData((prev) => ({ ...prev, is_active: checked === true }))
								}
							>
								<Text size="2">Active (template is available for use)</Text>
							</Checkbox>
						</div>

						<div className="flex items-center justify-end gap-4 pt-4 border-t">
							<Button type="button" variant="ghost" onClick={onClose}>
								Cancel
							</Button>
							<Button type="submit" disabled={createMutation.isPending || updateMutation.isPending}>
								{createMutation.isPending || updateMutation.isPending
									? "Saving..."
									: templateId
										? "Update"
										: "Create"}
							</Button>
						</div>
					</form>
				</div>
			</Card>
		</div>
	);
}

