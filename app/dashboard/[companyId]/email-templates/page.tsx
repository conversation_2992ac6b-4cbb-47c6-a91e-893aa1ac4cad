import { headers } from "next/headers";
import { whopsdk } from "@/lib/whop-sdk";
import { EmailTemplateBuilder } from "./components/EmailTemplateBuilder";

export default async function EmailTemplatesPage({
	params,
	searchParams,
}: {
	params: Promise<{ companyId: string }>;
	searchParams: Promise<{ type?: string; templateId?: string }>;
}) {
	const { companyId } = await params;
	const resolvedSearchParams = await searchParams;
	const { userId } = await whopsdk.verifyUserToken(await headers());

	return (
		<div className="min-h-screen p-4 sm:p-8">
			<div className="max-w-6xl mx-auto">
				<EmailTemplateBuilder
					companyId={companyId}
					templateId={resolvedSearchParams.templateId}
					type={(resolvedSearchParams.type as any) || "custom"}
					onClose={() => {
						if (typeof window !== "undefined") {
							window.location.href = `/dashboard/${companyId}`;
						}
					}}
				/>
			</div>
		</div>
	);
}

