import { Heading } from "@whop/react/components";
import { headers } from "next/headers";
import { whopsdk } from "@/lib/whop-sdk";
import { WebinarList } from "./components/WebinarList";

export default async function DashboardPage({
	params,
}: {
	params: Promise<{ companyId: string }>;
}) {
	const { companyId } = await params;
	const { userId } = await whopsdk.verifyUserToken(await headers());

	const [company, user, access] = await Promise.all([
		whopsdk.companies.retrieve(companyId),
		whopsdk.users.retrieve(userId),
		whopsdk.users.checkAccess(companyId, { id: userId }),
	]);

	if (!access.has_access) {
		return (
			<div className="flex flex-col items-center justify-center min-h-screen p-8">
				<div className="max-w-md text-center">
					<Heading size="8" weight="bold" className="mb-4">
						Access Restricted
					</Heading>
					<p className="text-4 text-gray-10">
						You don't have access to this company dashboard.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen">
			<div className="container mx-auto py-4 sm:py-6 md:py-8">
				<div className="mb-6 sm:mb-8 px-4 sm:px-6">
					<Heading size="7" weight="bold" className="sm:text-8">
					Dashboard
				</Heading>
				</div>
				<WebinarList companyId={companyId} experienceId={null} />
			</div>
		</div>
	);
}
