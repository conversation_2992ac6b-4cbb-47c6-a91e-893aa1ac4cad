"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, TextField } from "@whop/react/components";
import { Download, Filter, Tag } from "lucide-react";
import { useRegistrations, useRegistrationTags } from "@/lib/queries/registrations";
import { downloadCSV, generateCSV, generateCSVFilename } from "@/lib/csv-export";
import type { RegistrationSubmission, RegistrationTag } from "@/lib/supabase";

interface RegistrationLeadsViewProps {
	pageId: string;
	companyId: string;
	webinarTitle: string;
}

export function RegistrationLeadsView({
	pageId,
	companyId,
	webinarTitle,
}: RegistrationLeadsViewProps) {
	const { data: registrations = [], isLoading } = useRegistrations(pageId);
	const { data: tags = [] } = useRegistrationTags(companyId);

	const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
	const [searchQuery, setSearchQuery] = useState("");

	const filteredRegistrations = registrations.filter((reg) => {
		if (selectedTagIds.length > 0) {
			// In a real implementation, you'd filter by tag assignments
			// For now, we'll skip tag filtering
		}
		if (searchQuery) {
			const query = searchQuery.toLowerCase();
			return (
				reg.name.toLowerCase().includes(query) ||
				reg.email.toLowerCase().includes(query) ||
				(reg.phone && reg.phone.toLowerCase().includes(query))
			);
		}
		return true;
	});

	const handleExportCSV = () => {
		const csvContent = generateCSV(filteredRegistrations);
		const filename = generateCSVFilename(webinarTitle);
		downloadCSV(csvContent, filename);
	};

	if (isLoading) {
		return <Text>Loading registrations...</Text>;
	}

	return (
		<div className="flex flex-col gap-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
				<div>
					<Heading size="6" className="mb-2">
						Registrations
					</Heading>
					<Text className="text-gray-10">
						{filteredRegistrations.length} of {registrations.length} registrations
					</Text>
				</div>
				<Button onClick={handleExportCSV} variant="surface">
					<Download className="w-4 h-4 mr-2" />
					Export CSV
				</Button>
			</div>

			{/* Filters */}
			<Card className="p-4">
				<div className="flex flex-col sm:flex-row gap-4">
					<div className="flex-1">
						<TextField.Root>
							<TextField.Input
								placeholder="Search by name, email, or phone..."
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
							/>
						</TextField.Root>
					</div>
					{tags.length > 0 && (
						<div className="flex flex-wrap gap-2">
							{tags.map((tag) => (
								<button
									key={tag.id}
									type="button"
									onClick={() => {
										setSelectedTagIds((prev) =>
											prev.includes(tag.id)
												? prev.filter((id) => id !== tag.id)
												: [...prev, tag.id],
										);
									}}
									className={`px-3 py-1 rounded-full text-sm flex items-center gap-2 transition-colors ${
										selectedTagIds.includes(tag.id)
											? "bg-blue-500 text-white"
											: "bg-gray-3 text-gray-11 hover:bg-gray-4"
									}`}
								>
									<div
										className="w-2 h-2 rounded-full"
										style={{ backgroundColor: tag.color }}
									/>
									{tag.name}
								</button>
							))}
						</div>
					)}
				</div>
			</Card>

			{/* Registrations List */}
			<div className="flex flex-col gap-4">
				{filteredRegistrations.length === 0 ? (
					<Card className="p-8 text-center">
						<Text className="text-gray-10">
							{registrations.length === 0
								? "No registrations yet."
								: "No registrations match your filters."}
						</Text>
					</Card>
				) : (
					filteredRegistrations.map((registration) => (
						<RegistrationCard
							key={registration.id}
							registration={registration}
							tags={tags}
						/>
					))
				)}
			</div>
		</div>
	);
}

interface RegistrationCardProps {
	registration: RegistrationSubmission;
	tags: RegistrationTag[];
}

function RegistrationCard({ registration, tags }: RegistrationCardProps) {
	const formattedDate = new Date(registration.created_at).toLocaleString();

	return (
		<Card className="p-4">
			<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
				<div className="flex-1">
					<div className="flex items-center gap-2 mb-2">
						<Heading size="6">{registration.name}</Heading>
						<Text className="text-sm text-gray-10">({registration.source})</Text>
					</div>
					<div className="flex flex-col gap-1 text-sm">
						<Text className="text-gray-11">
							<strong>Email:</strong> {registration.email}
						</Text>
						{registration.phone && (
							<Text className="text-gray-11">
								<strong>Phone:</strong> {registration.phone}
							</Text>
						)}
						<Text className="text-gray-10">Registered: {formattedDate}</Text>
					</div>
					{Object.keys(registration.custom_field_data || {}).length > 0 && (
						<div className="mt-3 pt-3 border-t">
							<Text className="text-sm font-medium mb-2">Custom Fields:</Text>
							<div className="flex flex-col gap-1">
								{Object.entries(registration.custom_field_data || {}).map(
									([key, value]) => (
										<Text key={key} className="text-sm text-gray-11">
											<strong>{key}:</strong> {String(value)}
										</Text>
									),
								)}
							</div>
						</div>
					)}
				</div>
			</div>
		</Card>
	);
}

