import { headers } from "next/headers";
import { whopsdk } from "@/lib/whop-sdk";
import { RegistrationLeadsView } from "../components/RegistrationLeadsView";
import { supabaseServer } from "@/lib/supabase";

export default async function RegistrationLeadsPage({
	params,
}: {
	params: Promise<{ companyId: string; pageId: string }>;
}) {
	const { companyId, pageId } = await params;
	const { userId } = await whopsdk.verifyUserToken(await headers());

	// Get registration page and webinar info
	const { data: registrationPage } = await supabaseServer
		.from("registration_pages")
		.select("*, webinars!inner(title)")
		.eq("id", pageId)
		.single();

	if (!registrationPage) {
		return (
			<div className="flex items-center justify-center min-h-screen p-8">
				<div className="text-center">
					<h1 className="text-2xl font-bold mb-4">Registration Page Not Found</h1>
					<p className="text-gray-10">The registration page you're looking for doesn't exist.</p>
				</div>
			</div>
		);
	}

	const webinar = (registrationPage as any).webinars;
	const webinarTitle = webinar?.title || "Webinar";

	return (
		<div className="min-h-screen p-4 sm:p-8">
			<div className="max-w-6xl mx-auto">
				<RegistrationLeadsView
					pageId={pageId}
					companyId={companyId}
					webinarTitle={webinarTitle}
				/>
			</div>
		</div>
	);
}

