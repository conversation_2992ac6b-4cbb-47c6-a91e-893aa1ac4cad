"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text } from "@whop/react/components";
import { Filter, Clock, CheckCircle, XCircle } from "lucide-react";
import { useAttendance } from "@/lib/queries/email-templates";
import { useRegistrations } from "@/lib/queries/registrations";
import type { WebinarAttendance } from "@/lib/supabase";

interface AttendanceViewProps {
	webinarId: string;
	pageId?: string;
}

export function AttendanceView({ webinarId, pageId }: AttendanceViewProps) {
	const { data: attendance, isLoading } = useAttendance(webinarId);
	const { data: registrations = [] } = useRegistrations(pageId, webinarId);

	const [statusFilter, setStatusFilter] = useState<
		"all" | "attended" | "missed" | "partial"
	>("all");

	const attendanceArray = Array.isArray(attendance) ? attendance : [];
	const attendanceMap = new Map<string, WebinarAttendance>();
	attendanceArray.forEach((att) => {
		attendanceMap.set(att.submission_id, att);
	});

	const filteredRegistrations = registrations.filter((reg) => {
		if (statusFilter === "all") return true;
		const att = attendanceMap.get(reg.id);
		if (!att) return statusFilter === "missed";
		return att.status === statusFilter;
	});

	if (isLoading) {
		return <Text>Loading attendance...</Text>;
	}

	const stats = {
		total: registrations.length,
		attended: attendanceArray.filter((a) => a.status === "attended").length,
		missed: registrations.length - attendanceArray.length,
		partial: attendanceArray.filter((a) => a.status === "partial").length,
	};

	return (
		<div className="flex flex-col gap-6">
			<Heading size="6">Attendance</Heading>

			{/* Stats */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				<Card className="p-4">
					<Text className="text-sm text-gray-10 mb-1">Total Registered</Text>
					<Text className="text-2xl font-bold">{stats.total}</Text>
				</Card>
				<Card className="p-4">
					<Text className="text-sm text-gray-10 mb-1">Attended</Text>
					<Text className="text-2xl font-bold text-green-11">{stats.attended}</Text>
				</Card>
				<Card className="p-4">
					<Text className="text-sm text-gray-10 mb-1">Missed</Text>
					<Text className="text-2xl font-bold text-red-11">{stats.missed}</Text>
				</Card>
				<Card className="p-4">
					<Text className="text-sm text-gray-10 mb-1">Partial</Text>
					<Text className="text-2xl font-bold text-orange-11">{stats.partial}</Text>
				</Card>
			</div>

			{/* Filters */}
			<Card className="p-4">
				<div className="flex flex-wrap gap-2">
					{(["all", "attended", "missed", "partial"] as const).map((status) => (
						<button
							key={status}
							type="button"
							onClick={() => setStatusFilter(status)}
							className={`px-3 py-1 rounded-full text-sm transition-colors ${
								statusFilter === status
									? "bg-blue-500 text-white"
									: "bg-gray-3 text-gray-11 hover:bg-gray-4"
							}`}
						>
							{status.charAt(0).toUpperCase() + status.slice(1)}
						</button>
					))}
				</div>
			</Card>

			{/* Attendance List */}
			<div className="flex flex-col gap-4">
				{filteredRegistrations.length === 0 ? (
					<Card className="p-8 text-center">
						<Text className="text-gray-10">
							{registrations.length === 0
								? "No registrations yet."
								: "No registrations match your filter."}
						</Text>
					</Card>
				) : (
					filteredRegistrations.map((registration) => {
						const att = attendanceMap.get(registration.id);
						return (
							<Card key={registration.id} className="p-4">
								<div className="flex items-center justify-between">
									<div className="flex-1">
										<Text className="font-medium">{registration.name}</Text>
										<Text className="text-sm text-gray-10">{registration.email}</Text>
										{att && (
											<div className="flex items-center gap-4 mt-2 text-sm">
												<div className="flex items-center gap-1">
													<Clock className="w-3 h-3" />
													Joined: {new Date(att.joined_at).toLocaleString()}
												</div>
												{att.duration_minutes && (
													<Text>Duration: {att.duration_minutes} minutes</Text>
												)}
											</div>
										)}
									</div>
									<div className="flex items-center gap-2">
										{att ? (
											att.status === "attended" ? (
												<CheckCircle className="w-5 h-5 text-green-11" />
											) : (
												<Clock className="w-5 h-5 text-orange-11" />
											)
										) : (
											<XCircle className="w-5 h-5 text-red-11" />
										)}
									</div>
								</div>
							</Card>
						);
					})
				)}
			</div>
		</div>
	);
}

