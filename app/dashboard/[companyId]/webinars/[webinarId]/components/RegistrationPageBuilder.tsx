"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, TextField, TextArea, Select, Checkbox } from "@whop/react/components";
import { Plus, Trash2, GripVertical, Eye, Save, X } from "lucide-react";
import {
	useCreateRegistrationPage,
	useUpdateRegistrationPage,
	useRegistrationPage,
} from "@/lib/queries/registrations";
import { useWebinar } from "@/lib/queries/webinars";
import { REGISTRATION_TEMPLATES, getTemplate } from "@/lib/registration-templates";
import type { RegistrationPage, CustomField, ThankYouPageConfig } from "@/lib/supabase";

interface RegistrationPageBuilderProps {
	webinarId: string;
	experienceId: string;
	companyId: string;
	pageId?: string;
	onClose: () => void;
}

export function RegistrationPageBuilder({
	webinarId,
	experienceId,
	companyId,
	pageId,
	onClose,
}: RegistrationPageBuilderProps) {
	const { data: existingPage } = useRegistrationPage(pageId || null);
	const { data: webinar } = useWebinar(webinarId);

	const createMutation = useCreateRegistrationPage();
	const updateMutation = useUpdateRegistrationPage();
	const isLoading = createMutation.isPending || updateMutation.isPending;

	const [formData, setFormData] = useState<{
		slug: string;
		template_id: string;
		title: string;
		description: string;
		custom_fields: CustomField[];
		thank_you_page_config: ThankYouPageConfig;
		is_active: boolean;
		requires_auth: boolean;
	}>({
		slug: "",
		template_id: "standard",
		title: "",
		description: "",
		custom_fields: [],
		thank_you_page_config: {},
		is_active: true,
		requires_auth: false,
	});

	const [showPreview, setShowPreview] = useState(false);

	useEffect(() => {
		if (existingPage) {
			setFormData({
				slug: existingPage.slug,
				template_id: existingPage.template_id,
				title: existingPage.title,
				description: existingPage.description || "",
				custom_fields: existingPage.custom_fields || [],
				thank_you_page_config: existingPage.thank_you_page_config || {},
				is_active: existingPage.is_active,
				requires_auth: existingPage.requires_auth,
			});
		} else {
			// Initialize with template defaults
			const template = getTemplate("standard");
			if (template) {
				setFormData((prev) => ({
					...prev,
					title: webinar?.title || "Register for Webinar",
					custom_fields: template.defaultFields,
					thank_you_page_config: template.defaultThankYouConfig,
				}));
			}
		}
	}, [existingPage, webinar]);

	const handleTemplateChange = (templateId: string) => {
		const template = getTemplate(templateId);
		if (template) {
			setFormData((prev) => ({
				...prev,
				template_id: templateId,
				custom_fields: template.defaultFields,
				thank_you_page_config: template.defaultThankYouConfig,
			}));
		}
	};

	const handleAddField = () => {
		const newField: CustomField = {
			id: `field-${Date.now()}`,
			type: "text",
			label: "New Field",
			placeholder: "",
			required: false,
			order: formData.custom_fields.length,
		};
		setFormData((prev) => ({
			...prev,
			custom_fields: [...prev.custom_fields, newField],
		}));
	};

	const handleUpdateField = (fieldId: string, updates: Partial<CustomField>) => {
		setFormData((prev) => ({
			...prev,
			custom_fields: prev.custom_fields.map((f) =>
				f.id === fieldId ? { ...f, ...updates } : f,
			),
		}));
	};

	const handleDeleteField = (fieldId: string) => {
		setFormData((prev) => ({
			...prev,
			custom_fields: prev.custom_fields.filter((f) => f.id !== fieldId),
		}));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!formData.slug || !formData.title) {
			alert("Please fill in all required fields");
			return;
		}

		try {
			if (pageId) {
				await updateMutation.mutateAsync({
					id: pageId,
					...formData,
				});
			} else {
				await createMutation.mutateAsync({
					webinar_id: webinarId,
					experience_id: experienceId,
					company_id: companyId,
					...formData,
				});
			}
			onClose();
		} catch (error) {
			console.error("Error saving registration page:", error);
			alert("Failed to save registration page");
		}
	};

	return (
		<div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 overflow-y-auto">
			<Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
				<div className="p-6">
					<div className="flex items-center justify-between mb-6">
						<Heading size="6">
							{pageId ? "Edit Registration Page" : "Create Registration Page"}
						</Heading>
						<Button variant="ghost" onClick={onClose}>
							<X className="w-4 h-4" />
						</Button>
					</div>

					<form onSubmit={handleSubmit} className="flex flex-col gap-6">
						{/* Basic Settings */}
						<div className="flex flex-col gap-4">
							<Heading size="5">Basic Settings</Heading>
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Slug (URL) <span className="text-red-500">*</span>
								</Text>
								<TextField.Root>
									<TextField.Input
										value={formData.slug}
										onChange={(e) =>
											setFormData((prev) => ({ ...prev, slug: e.target.value }))
										}
										placeholder="my-webinar-registration"
										required
									/>
								</TextField.Root>
								<Text size="1" color="gray">
									URL: /register/{formData.slug || "..."}
								</Text>
							</div>

							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Title <span className="text-red-500">*</span>
								</Text>
								<TextField.Root>
									<TextField.Input
										value={formData.title}
										onChange={(e) =>
											setFormData((prev) => ({ ...prev, title: e.target.value }))
										}
										placeholder="Register for Webinar"
										required
									/>
								</TextField.Root>
							</div>

							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">Description</Text>
								<TextArea
									value={formData.description}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											description: e.target.value,
										}))
									}
									placeholder="Enter a description for your registration page"
									rows={3}
								/>
							</div>
						</div>

						{/* Template Selection */}
						<div className="flex flex-col gap-4">
							<Heading size="5">Template</Heading>
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
								{REGISTRATION_TEMPLATES.map((template) => (
									<Card
										key={template.id}
										className={`p-4 cursor-pointer transition-colors ${
											formData.template_id === template.id
												? "border-blue-500 border-2"
												: ""
										}`}
										onClick={() => handleTemplateChange(template.id)}
									>
										<Heading size="6" className="mb-2">
											{template.name}
										</Heading>
										<Text className="text-sm text-gray-10">
											{template.description}
										</Text>
									</Card>
								))}
							</div>
						</div>

						{/* Custom Fields */}
						<div className="flex flex-col gap-4">
							<div className="flex items-center justify-between">
								<Heading size="5">Custom Fields</Heading>
								<Button
									type="button"
									onClick={handleAddField}
									variant="surface"
									size="2"
								>
									<Plus className="w-4 h-4 mr-2" />
									Add Field
								</Button>
							</div>

							<div className="flex flex-col gap-4">
								{formData.custom_fields.map((field, index) => (
									<Card key={field.id} className="p-4">
										<div className="flex flex-col gap-4">
											<div className="flex items-center justify-between">
												<div className="flex items-center gap-2">
													<GripVertical className="w-4 h-4 text-gray-10" />
													<Text className="text-sm font-medium">
														Field {index + 1}
													</Text>
												</div>
												<Button
													type="button"
													onClick={() => handleDeleteField(field.id)}
													variant="ghost"
													size="2"
												>
													<Trash2 className="w-4 h-4" />
												</Button>
											</div>

											<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
												<div className="flex flex-col gap-2">
													<Text size="2" weight="semi-bold">Label</Text>
													<TextField.Root>
														<TextField.Input
															value={field.label}
															onChange={(e) =>
																handleUpdateField(field.id, {
																	label: e.target.value,
																})
															}
														/>
													</TextField.Root>
												</div>

												<div className="flex flex-col gap-2">
													<Text size="2" weight="semi-bold">Type</Text>
													<Select.Root
														value={field.type}
														onValueChange={(value) =>
															handleUpdateField(field.id, {
																type: value as CustomField["type"],
															})
														}
													>
														<Select.Trigger placeholder="Select type">
														</Select.Trigger>
														<Select.Content>
															<Select.Item value="text">Text</Select.Item>
															<Select.Item value="textarea">Textarea</Select.Item>
															<Select.Item value="select">Select</Select.Item>
															<Select.Item value="checkbox">Checkbox</Select.Item>
															<Select.Item value="radio">Radio</Select.Item>
															<Select.Item value="number">Number</Select.Item>
															<Select.Item value="date">Date</Select.Item>
															<Select.Item value="url">URL</Select.Item>
														</Select.Content>
													</Select.Root>
												</div>
											</div>

											<div className="flex flex-col gap-2">
												<Text size="2" weight="semi-bold">
													Placeholder
												</Text>
												<TextField.Root>
													<TextField.Input
														value={field.placeholder || ""}
														onChange={(e) =>
															handleUpdateField(field.id, {
																placeholder: e.target.value,
															})
														}
													/>
												</TextField.Root>
											</div>

											{(field.type === "select" || field.type === "radio") && (
												<div className="flex flex-col gap-2">
													<Text size="2" weight="semi-bold">
														Options (one per line)
													</Text>
													<TextArea
														value={field.options?.join("\n") || ""}
														onChange={(e) =>
															handleUpdateField(field.id, {
																options: e.target.value
																	.split("\n")
																	.filter((o) => o.trim()),
															})
														}
														rows={3}
													/>
												</div>
											)}

											<div className="flex items-center gap-2">
												<Checkbox
													id={`required-${field.id}`}
													checked={field.required}
													onCheckedChange={(checked) =>
														handleUpdateField(field.id, {
															required: checked === true,
														})
													}
												>
													<Text size="2">Required</Text>
												</Checkbox>
											</div>
										</div>
									</Card>
								))}
							</div>
						</div>

						{/* Thank You Page Config */}
						<div className="flex flex-col gap-4">
							<Heading size="5">Thank You Page</Heading>
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">Title</Text>
								<TextField.Root>
									<TextField.Input
										value={formData.thank_you_page_config.title || ""}
										onChange={(e) =>
											setFormData((prev) => ({
												...prev,
												thank_you_page_config: {
													...prev.thank_you_page_config,
													title: e.target.value,
												},
											}))
										}
										placeholder="Thank You!"
									/>
								</TextField.Root>
							</div>
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">Message</Text>
								<TextArea
									value={formData.thank_you_page_config.message || ""}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											thank_you_page_config: {
												...prev.thank_you_page_config,
												message: e.target.value,
											},
										}))
									}
									placeholder="Your registration is confirmed..."
									rows={3}
								/>
							</div>
							<div className="flex items-center gap-2">
								<Checkbox
									id="show-calendar"
									checked={
										formData.thank_you_page_config.showCalendarDownload !== false
									}
									onCheckedChange={(checked) =>
										setFormData((prev) => ({
											...prev,
											thank_you_page_config: {
												...prev.thank_you_page_config,
												showCalendarDownload: checked === true,
											},
										}))
									}
								>
									<Text size="2">Show calendar download button</Text>
								</Checkbox>
							</div>
						</div>

						{/* Status */}
						<div className="flex flex-col gap-4">
							<Heading size="5">Settings</Heading>
							<div className="flex items-center gap-2">
								<Checkbox
									id="is-active"
									checked={formData.is_active}
									onCheckedChange={(checked) =>
										setFormData((prev) => ({
											...prev,
											is_active: checked === true,
										}))
									}
								>
									<Text size="2">Active (registration page is live)</Text>
								</Checkbox>
							</div>
							<div className="flex items-center gap-2">
								<Checkbox
									id="requires-auth"
									checked={formData.requires_auth}
									onCheckedChange={(checked) =>
										setFormData((prev) => ({
											...prev,
											requires_auth: checked === true,
										}))
									}
								>
									<Text size="2">Require authentication (for internal use)</Text>
								</Checkbox>
							</div>
						</div>

						{/* Actions */}
						<div className="flex items-center justify-end gap-4 pt-4 border-t">
							<Button type="button" variant="surface" onClick={onClose}>
								Cancel
							</Button>
							<Button type="submit" disabled={isLoading}>
								{isLoading ? "Saving..." : pageId ? "Update" : "Create"}
							</Button>
						</div>
					</form>
				</div>
			</Card>
		</div>
	);
}

