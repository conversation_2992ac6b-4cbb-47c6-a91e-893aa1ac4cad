"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, TextField } from "@whop/react/components";
import { Plus, Trash2, Clock } from "lucide-react";
import {
	useReminderSchedules,
	useCreateReminderSchedule,
	useUpdateReminderSchedule,
	useDeleteReminderSchedule,
} from "@/lib/queries/reminder-schedules";
import { useEmailTemplates } from "@/lib/queries/email-templates";
import type { ReminderSchedule } from "@/lib/supabase";

interface ReminderScheduleManagerProps {
	webinarId: string;
	companyId: string;
}

export function ReminderScheduleManager({
	webinarId,
	companyId,
}: ReminderScheduleManagerProps) {
	const { data: schedules = [], isLoading } = useReminderSchedules(webinarId);
	const { data: templates = [] } = useEmailTemplates(companyId);
	const createMutation = useCreateReminderSchedule();
	const updateMutation = useUpdateReminderSchedule();
	const deleteMutation = useDeleteReminderSchedule();

	const [editingSchedule, setEditingSchedule] = useState<string | null>(null);
	const [newSchedule, setNewSchedule] = useState<Partial<ReminderSchedule>>({
		reminder_type: "email",
		timing_hours_before: 24,
		is_active: true,
	});

	const handleCreateSchedule = async () => {
		if (!newSchedule.timing_hours_before) return;

		try {
			await createMutation.mutateAsync({
				webinar_id: webinarId,
				reminder_type: newSchedule.reminder_type || "email",
				timing_hours_before: newSchedule.timing_hours_before!,
				is_active: newSchedule.is_active !== false,
				template_id: newSchedule.template_id || null,
			});

			setNewSchedule({
				reminder_type: "email",
				timing_hours_before: 24,
				is_active: true,
			});
		} catch (error) {
			console.error("Error creating schedule:", error);
		}
	};

	const handleDeleteSchedule = async (scheduleId: string) => {
		if (!confirm("Are you sure you want to delete this reminder schedule?"))
			return;

		try {
			await deleteMutation.mutateAsync(scheduleId);
		} catch (error) {
			console.error("Error deleting schedule:", error);
		}
	};

	if (isLoading) {
		return <Text>Loading reminder schedules...</Text>;
	}

	return (
		<Card className="p-6">
			<Heading size="6" className="mb-4">
				Reminder Schedules
			</Heading>
			<Text className="text-gray-10 mb-6">
				Schedule automatic email and DM reminders before webinars.
			</Text>

			{/* Create New Schedule */}
			<div className="flex flex-col gap-4 mb-8 p-4 border rounded-md">
				<Heading size="5">Add Reminder</Heading>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="flex flex-col gap-2">
						<label className="text-sm font-medium">Hours Before Webinar</label>
						<TextField.Root>
							<TextField.Input
								type="number"
								value={newSchedule.timing_hours_before || ""}
								onChange={(e) =>
									setNewSchedule({
										...newSchedule,
										timing_hours_before: parseInt(e.target.value) || 0,
									})
								}
								placeholder="24"
								min="1"
							/>
						</TextField.Root>
					</div>
					<div className="flex flex-col gap-2">
						<label className="text-sm font-medium">Reminder Type</label>
						<select
							value={newSchedule.reminder_type || "email"}
							onChange={(e) =>
								setNewSchedule({
									...newSchedule,
									reminder_type: e.target.value as "email" | "dm" | "both",
								})
							}
							className="w-full px-3 py-2 border rounded-md"
						>
							<option value="email">Email Only</option>
							<option value="dm">DM Only</option>
							<option value="both">Email & DM</option>
						</select>
					</div>
					<div className="flex flex-col gap-2">
						<label className="text-sm font-medium">Template (Optional)</label>
						<select
							value={newSchedule.template_id || ""}
							onChange={(e) =>
								setNewSchedule({
									...newSchedule,
									template_id: e.target.value || null,
								})
							}
							className="w-full px-3 py-2 border rounded-md"
						>
							<option value="">Default Template</option>
							{templates
								.filter((t) => t.type === "reminder")
								.map((t) => (
									<option key={t.id} value={t.id}>
										{t.name}
									</option>
								))}
						</select>
					</div>
				</div>
				<Button
					onClick={handleCreateSchedule}
					disabled={!newSchedule.timing_hours_before || createMutation.isPending}
				>
					<Plus className="w-4 h-4 mr-2" />
					Add Reminder Schedule
				</Button>
			</div>

			{/* Schedules List */}
			<div className="flex flex-col gap-2">
				{schedules.length === 0 ? (
					<Text className="text-gray-10">No reminder schedules configured.</Text>
				) : (
					schedules.map((schedule) => (
						<ScheduleItem
							key={schedule.id}
							schedule={schedule}
							templates={templates}
							onDelete={handleDeleteSchedule}
						/>
					))
				)}
			</div>
		</Card>
	);
}

interface ScheduleItemProps {
	schedule: ReminderSchedule;
	templates: any[];
	onDelete: (id: string) => void;
}

function ScheduleItem({ schedule, templates, onDelete }: ScheduleItemProps) {
	const template = templates.find((t) => t.id === schedule.template_id);

	return (
		<div className="flex items-center justify-between p-3 border rounded-md">
			<div className="flex items-center gap-3">
				<Clock className="w-4 h-4 text-gray-10" />
				<div>
					<Text className="font-medium">
						{schedule.timing_hours_before} hour(s) before
					</Text>
					<Text className="text-sm text-gray-10">
						Type: {schedule.reminder_type} {template ? `• Template: ${template.name}` : ""}
					</Text>
				</div>
			</div>
			<Button
				size="2"
				variant="ghost"
				onClick={() => onDelete(schedule.id)}
			>
				<Trash2 className="w-4 h-4" />
			</Button>
		</div>
	);
}

