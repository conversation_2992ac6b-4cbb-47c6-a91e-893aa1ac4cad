"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Card, Heading, Text } from "@whop/react/components";
import { RegistrationPageBuilder } from "../../components/RegistrationPageBuilder";
import { ReminderScheduleManager } from "../../components/ReminderScheduleManager";
import { AttendanceView } from "../../components/AttendanceView";

interface WebinarRegistrationManagementProps {
	webinarId: string;
	companyId: string;
	experienceId: string;
}

export function WebinarRegistrationManagement({
	webinarId,
	companyId,
	experienceId,
}: WebinarRegistrationManagementProps) {
	const [activeTab, setActiveTab] = useState<"registration" | "reminders" | "attendance">("registration");
	const [showBuilder, setShowBuilder] = useState(false);

	return (
		<div className="min-h-screen p-4 sm:p-8">
			<div className="max-w-6xl mx-auto">
				<Heading size="6" className="mb-6">
					Webinar Registration Management
				</Heading>

				{/* Tabs */}
				<div className="flex flex-wrap gap-2 mb-6 border-b">
					<button
						type="button"
						onClick={() => setActiveTab("registration")}
						className={`px-4 py-2 border-b-2 transition-colors ${
							activeTab === "registration"
								? "border-blue-500 text-blue-11"
								: "border-transparent text-gray-10 hover:text-gray-12"
						}`}
					>
						Registration Page
					</button>
					<button
						type="button"
						onClick={() => setActiveTab("reminders")}
						className={`px-4 py-2 border-b-2 transition-colors ${
							activeTab === "reminders"
								? "border-blue-500 text-blue-11"
								: "border-transparent text-gray-10 hover:text-gray-12"
						}`}
					>
						Reminders
					</button>
					<button
						type="button"
						onClick={() => setActiveTab("attendance")}
						className={`px-4 py-2 border-b-2 transition-colors ${
							activeTab === "attendance"
								? "border-blue-500 text-blue-11"
								: "border-transparent text-gray-10 hover:text-gray-12"
						}`}
					>
						Attendance
					</button>
				</div>

				{/* Content */}
				{activeTab === "registration" && (
					<div>
						{showBuilder ? (
							<RegistrationPageBuilder
								webinarId={webinarId}
								experienceId={experienceId}
								companyId={companyId}
								onClose={() => setShowBuilder(false)}
							/>
						) : (
							<Card className="p-6">
								<Button onClick={() => setShowBuilder(true)}>
									Create/Edit Registration Page
								</Button>
							</Card>
						)}
					</div>
				)}

				{activeTab === "reminders" && (
					<ReminderScheduleManager webinarId={webinarId} companyId={companyId} />
				)}

				{activeTab === "attendance" && (
					<AttendanceView webinarId={webinarId} />
				)}
			</div>
		</div>
	);
}

