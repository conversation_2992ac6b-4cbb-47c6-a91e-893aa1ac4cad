import { headers } from "next/headers";
import { whopsdk } from "@/lib/whop-sdk";
import { supabaseServer } from "@/lib/supabase";
import { WebinarRegistrationManagement } from "./components/WebinarRegistrationManagement";

export default async function WebinarRegistrationPage({
	params,
}: {
	params: Promise<{ companyId: string; webinarId: string }>;
}) {
	const { companyId, webinarId } = await params;
	const { userId } = await whopsdk.verifyUserToken(await headers());

	// Get webinar to get experience_id
	const { data: webinar } = await supabaseServer
		.from("webinars")
		.select("experience_id")
		.eq("id", webinarId)
		.single();

	if (!webinar) {
		return (
			<div className="flex items-center justify-center min-h-screen p-8">
				<div className="text-center">
					<h1 className="text-2xl font-bold mb-4">Webinar Not Found</h1>
					<p className="text-gray-10">The webinar you're looking for doesn't exist.</p>
				</div>
			</div>
		);
	}

	return (
		<WebinarRegistrationManagement
			webinarId={webinarId}
			companyId={companyId}
			experienceId={webinar.experience_id}
		/>
	);
}
