"use client";

import { useState, useEffect } from "react";
import type { DailyCall } from "@daily-co/daily-js";
import { Card, Heading, Text, Badge, Button } from "@whop/react/components";
import { Users, Mic, MicOff, Shield, ShieldOff } from "lucide-react";

interface Participant {
	session_id: string;
	user_name: string;
	user_id?: string;
	local: boolean;
}

interface AttendeeListProps {
	callFrame: DailyCall | null;
	webinarId: string;
	isModerator: boolean;
}

export function AttendeeList({ callFrame, webinarId, isModerator }: AttendeeListProps) {
	const [participants, setParticipants] = useState<Participant[]>([]);
	const [moderationActions, setModerationActions] = useState<Record<string, { type: string; isActive: boolean }>>({});

	useEffect(() => {
		if (!callFrame) return;

		const updateParticipants = () => {
			const participantsObj = callFrame.participants();
			const participantsList = Object.values(participantsObj)
				.map((p) => ({
					session_id: p.session_id,
					user_name: p.user_name || "Unknown",
					user_id: (p.userData as any)?.userId,
					local: p.local || false,
				}))
				.filter((p) => !p.local)
				.sort((a, b) => a.user_name.localeCompare(b.user_name));

			setParticipants(participantsList);
		};

		updateParticipants();

		callFrame.on("participant-joined", updateParticipants);
		callFrame.on("participant-left", updateParticipants);
		callFrame.on("participant-updated", updateParticipants);

		return () => {
			callFrame.off("participant-joined", updateParticipants);
			callFrame.off("participant-left", updateParticipants);
			callFrame.off("participant-updated", updateParticipants);
		};
	}, [callFrame]);

	useEffect(() => {
		const fetchModerationActions = async () => {
			if (!webinarId || !isModerator) return;

			try {
				const response = await fetch(`/api/webinar-moderation/${webinarId}`);
				if (response.ok) {
					const data = await response.json();
					const actionsMap: Record<string, { type: string; isActive: boolean }> = {};
					data.actions?.forEach((action: any) => {
						if (action.is_active) {
							actionsMap[action.target_user_id] = {
								type: action.action_type,
								isActive: true,
							};
						}
					});
					setModerationActions(actionsMap);
				}
			} catch (error) {
				console.error("Error fetching moderation actions:", error);
			}
		};

		fetchModerationActions();
		const interval = setInterval(fetchModerationActions, 5000);
		return () => clearInterval(interval);
	}, [webinarId, isModerator]);

	const performModerationAction = async (
		targetUserId: string,
		actionType: "mute" | "unmute" | "block" | "unblock",
	) => {
		if (!webinarId) return;

		try {
			const response = await fetch(`/api/webinar-moderation/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					actionType,
					targetUserId,
				}),
			});

			if (response.ok) {
				if (actionType === "unmute" || actionType === "unblock") {
					const newActions = { ...moderationActions };
					delete newActions[targetUserId];
					setModerationActions(newActions);
				} else {
					setModerationActions({
						...moderationActions,
						[targetUserId]: { type: actionType, isActive: true },
					});
				}
			}
		} catch (error) {
			console.error("Error performing moderation action:", error);
		}
	};

	return (
		<div className="flex h-full flex-col bg-transparent">
			<div className="flex items-center justify-between px-4 sm:px-6 py-3 bg-gray-a3/40 backdrop-blur-sm">
				<Heading size="5" weight="bold" className="flex items-center gap-2 text-gray-12">
					<Users className="h-5 w-5" />
					Attendees ({participants.length})
				</Heading>
			</div>
			<div className="flex-1 px-4 sm:px-6 py-4 overflow-hidden">
				<div className="flex flex-col gap-3 h-full overflow-y-auto">
				{participants.length === 0 ? (
						<div className="flex flex-col items-center justify-center py-12 text-center">
							<div className="mb-5 flex h-20 w-20 items-center justify-center rounded-xl bg-gray-a4/50 backdrop-blur-sm shadow-sm">
								<Users className="h-10 w-10 text-gray-9" />
							</div>
							<Text size="4" weight="semi-bold" className="mb-2 text-gray-12">
						No other participants
					</Text>
							<Text size="2" className="text-gray-11 max-w-xs">
								Waiting for others to join...
							</Text>
						</div>
				) : (
					participants.map((participant) => {
						const userId = participant.user_id || participant.session_id;
						const action = moderationActions[userId];
						const isMuted = action?.type === "mute" && action.isActive;
						const isBlocked = action?.type === "block" && action.isActive;

						return (
							<div
								key={participant.session_id}
								className="flex items-center justify-between p-3 rounded-lg bg-gray-a3/40 backdrop-blur-sm hover:bg-gray-a4/50 transition-all shadow-sm gap-2"
							>
								<div className="flex items-center gap-2 flex-1 min-w-0">
									<Text size="2" weight="medium" className="truncate">
										{participant.user_name}
									</Text>
									{isMuted && (
										<Badge variant="soft" color="warning" size="1">
											Muted
										</Badge>
									)}
									{isBlocked && (
										<Badge variant="soft" color="danger" size="1">
											Blocked
										</Badge>
									)}
								</div>
								{isModerator && (
									<div className="flex gap-1">
										{isMuted ? (
											<Button
												size="1"
												variant="soft"
												onClick={() => performModerationAction(userId, "unmute")}
												className="min-h-[32px] min-w-[32px] p-1"
												title="Unmute"
											>
												<Mic className="h-4 w-4" />
											</Button>
										) : (
											<Button
												size="1"
												variant="soft"
												onClick={() => performModerationAction(userId, "mute")}
												className="min-h-[32px] min-w-[32px] p-1"
												title="Mute"
											>
												<MicOff className="h-4 w-4" />
											</Button>
										)}
										{isBlocked ? (
											<Button
												size="1"
												variant="soft"
												color="success"
												onClick={() => performModerationAction(userId, "unblock")}
												className="min-h-[32px] min-w-[32px] p-1"
												title="Unblock"
											>
												<Shield className="h-4 w-4" />
											</Button>
										) : (
											<Button
												size="1"
												variant="soft"
												color="danger"
												onClick={() => performModerationAction(userId, "block")}
												className="min-h-[32px] min-w-[32px] p-1"
												title="Block"
											>
												<ShieldOff className="h-4 w-4" />
											</Button>
										)}
									</div>
								)}
							</div>
						);
					})
				)}
			</div>
			</div>
		</div>
	);
}

