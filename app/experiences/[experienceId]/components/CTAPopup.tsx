"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, Badge } from "@whop/react/components";
import { X, Clock } from "lucide-react";
import Image from "next/image";

interface CTA {
	id: string;
	title: string;
	description?: string;
	button_text: string;
	button_url: string;
	image_url?: string;
	countdown_seconds: number;
	show_countdown: boolean;
	duration_seconds: number;
	is_active: boolean;
}

interface CTAPopupProps {
	webinarId: string;
	userId: string;
	isModerator: boolean;
	webinarStartTime?: string; // ISO timestamp of when webinar started
}

export function CTAPopup({ webinarId, userId, isModerator, webinarStartTime }: CTAPopupProps) {
	const [activeCTA, setActiveCTA] = useState<CTA | null>(null);
	const [countdown, setCountdown] = useState<number>(0);
	const [timeRemaining, setTimeRemaining] = useState<number>(0);
	const [startTime, setStartTime] = useState<string | null>(null);
	const [previousCTAId, setPreviousCTAId] = useState<string | null>(null);

	const captureReplayInteraction = async (
		interactionType: string,
		interactionId: string,
		interactionData: Record<string, unknown>,
	) => {
		if (!startTime || !webinarId) return;

		try {
			const now = new Date();
			const start = new Date(startTime);
			const timestampOffset = (now.getTime() - start.getTime()) / 1000; // seconds

			await fetch(`/api/webinar-replay-data/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					interactionType,
					interactionId,
					timestampOffset,
					webinarStartTime: startTime,
					interactionData,
				}),
			});
		} catch (error) {
			console.error("Error capturing replay data:", error);
		}
	};

	useEffect(() => {
		if (webinarStartTime) {
			setStartTime(webinarStartTime);
		} else if (webinarId) {
			// Fetch webinar to get scheduled_at time
			fetch(`/api/webinars/${webinarId}`)
				.then((res) => res.json())
				.then((data) => {
					if (data.webinar?.scheduled_at) {
						setStartTime(data.webinar.scheduled_at);
					}
				})
				.catch((err) => console.error("Error fetching webinar start time:", err));
		}
	}, [webinarId, webinarStartTime]);

	useEffect(() => {
		const fetchActiveCTA = async () => {
			try {
				const response = await fetch(`/api/webinar-ctas/${webinarId}?activeOnly=true`);
				if (response.ok) {
					const data = await response.json();
					if (data.ctas && data.ctas.length > 0) {
						const cta = data.ctas[0];
						setActiveCTA(cta);
						setCountdown(cta.countdown_seconds || 60);
						setTimeRemaining(cta.duration_seconds || 30);
						
						// Capture timestamp when CTA becomes active
						if (cta.id !== previousCTAId && startTime) {
							setPreviousCTAId(cta.id);
							captureReplayInteraction("cta", cta.id, {
								ctaId: cta.id,
								action: "shown",
								title: cta.title,
								buttonText: cta.button_text,
								buttonUrl: cta.button_url,
							});
						}
					} else {
						setActiveCTA(null);
					}
				}
			} catch (error) {
				console.error("Error fetching active CTA:", error);
			}
		};

		fetchActiveCTA();
		const interval = setInterval(fetchActiveCTA, 2000);
		return () => clearInterval(interval);
	}, [webinarId, startTime, previousCTAId]);

	useEffect(() => {
		if (!activeCTA) return;

		// Countdown timer
		if (activeCTA.show_countdown && countdown > 0) {
			const timer = setInterval(() => {
				setCountdown((prev) => {
					if (prev <= 1) {
						clearInterval(timer);
						return 0;
					}
					return prev - 1;
				});
			}, 1000);
			return () => clearInterval(timer);
		}

		// Auto-hide timer
		if (timeRemaining > 0) {
			const timer = setInterval(() => {
				setTimeRemaining((prev) => {
					if (prev <= 1) {
						clearInterval(timer);
						// Dismiss CTA
						if (activeCTA) {
							handleDismiss();
						}
						return 0;
					}
					return prev - 1;
				});
			}, 1000);
			return () => clearInterval(timer);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeCTA, countdown, timeRemaining]);

	const handleDismiss = async () => {
		if (!activeCTA) return;

		// Capture timestamp for dismissal
		if (startTime) {
			captureReplayInteraction("cta_interaction", activeCTA.id, {
				ctaId: activeCTA.id,
				action: "dismissed",
				userId,
			});
		}

		// Track dismissal
		await fetch(`/api/webinar-ctas/${webinarId}`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				action: "track",
				ctaId: activeCTA.id,
				interactionType: "dismissed",
			}),
		});

		setActiveCTA(null);
	};

	const handleClick = async () => {
		if (!activeCTA) return;

		// Capture timestamp for click
		if (startTime) {
			captureReplayInteraction("cta_interaction", activeCTA.id, {
				ctaId: activeCTA.id,
				action: "clicked",
				userId,
				buttonUrl: activeCTA.button_url,
			});
		}

		// Track click
		await fetch(`/api/webinar-ctas/${webinarId}`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				action: "track",
				ctaId: activeCTA.id,
				interactionType: "clicked",
			}),
		});

		// Open URL
		window.open(activeCTA.button_url, "_blank");
	};

	if (!activeCTA) return null;

	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4 animate-fade-in">
			<Card
				variant="surface"
				size="4"
				className="relative max-w-md w-full mx-auto animate-slide-up shadow-2xl"
			>
				{/* Close Button */}
				<button
					onClick={handleDismiss}
					className="absolute top-4 right-4 z-10 p-2 rounded-full bg-gray-a6 hover:bg-gray-a7 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
					aria-label="Close"
				>
					<X className="h-5 w-5" />
				</button>

				{/* Countdown Badge */}
				{activeCTA.show_countdown && countdown > 0 && (
					<div className="absolute top-4 left-4 z-10">
						<Badge variant="soft" color="danger" size="2" className="flex items-center gap-2 animate-pulse">
							<Clock className="h-4 w-4" />
							{countdown}s
						</Badge>
					</div>
				)}

				<div className="flex flex-col gap-4 p-6">
					{/* Image */}
					{activeCTA.image_url && (
						<div className="relative w-full h-48 rounded-lg overflow-hidden bg-gray-a4">
							<Image
								src={activeCTA.image_url}
								alt={activeCTA.title}
								fill
								className="object-cover"
							/>
						</div>
					)}

					{/* Content */}
					<div className="flex flex-col gap-3">
						<Heading size="5" weight="bold">
							{activeCTA.title}
						</Heading>
						{activeCTA.description && (
							<Text size="3" color="gray">
								{activeCTA.description}
							</Text>
						)}
					</div>

					{/* CTA Button */}
					<Button
						onClick={handleClick}
						variant="solid"
						color="blue"
						size="4"
						className="w-full font-semibold min-h-[48px] shadow-lg hover:shadow-xl transition-all"
					>
						{activeCTA.button_text}
					</Button>

					{/* Time Remaining */}
					{timeRemaining > 0 && (
						<Text size="1" color="gray" className="text-center">
							This offer will close in {timeRemaining} seconds
						</Text>
					)}
				</div>
			</Card>

			<style jsx>{`
				@keyframes fade-in {
					from {
						opacity: 0;
					}
					to {
						opacity: 1;
					}
				}
				@keyframes slide-up {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}
				.animate-fade-in {
					animation: fade-in 0.3s ease-out;
				}
				.animate-slide-up {
					animation: slide-up 0.3s ease-out;
				}
			`}</style>
		</div>
	);
}

