"use client";

import { useEffect, useRef, useState, type KeyboardEvent } from "react";
import type { DailyCall } from "@daily-co/daily-js";
import { <PERSON><PERSON>, Heading, ScrollArea, Text, TextField, DropdownMenu, Badge } from "@whop/react/components";
import { MessageSquare, Trash2, MoreVertical, User, Shield } from "lucide-react";

interface ChatMessage {
	id: string;
	user_name: string;
	user_id: string;
	message: string;
	timestamp: string;
	user_role: "host" | "co-host" | "moderator" | "attendee";
	is_highlighted: boolean;
	message_type: "public" | "private";
	recipient_user_id?: string;
}

interface ChatProps {
	callFrame: DailyCall | null;
	userName: string;
	userId: string;
	webinarId: string;
	userRole: "host" | "co-host" | "moderator" | "attendee";
	isModerator: boolean;
	dailySessionId?: string;
	webinarStartTime?: string; // ISO timestamp of when webinar started
}

export function Chat({ 
	callFrame, 
	userName, 
	userId, 
	webinarId, 
	userRole, 
	isModerator,
	dailySessionId,
	webinarStartTime 
}: ChatProps) {
	const [messages, setMessages] = useState<ChatMessage[]>([]);
	const [inputMessage, setInputMessage] = useState("");
	const [chatMode, setChatMode] = useState<"public" | "private">("public");
	const [selectedRecipient, setSelectedRecipient] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [startTime, setStartTime] = useState<string | null>(null);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

	// Fetch webinar start time if not provided
	useEffect(() => {
		if (webinarStartTime) {
			setStartTime(webinarStartTime);
		} else if (webinarId) {
			// Fetch webinar to get scheduled_at time
			fetch(`/api/webinars/${webinarId}`)
				.then((res) => res.json())
				.then((data) => {
					if (data.webinar?.scheduled_at) {
						setStartTime(data.webinar.scheduled_at);
					}
				})
				.catch((err) => console.error("Error fetching webinar start time:", err));
		}
	}, [webinarId, webinarStartTime]);

	// Fetch messages from API
	const fetchMessages = async () => {
		if (!webinarId) return;

		try {
			let url = `/api/webinar-chat/${webinarId}?type=${chatMode}`;
			if (chatMode === "private" && selectedRecipient) {
				url += `&recipientUserId=${selectedRecipient}`;
			}

			const response = await fetch(url);
			if (response.ok) {
				const data = await response.json();
				setMessages(data.messages || []);
			}
		} catch (error) {
			console.error("Error fetching messages:", error);
		}
	};

	// Poll for new messages
	useEffect(() => {
		fetchMessages();
		
		// Poll every 2 seconds for new messages
		pollingIntervalRef.current = setInterval(() => {
			fetchMessages();
		}, 2000);

		return () => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
			}
		};
	}, [webinarId, chatMode, selectedRecipient]);

	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, [messages]);

	const sendMessage = async () => {
		if (!inputMessage.trim() || !webinarId) return;

		setIsLoading(true);
		try {
			const response = await fetch(`/api/webinar-chat/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					message: inputMessage,
					messageType: chatMode,
					recipientUserId: chatMode === "private" ? selectedRecipient : undefined,
					userRole,
					dailySessionId,
				}),
			});

			if (response.ok) {
				const data = await response.json();
				const messageId = data.message?.id;
				
				// Capture timestamp for replay synchronization
				if (messageId && startTime) {
					const now = new Date();
					const start = new Date(startTime);
					const timestampOffset = (now.getTime() - start.getTime()) / 1000; // seconds
					
					// Store interaction data for replay
					try {
						await fetch(`/api/webinar-replay-data/${webinarId}`, {
							method: "POST",
							headers: {
								"Content-Type": "application/json",
							},
							body: JSON.stringify({
								interactionType: "chat",
								interactionId: messageId,
								timestampOffset,
								webinarStartTime: startTime,
								interactionData: {
									message: inputMessage,
									userName,
									userId,
									userRole,
									messageType: chatMode,
									recipientUserId: chatMode === "private" ? selectedRecipient : undefined,
								},
							}),
						});
					} catch (replayError) {
						// Don't fail message send if replay capture fails
						console.error("Error capturing replay data:", replayError);
					}
				}
				
				setInputMessage("");
				// Refresh messages
				await fetchMessages();
			} else {
				const error = await response.json();
				alert(error.error || "Failed to send message");
			}
		} catch (error) {
			console.error("Error sending message:", error);
			alert("Failed to send message");
		} finally {
			setIsLoading(false);
		}
	};

	const deleteMessage = async (messageId: string) => {
		if (!confirm("Are you sure you want to delete this message?")) return;

		try {
			const response = await fetch(`/api/webinar-chat/${webinarId}/${messageId}`, {
				method: "DELETE",
			});

			if (response.ok) {
				await fetchMessages();
			} else {
				const error = await response.json();
				alert(error.error || "Failed to delete message");
			}
		} catch (error) {
			console.error("Error deleting message:", error);
			alert("Failed to delete message");
		}
	};

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			sendMessage();
		}
	};

	const formatTime = (timestamp: string) => {
		const date = new Date(timestamp);
		return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
	};

	const getRoleBadgeColor = (role: string) => {
		switch (role) {
			case "host":
				return "blue";
			case "co-host":
				return "indigo";
			case "moderator":
				return "purple";
			default:
				return "gray";
		}
	};

	const getRoleLabel = (role: string) => {
		switch (role) {
			case "host":
				return "Host";
			case "co-host":
				return "Co-Host";
			case "moderator":
				return "Moderator";
			default:
				return "";
		}
	};

	// For private chat, we need to get list of hosts/moderators
	// For now, we'll show a simple toggle - in production, you'd fetch hosts from the webinar
	const canSendPrivateChat = chatMode === "private" && selectedRecipient && !isModerator;

	return (
		<div className="flex h-full flex-col bg-transparent">
			{/* Header */}
			<div className="flex items-center justify-between bg-gray-a5/80 backdrop-blur-xl border-b border-white/5 px-4 sm:px-5 py-3 sm:py-4 flex-shrink-0">
				<div className="flex items-center gap-3">
					<Heading size="6" weight="bold" className="text-gray-12">
						Chat
					</Heading>
					{chatMode === "private" && (
						<Badge variant="soft" color="blue" size="2">
							Private
						</Badge>
					)}
				</div>
				{isModerator && (
					<Button
						variant="soft"
						size="2"
						onClick={() => setChatMode(chatMode === "public" ? "private" : "public")}
					>
						{chatMode === "public" ? "Private" : "Public"}
					</Button>
				)}
			</div>
			{/* Messages Area */}
			<div className="flex-1 px-4 sm:px-5 py-4 overflow-hidden bg-transparent">
				<ScrollArea className="h-full pr-2" scrollbars="vertical">
					<div className="flex flex-col gap-2.5">
						{messages.length === 0 ? (
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<div className="mb-5 flex h-20 w-20 items-center justify-center rounded-full bg-gray-a3/40 backdrop-blur-sm">
									<MessageSquare className="h-10 w-10 text-gray-9" />
								</div>
								<Text size="4" weight="semi-bold" className="mb-2 text-gray-12">
									No messages yet
								</Text>
								<Text size="2" className="text-gray-11 max-w-xs">
									Be the first to start the conversation!
								</Text>
							</div>
						) : (
							messages.map((msg) => (
								<div
									key={msg.id}
									className={`flex flex-col gap-1.5 py-2 transition-all ${
										msg.is_highlighted
											? "border-l-2 border-l-blue-9 pl-3"
											: ""
									}`}
								>
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											{msg.user_role !== "attendee" && (
												<Shield className="h-4 w-4 text-blue-9" />
											)}
											<Text 
												size="2" 
												weight="semi-bold" 
												highContrast 
												className={msg.is_highlighted ? "text-blue-10" : "text-gray-12"}
											>
												{msg.user_name}
											</Text>
											{msg.user_role !== "attendee" && (
												<Badge variant="soft" color={getRoleBadgeColor(msg.user_role)} size="1">
													{getRoleLabel(msg.user_role)}
												</Badge>
											)}
											{msg.message_type === "private" && (
												<Badge variant="soft" color="gray" size="1">
													Private
												</Badge>
											)}
										</div>
										<div className="flex items-center gap-2">
											<Text size="1" color="gray">
												{formatTime(msg.timestamp)}
											</Text>
											{(isModerator || msg.user_id === userId) && (
												<DropdownMenu.Root>
													<DropdownMenu.Trigger>
														<Button
															variant="ghost"
															size="1"
															className="h-6 w-6 p-0"
														>
															<MoreVertical className="h-3 w-3" />
														</Button>
													</DropdownMenu.Trigger>
													<DropdownMenu.Content>
														<DropdownMenu.Item
															onClick={() => deleteMessage(msg.id)}
															className="text-danger"
														>
															<Trash2 className="h-4 w-4 mr-2" />
															Delete
														</DropdownMenu.Item>
													</DropdownMenu.Content>
												</DropdownMenu.Root>
											)}
										</div>
									</div>
									<Text 
										size="2" 
										color="gray" 
										className="whitespace-pre-wrap leading-relaxed"
									>
										{msg.message}
									</Text>
								</div>
							))
						)}
						<div ref={messagesEndRef} />
					</div>
				</ScrollArea>
			</div>
			{/* Input Area */}
			<div className="bg-gray-a5/80 backdrop-blur-xl border-t border-white/5 px-4 sm:px-5 py-3 flex-shrink-0">
				<div className="flex gap-2">
					<TextField.Root className="flex-1" variant="surface" size="2" color="gray">
						<TextField.Input
							value={inputMessage}
							onChange={(e) => setInputMessage(e.target.value)}
							onKeyDown={handleKeyDown}
							placeholder={chatMode === "private" ? "Type a private message..." : "Type a message..."}
							disabled={isLoading}
							className="w-full"
						/>
					</TextField.Root>
					<Button
						onClick={sendMessage}
						disabled={!inputMessage.trim() || isLoading}
						variant="solid"
						color="blue"
						size="2"
						className="px-3 py-2 h-9"
					>
						Send
					</Button>
				</div>
			</div>
		</div>
	);
}
