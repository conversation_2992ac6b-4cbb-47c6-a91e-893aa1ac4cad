"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>ing, Text, TextField, Card, Select, Checkbox, IconButton } from "@whop/react/components";
import { X, Loader2, Plus, Trash2, Eye, EyeOff } from "lucide-react";
import ColorPicker from "@rc-component/color-picker";
import "@rc-component/color-picker/assets/index.css";
import { useCreateWebinar, useUpdateWebinar } from "@/lib/queries/webinars";
import type { Webinar, PresenterRole } from "@/lib/supabase";

interface CreateWebinarFormProps {
	experienceId: string;
	companyId: string;
	initialData?: Webinar;
	onClose: () => void;
}

const EVENT_TYPES = [
	{ value: "live", label: "Live Event", description: "One-time scheduled event" },
	{ value: "right_now", label: "Right Now", description: "Event happening immediately" },
	{ value: "recurring", label: "Recurring Event", description: "Repeats on a schedule" },
	{ value: "always_on", label: "Always On", description: "Available 24/7, no end time" },
	{ value: "evergreen_room", label: "Evergreen Room", description: "Permanent room for ongoing sessions" },
] as const;

const PRESENTER_ROLES: { value: PresenterRole; label: string }[] = [
	{ value: "host", label: "Host" },
	{ value: "co-host", label: "Co-Host" },
	{ value: "moderator", label: "Moderator" },
	{ value: "presenter", label: "Presenter" },
	{ value: "panelist", label: "Panelist" },
];

// Color Picker Component
function ColorPickerField({
	label,
	value,
	onChange,
}: {
	label: string;
	value: string;
	onChange: (color: string) => void;
}) {
	const [showPicker, setShowPicker] = useState(false);
	const pickerRef = useRef<HTMLDivElement>(null);

	// Close picker when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				pickerRef.current &&
				!pickerRef.current.contains(event.target as Node)
			) {
				setShowPicker(false);
			}
		};

		if (showPicker) {
			document.addEventListener("mousedown", handleClickOutside);
			return () => {
				document.removeEventListener("mousedown", handleClickOutside);
			};
		}
	}, [showPicker]);

	return (
		<div className="flex flex-col gap-2">
			<Text size="2" weight="semi-bold">
				{label}
			</Text>
			<div className="flex gap-2 items-center">
				<div className="relative" ref={pickerRef}>
					<button
						type="button"
						onClick={() => setShowPicker(!showPicker)}
						className="w-10 h-10 rounded-lg border border-gray-a6 cursor-pointer shadow-sm hover:shadow-md transition-all overflow-hidden relative group flex-shrink-0"
						style={{ backgroundColor: value }}
					>
						<div className="absolute inset-0 rounded-lg bg-gradient-to-br from-white/20 to-black/10 opacity-0 group-hover:opacity-100 transition-opacity" />
					</button>
					{showPicker && (
						<div className="absolute bottom-full left-0 mb-2 z-[100] bg-white rounded-lg shadow-2xl border border-gray-a3 p-2 min-w-[240px]">
							<div className="relative">
								<ColorPicker
									value={value}
									onChange={(color) => {
										// @rc-component/color-picker onChange returns a Color object
										try {
											const hexColor = typeof color === 'string' ? color : (color as any)?.toHexString?.() || value;
											onChange(hexColor);
										} catch {
											onChange(value);
										}
									}}
								/>
							</div>
							<div className="mt-1 flex justify-end">
								<Button
									type="button"
									variant="ghost"
									size="2"
									onClick={() => setShowPicker(false)}
								>
									Done
								</Button>
							</div>
						</div>
					)}
				</div>
				<TextField.Root className="flex-1">
					<TextField.Input
						value={value}
						onChange={(e) => {
							const val = e.target.value;
							if (/^#[0-9A-Fa-f]{0,6}$/.test(val)) {
								onChange(val);
							}
						}}
						placeholder="#FFFFFF"
					/>
				</TextField.Root>
			</div>
		</div>
	);
}

// Common timezones list
const TIMEZONES = [
	"UTC",
	"America/New_York",
	"America/Chicago",
	"America/Denver",
	"America/Los_Angeles",
	"America/Phoenix",
	"America/Toronto",
	"America/Vancouver",
	"Europe/London",
	"Europe/Paris",
	"Europe/Berlin",
	"Europe/Madrid",
	"Europe/Rome",
	"Asia/Tokyo",
	"Asia/Shanghai",
	"Asia/Hong_Kong",
	"Asia/Dubai",
	"Asia/Singapore",
	"Asia/Mumbai",
	"Australia/Sydney",
	"Australia/Melbourne",
	"Pacific/Auckland",
];

const CURRENCIES = [
	{ code: "USD", symbol: "$" },
	{ code: "EUR", symbol: "€" },
	{ code: "GBP", symbol: "£" },
	{ code: "JPY", symbol: "¥" },
	{ code: "CAD", symbol: "C$" },
	{ code: "AUD", symbol: "A$" },
];

interface Presenter {
	id: string;
	role: PresenterRole;
}

export function CreateWebinarForm({
	experienceId,
	companyId,
	initialData,
	onClose,
}: CreateWebinarFormProps) {
	const [showPassword, setShowPassword] = useState(false);
	const [showAdvanced, setShowAdvanced] = useState(false);
	
	const createMutation = useCreateWebinar();
	const updateMutation = useUpdateWebinar();
	const isLoading = createMutation.isPending || updateMutation.isPending;
	
	// Parse initial presenter roles
	const initialPresenters: Presenter[] = initialData?.host_ids.map((id) => ({
		id,
		role: (initialData.presenter_roles?.[id] as PresenterRole) || "host",
	})) || [];

	const [formData, setFormData] = useState({
		title: initialData?.title || "",
		description: initialData?.description || "",
		event_type: (initialData?.event_type || "live") as Webinar["event_type"],
		scheduled_at: initialData
			? new Date(initialData.scheduled_at).toISOString().slice(0, 16)
			: "",
		duration_minutes: initialData?.duration_minutes || 60,
		presenters: initialPresenters,
		recurrence_pattern: initialData?.recurrence_pattern || undefined,
		recurrence_interval: initialData?.recurrence_interval || 1,
		recurrence_end_date: initialData?.recurrence_end_date
			? new Date(initialData.recurrence_end_date).toISOString().slice(0, 16)
			: "",
		recurrence_count: initialData?.recurrence_count || undefined,
		room_name: initialData?.room_name || "",
		timezone: initialData?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC",
		is_password_protected: initialData?.is_password_protected || false,
		password: initialData?.password || "",
		is_paid: initialData?.is_paid || false,
		price_amount: initialData?.price_amount || 0,
		price_currency: initialData?.price_currency || "USD",
		custom_branding_logo_url: initialData?.custom_branding_logo_url || "",
		custom_branding_background_color: initialData?.custom_branding_background_color || "",
		custom_branding_text_color: initialData?.custom_branding_text_color || "",
		custom_branding_accent_color: initialData?.custom_branding_accent_color || "",
	});

	const requiresScheduledTime = formData.event_type !== "always_on" && formData.event_type !== "evergreen_room";
	const requiresDuration = formData.event_type !== "always_on" && formData.event_type !== "evergreen_room";
	const isRecurring = formData.event_type === "recurring";
	const isRightNow = formData.event_type === "right_now";

	const addPresenter = () => {
		setFormData({
			...formData,
			presenters: [...formData.presenters, { id: "", role: "host" }],
		});
	};

	const removePresenter = (index: number) => {
		setFormData({
			...formData,
			presenters: formData.presenters.filter((_, i) => i !== index),
		});
	};

	const updatePresenter = (index: number, field: keyof Presenter, value: string) => {
		const updated = [...formData.presenters];
		updated[index] = { ...updated[index], [field]: value };
		setFormData({ ...formData, presenters: updated });
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		try {
			// Build presenter roles map
			const presenterRoles: Record<string, string> = {};
			formData.presenters.forEach((presenter) => {
				if (presenter.id.trim()) {
					presenterRoles[presenter.id.trim()] = presenter.role;
				}
			});

			// Prepare submission data
			const submitData: any = {
				...formData,
				experience_id: experienceId,
				company_id: companyId,
				host_ids: formData.presenters.map((p) => p.id.trim()).filter(Boolean),
				presenter_roles: presenterRoles,
			};

			// Handle scheduled_at based on event type
			if (isRightNow) {
				submitData.scheduled_at = new Date().toISOString();
			} else if (requiresScheduledTime && formData.scheduled_at) {
				submitData.scheduled_at = new Date(formData.scheduled_at).toISOString();
			} else if (formData.event_type === "always_on" || formData.event_type === "evergreen_room") {
				submitData.scheduled_at = new Date().toISOString();
			}

			// Handle recurrence fields
			if (isRecurring) {
				if (formData.recurrence_end_date) {
					submitData.recurrence_end_date = new Date(formData.recurrence_end_date).toISOString();
				}
				if (!formData.recurrence_end_date && !formData.recurrence_count) {
					submitData.recurrence_count = null;
					submitData.recurrence_end_date = null;
				}
			} else {
				submitData.recurrence_pattern = null;
				submitData.recurrence_interval = null;
				submitData.recurrence_end_date = null;
				submitData.recurrence_count = null;
			}

			// Handle evergreen room name
			if (formData.event_type === "evergreen_room" && !formData.room_name) {
				submitData.room_name = `evergreen-${experienceId}-${Date.now()}`;
			}

			// Handle password protection
			if (!formData.is_password_protected) {
				submitData.password = null;
			}

			// Handle paid webinar
			if (!formData.is_paid) {
				submitData.price_amount = 0;
			}

			// Remove empty branding fields
			if (!submitData.custom_branding_logo_url) submitData.custom_branding_logo_url = null;
			if (!submitData.custom_branding_background_color) submitData.custom_branding_background_color = null;
			if (!submitData.custom_branding_text_color) submitData.custom_branding_text_color = null;
			if (!submitData.custom_branding_accent_color) submitData.custom_branding_accent_color = null;

			// Remove presenters array from submit data
			delete submitData.presenters;

			if (initialData) {
				await updateMutation.mutateAsync({ id: initialData.id, ...submitData });
			} else {
				await createMutation.mutateAsync(submitData);
			}

			onClose();
		} catch (error) {
			console.error("Error saving webinar:", error);
			alert(error instanceof Error ? error.message : "Failed to save webinar");
		}
	};

	return (
		<div className="max-w-4xl mx-auto pt-4 sm:pt-6 md:pt-8 pb-4 sm:pb-6 md:pb-8">
			<Card className="p-4 sm:p-6 md:p-8">
			<div className="flex items-center justify-between mb-6">
				<Heading size="6" weight="bold">
					{initialData ? "Edit Event" : "Create New Event"}
				</Heading>
				<Button variant="ghost" size="2" onClick={onClose}>
					<X className="h-5 w-5" />
				</Button>
			</div>

			<form onSubmit={handleSubmit} className="flex flex-col gap-6">
				{/* Basic Information */}
				<div className="flex flex-col gap-4">
					<Heading size="5" weight="bold">
						Basic Information
					</Heading>
					
					<div className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							Event Type *
						</Text>
						<Select.Root
							value={formData.event_type}
							onValueChange={(value) =>
								setFormData({
									...formData,
									event_type: value as Webinar["event_type"],
								})
							}
							required
						>
							<Select.Trigger placeholder="Select event type">
							</Select.Trigger>
							<Select.Content>
								{EVENT_TYPES.map((type) => (
									<Select.Item key={type.value} value={type.value}>
										{type.label} - {type.description}
									</Select.Item>
								))}
							</Select.Content>
						</Select.Root>
					</div>

					<div className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							Title *
						</Text>
						<TextField.Root>
							<TextField.Input
								value={formData.title}
								onChange={(e) => setFormData({ ...formData, title: e.target.value })}
								placeholder="Enter event title"
								required
							/>
						</TextField.Root>
					</div>

					<div className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							Description
						</Text>
						<TextField.Root>
							<TextField.Input
								value={formData.description}
								onChange={(e) =>
									setFormData({ ...formData, description: e.target.value })
								}
								placeholder="Enter event description (optional)"
							/>
						</TextField.Root>
					</div>

					{requiresScheduledTime && (
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									{isRightNow ? "Started At" : "Scheduled Date & Time"} *
								</Text>
								<TextField.Root>
									<TextField.Input
										type="datetime-local"
										value={formData.scheduled_at}
										onChange={(e) =>
											setFormData({ ...formData, scheduled_at: e.target.value })
										}
										required={!isRightNow}
										disabled={isRightNow}
									/>
								</TextField.Root>
							</div>

							{requiresDuration && (
								<div className="flex flex-col gap-2">
									<Text size="2" weight="semi-bold">
										Duration (minutes) *
									</Text>
									<TextField.Root>
										<TextField.Input
											type="number"
											value={formData.duration_minutes}
											onChange={(e) =>
												setFormData({
													...formData,
													duration_minutes: parseInt(e.target.value) || 60,
												})
											}
											min="1"
											required
										/>
									</TextField.Root>
								</div>
							)}
						</div>
					)}

					<div className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							Timezone *
						</Text>
						<Select.Root
							value={formData.timezone}
							onValueChange={(value) =>
								setFormData({ ...formData, timezone: value })
							}
							required
						>
							<Select.Trigger placeholder="Select timezone">
							</Select.Trigger>
							<Select.Content>
								{TIMEZONES.map((tz) => (
									<Select.Item key={tz} value={tz}>
										{tz}
									</Select.Item>
								))}
							</Select.Content>
						</Select.Root>
						<Text size="1" color="gray">
							Timezone for scheduling and display
						</Text>
					</div>
				</div>

				{/* Presenters Section */}
				<div className="flex flex-col gap-4">
					<div className="flex items-center justify-between">
						<Heading size="5" weight="bold">
							Presenters & Hosts
						</Heading>
						<Button
							type="button"
							variant="surface"
							size="2"
							onClick={addPresenter}
							className="flex items-center gap-2"
						>
							<Plus className="h-4 w-4" />
							Add Presenter
						</Button>
					</div>

					{formData.presenters.map((presenter, index) => (
						<div key={index} className="flex flex-col sm:flex-row gap-2 sm:items-end">
							<div className="flex-1 flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Presenter {index + 1} User ID
								</Text>
								<TextField.Root>
									<TextField.Input
										value={presenter.id}
										onChange={(e) =>
											updatePresenter(index, "id", e.target.value)
										}
										placeholder="Enter Whop user ID"
									/>
								</TextField.Root>
							</div>
							<div className="w-full sm:w-40 flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Role
								</Text>
								<Select.Root
									value={presenter.role}
									onValueChange={(value) =>
										updatePresenter(index, "role", value)
									}
								>
									<Select.Trigger placeholder="Select role">
									</Select.Trigger>
									<Select.Content>
										{PRESENTER_ROLES.map((role) => (
											<Select.Item key={role.value} value={role.value}>
												{role.label}
											</Select.Item>
										))}
									</Select.Content>
								</Select.Root>
							</div>
							{formData.presenters.length > 1 && (
								<Button
									type="button"
									variant="ghost"
									size="3"
									onClick={() => removePresenter(index)}
									className="text-red-11 hover:text-red-12 min-h-[44px]"
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							)}
						</div>
					))}

					{formData.presenters.length === 0 && (
						<Text size="2" color="gray">
							No presenters added. Click "Add Presenter" to add hosts or presenters.
						</Text>
					)}
				</div>

				{/* Security & Access */}
				<div className="flex flex-col gap-4">
					<Heading size="5" weight="bold">
						Security & Access
					</Heading>

					<div className="flex items-center gap-3">
						<Checkbox
							id="password-protected"
							checked={formData.is_password_protected}
							onCheckedChange={(checked) =>
								setFormData({
									...formData,
									is_password_protected: checked === true,
									password: checked === true ? formData.password : "",
								})
							}
						>
							<Text size="2" weight="semi-bold">
								Password Protected
							</Text>
						</Checkbox>
					</div>

					{formData.is_password_protected && (
						<div className="flex flex-col gap-2">
							<Text size="2" weight="semi-bold">
								Password *
							</Text>
							<div className="relative">
								<TextField.Root>
									<TextField.Input
										type={showPassword ? "text" : "password"}
										value={formData.password}
										onChange={(e) =>
											setFormData({ ...formData, password: e.target.value })
										}
										placeholder="Enter password for webinar"
										required={formData.is_password_protected}
									/>
									<IconButton
										type="button"
										variant="ghost"
										size="1"
										onClick={() => setShowPassword(!showPassword)}
										className="absolute right-2 top-1/2 -translate-y-1/2"
									>
										{showPassword ? (
											<EyeOff className="h-4 w-4" />
										) : (
											<Eye className="h-4 w-4" />
										)}
									</IconButton>
								</TextField.Root>
							</div>
						</div>
					)}

					<div className="flex items-center gap-3">
						<Checkbox
							id="paid-webinar"
							checked={formData.is_paid}
							onCheckedChange={(checked) =>
								setFormData({
									...formData,
									is_paid: checked === true,
									price_amount: checked === true ? formData.price_amount : 0,
								})
							}
						>
							<Text size="2" weight="semi-bold">
								Paid Webinar
							</Text>
						</Checkbox>
					</div>

					{formData.is_paid && (
						<div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
							<div className="col-span-1 sm:col-span-2 flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Price *
								</Text>
								<TextField.Root>
									<TextField.Input
										type="number"
										value={formData.price_amount}
										onChange={(e) =>
											setFormData({
												...formData,
												price_amount: parseFloat(e.target.value) || 0,
											})
										}
										min="0"
										step="0.01"
										required={formData.is_paid}
									/>
								</TextField.Root>
							</div>
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Currency *
								</Text>
								<Select.Root
									value={formData.price_currency}
									onValueChange={(value) =>
										setFormData({ ...formData, price_currency: value })
									}
									required={formData.is_paid}
								>
									<Select.Trigger placeholder="Select currency">
									</Select.Trigger>
									<Select.Content>
										{CURRENCIES.map((curr) => (
											<Select.Item key={curr.code} value={curr.code}>
												{curr.code} ({curr.symbol})
											</Select.Item>
										))}
									</Select.Content>
								</Select.Root>
							</div>
						</div>
					)}
				</div>

				{/* Recurrence Settings */}
				{isRecurring && (
					<div className="flex flex-col gap-4 p-4 border border-gray-a6 rounded-lg">
						<Text size="2" weight="semi-bold">
							Recurrence Settings
						</Text>
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Repeat Pattern *
								</Text>
								<Select.Root
									value={formData.recurrence_pattern || ""}
									onValueChange={(value) =>
										setFormData({
											...formData,
											recurrence_pattern: value as "daily" | "weekly" | "monthly" | undefined,
										})
									}
									required
								>
									<Select.Trigger placeholder="Select pattern">
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="daily">Daily</Select.Item>
										<Select.Item value="weekly">Weekly</Select.Item>
										<Select.Item value="monthly">Monthly</Select.Item>
									</Select.Content>
								</Select.Root>
							</div>
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Repeat Every
								</Text>
								<TextField.Root>
									<TextField.Input
										type="number"
										value={formData.recurrence_interval}
										onChange={(e) =>
											setFormData({
												...formData,
												recurrence_interval: parseInt(e.target.value) || 1,
											})
										}
										min="1"
										required
									/>
								</TextField.Root>
								<Text size="1" color="gray">
									{formData.recurrence_pattern === "daily" && "days"}
									{formData.recurrence_pattern === "weekly" && "weeks"}
									{formData.recurrence_pattern === "monthly" && "months"}
								</Text>
							</div>
						</div>
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									End Date (optional)
								</Text>
								<TextField.Root>
									<TextField.Input
										type="datetime-local"
										value={formData.recurrence_end_date}
										onChange={(e) =>
											setFormData({
												...formData,
												recurrence_end_date: e.target.value,
												recurrence_count: undefined,
											})
										}
									/>
								</TextField.Root>
							</div>
							<div className="flex flex-col gap-2">
								<Text size="2" weight="semi-bold">
									Number of Occurrences (optional)
								</Text>
								<TextField.Root>
									<TextField.Input
										type="number"
										value={formData.recurrence_count || ""}
										onChange={(e) =>
											setFormData({
												...formData,
												recurrence_count: e.target.value ? parseInt(e.target.value) : undefined,
												recurrence_end_date: "",
											})
										}
										min="1"
									/>
								</TextField.Root>
							</div>
						</div>
					</div>
				)}

				{/* Advanced Settings */}
				<div className="flex flex-col gap-4">
					<button
						type="button"
						onClick={() => setShowAdvanced(!showAdvanced)}
						className="flex items-center justify-between text-left w-full p-0 hover:opacity-80 transition-opacity border-none bg-transparent"
					>
						<Heading size="5" weight="bold" className="text-gray-12">
							Advanced Settings
						</Heading>
						<Text size="2" color="gray" className="font-medium">
							{showAdvanced ? "Hide" : "Show"}
						</Text>
					</button>

					{showAdvanced && (
						<div className="flex flex-col gap-4 pt-2">
							{formData.event_type === "evergreen_room" && (
								<div className="flex flex-col gap-2">
									<Text size="2" weight="semi-bold">
										Room Name
									</Text>
									<TextField.Root>
										<TextField.Input
											value={formData.room_name}
											onChange={(e) =>
												setFormData({ ...formData, room_name: e.target.value })
											}
											placeholder="Leave empty for auto-generated name"
										/>
									</TextField.Root>
								</div>
							)}

							<div className="flex flex-col gap-4">
								<Heading size="4" weight="bold" className="border-none">
									Custom Branding
								</Heading>

								<div className="flex flex-col gap-2">
									<Text size="2" weight="semi-bold">
										Logo URL
									</Text>
									<TextField.Root className="border-none">
										<TextField.Input
											type="url"
											value={formData.custom_branding_logo_url}
											onChange={(e) =>
												setFormData({
													...formData,
													custom_branding_logo_url: e.target.value,
												})
											}
											placeholder="https://example.com/logo.png"
											className="border-none focus:ring-0 focus:outline-none"
										/>
									</TextField.Root>
									{formData.custom_branding_logo_url && (
										<div className="mt-2">
											<img
												src={formData.custom_branding_logo_url}
												alt="Branding logo preview"
												className="h-16 object-contain"
												onError={(e) => {
													(e.target as HTMLImageElement).style.display = "none";
												}}
											/>
										</div>
									)}
								</div>

								<div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-0">
									<ColorPickerField
										label="Background Color"
												value={formData.custom_branding_background_color || "#ffffff"}
										onChange={(color) =>
													setFormData({
														...formData,
												custom_branding_background_color: color,
													})
												}
											/>
									<ColorPickerField
										label="Text Color"
												value={formData.custom_branding_text_color || "#000000"}
										onChange={(color) =>
													setFormData({
														...formData,
												custom_branding_text_color: color,
													})
												}
											/>
									<ColorPickerField
										label="Accent Color"
												value={formData.custom_branding_accent_color || "#3b82f6"}
										onChange={(color) =>
													setFormData({
														...formData,
												custom_branding_accent_color: color,
														})
													}
												/>
										</div>
									</div>
								</div>
					)}
				</div>

				<div className="flex flex-col sm:flex-row gap-3 justify-end pt-4">
					<Button variant="surface" onClick={onClose} disabled={isLoading}>
						Cancel
					</Button>
					<Button
						type="submit"
						variant="solid"
						color="blue"
						disabled={
							isLoading ||
							!formData.title ||
							(requiresScheduledTime && !formData.scheduled_at && !isRightNow) ||
							(isRecurring && !formData.recurrence_pattern) ||
							(formData.is_password_protected && !formData.password) ||
							(formData.is_paid && formData.price_amount <= 0)
						}
					>
						{isLoading ? (
							<>
								<Loader2 className="h-4 w-4 animate-spin mr-2" />
								Saving...
							</>
						) : initialData ? (
							"Update Event"
						) : (
							"Create Event"
						)}
					</Button>
				</div>
			</form>
		</Card>
		</div>
	);
}
