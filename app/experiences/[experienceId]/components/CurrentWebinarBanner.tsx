"use client";

import { <PERSON>, <PERSON><PERSON>, Text, <PERSON><PERSON>, Badge } from "@whop/react/components";
import { Video, Clock, Users, Calendar } from "lucide-react";
import type { Webinar } from "@/lib/supabase";

interface CurrentWebinarBannerProps {
	webinar: Webinar;
	onJoin: (webinarId: string) => void;
}

export function CurrentWebinarBanner({
	webinar,
	onJoin,
}: CurrentWebinarBannerProps) {
	const scheduledDate = new Date(webinar.scheduled_at);
	const endTime = new Date(
		scheduledDate.getTime() + webinar.duration_minutes * 60 * 1000,
	);
	const formattedTime = scheduledDate.toLocaleTimeString("en-US", {
		hour: "numeric",
		minute: "2-digit",
	});

	return (
		<Card className="p-3 sm:p-4 bg-blue-a2/30 backdrop-blur-md border border-white/10 shadow-lg">
			{/* Mobile: Stack layout, Desktop: Side-by-side */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
				<div className="flex-1 min-w-0">
					<div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-2">
						<Badge color="blue" variant="soft" size="2" className="flex-shrink-0 bg-blue-a2/40 backdrop-blur-sm">
							LIVE NOW
						</Badge>
						<Heading size="4" weight="bold" className="sm:text-5 break-words">
							{webinar.title}
						</Heading>
					</div>
					{webinar.description && (
						<Text size="2" color="gray" className="mb-2 sm:mb-3 sm:text-3 break-words">
							{webinar.description}
						</Text>
					)}
					{/* Info grid - Mobile: single column, Desktop: wrap */}
					<div className="flex flex-col sm:flex-row sm:flex-wrap gap-2 sm:gap-3">
						<div className="flex items-center gap-2 text-gray-11">
							<Clock className="h-4 w-4 flex-shrink-0" />
							<Text size="1" className="sm:text-2">Started at {formattedTime}</Text>
						</div>
						<div className="flex items-center gap-2 text-gray-11">
							<Calendar className="h-4 w-4 flex-shrink-0" />
							<Text size="1" className="sm:text-2">{webinar.duration_minutes} minutes</Text>
						</div>
						{webinar.host_ids.length > 0 && (
							<div className="flex items-center gap-2 text-gray-11">
								<Users className="h-4 w-4 flex-shrink-0" />
								<Text size="1" className="sm:text-2">{webinar.host_ids.length} host(s)</Text>
							</div>
						)}
					</div>
				</div>
				{/* Join button - Mobile: full width, Desktop: auto */}
				<div className="w-full sm:w-auto sm:ml-4">
					<Button
						variant="classic"
						color="blue"
						size="3"
						onClick={() => onJoin(webinar.id)}
						className="flex items-center justify-center gap-2 w-full sm:w-auto shadow-lg hover:shadow-xl transition-all font-semibold relative overflow-hidden bg-gradient-to-br from-blue-9 via-blue-10 to-blue-11 backdrop-blur-sm border border-white/20 px-4 sm:px-6 py-2 sm:py-3"
						style={{
							boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(0, 0, 0, 0.2)',
						}}
					>
						<span className="flex items-center gap-2 relative z-10">
							<Video className="h-4 w-4" />
							Join Webinar
						</span>
						{/* Frost overlay for depth */}
						<div className="absolute inset-0 bg-gradient-to-b from-white/20 via-transparent to-black/10 pointer-events-none rounded-lg" />
					</Button>
				</div>
			</div>
		</Card>
	);
}
