"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { WebinarRoom } from "./WebinarRoom";
import { WebinarListView } from "./WebinarListView";

interface ExperienceContentProps {
	experienceId: string;
	userId: string;
	userName: string;
	isHost: boolean;
	canCreateWebinars: boolean;
	companyId: string;
	initialWebinarId?: string | null;
}

export function ExperienceContent({
	experienceId,
	userId,
	userName,
	isHost,
	canCreateWebinars,
	companyId,
	initialWebinarId,
}: ExperienceContentProps) {
	const router = useRouter();
	const [webinarId, setWebinarId] = useState<string | null>(
		initialWebinarId || null,
	);
	const [roomData, setRoomData] = useState<{
		roomUrl: string;
		token: string;
		userRole?: "host" | "co-host" | "moderator" | "attendee";
	} | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const joinWebinar = async (targetWebinarId: string) => {
		setLoading(true);
		setError(null);

		try {
			// Fetch room data for this webinar
			const response = await fetch(`/api/daily/${experienceId}?webinarId=${targetWebinarId}`);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to join webinar");
			}

			const data = await response.json();
			setRoomData({
				roomUrl: data.roomUrl,
				token: data.token,
				userRole: data.userRole || (isHost ? "host" : "attendee"),
			});
			setWebinarId(targetWebinarId);
			// Update URL with webinarId
			router.push(`/experiences/${experienceId}?webinarId=${targetWebinarId}`);
		} catch (err) {
			console.error("Error joining webinar:", err);
			setError(err instanceof Error ? err.message : "Failed to join webinar");
			setLoading(false);
		}
	};

	const handleJoinWebinar = (targetWebinarId: string) => {
		joinWebinar(targetWebinarId);
	};

	const handleLeave = () => {
		setRoomData(null);
		setWebinarId(null);
		setError(null);
		// Navigate back to webinar list
		router.push(`/experiences/${experienceId}`);
	};

	if (roomData && webinarId) {
		return (
			<WebinarRoom
				roomUrl={roomData.roomUrl}
				token={roomData.token}
				userName={userName}
				userId={userId}
				isHost={isHost}
				webinarId={webinarId}
				userRole={roomData.userRole}
				onLeave={handleLeave}
			/>
		);
	}

	if (loading) {
		return (
			<div className="flex items-center justify-center min-h-screen p-4">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-11 mx-auto mb-4" />
					<p className="text-gray-11 text-sm sm:text-base">Joining webinar...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex flex-col items-center justify-center min-h-screen p-4 sm:p-8">
				<div className="max-w-md text-center w-full">
					<h1 className="text-6 sm:text-8 font-bold text-gray-12 mb-3 sm:mb-4">Error</h1>
					<p className="text-3 sm:text-4 text-gray-10 mb-4 break-words">{error}</p>
					<button
						onClick={() => {
							setError(null);
							setWebinarId(null);
						}}
						className="text-blue-11 hover:text-blue-12 text-sm sm:text-base min-h-[44px] px-4 py-2"
					>
						Go back
					</button>
				</div>
			</div>
		);
	}

	return (
		<WebinarListView
			experienceId={experienceId}
			companyId={companyId}
			isHost={isHost}
			canCreateWebinars={canCreateWebinars}
			onJoinWebinar={handleJoinWebinar}
		/>
	);
}
