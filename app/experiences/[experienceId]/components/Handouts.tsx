"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, Badge } from "@whop/react/components";
import { Download, File, FileArchive, Trash2 } from "lucide-react";

interface Handout {
	id: string;
	file_name: string;
	file_url: string;
	file_type: string;
	file_size?: number;
	description?: string;
	download_count: number;
}

interface HandoutsProps {
	webinarId: string;
	isModerator: boolean;
}

export function Handouts({ webinarId, isModerator }: HandoutsProps) {
	const [handouts, setHandouts] = useState<Handout[]>([]);

	useEffect(() => {
		if (!webinarId) return;

		const fetchHandouts = async () => {
			try {
				const response = await fetch(`/api/webinar-handouts/${webinarId}`);
				if (response.ok) {
					const data = await response.json();
					setHandouts(data.handouts || []);
				}
			} catch (error) {
				console.error("Error fetching handouts:", error);
			}
		};

		fetchHandouts();
		const interval = setInterval(fetchHandouts, 5000);
		return () => clearInterval(interval);
	}, [webinarId]);

	const handleDownload = async (handout: Handout) => {
		try {
			const response = await fetch(`/api/webinar-handouts/${webinarId}/${handout.id}/download`, {
				method: "POST",
			});

			if (response.ok) {
				const data = await response.json();
				// Open download URL
				window.open(data.downloadUrl, "_blank");
				// Refresh handouts to update download count
				const fetchResponse = await fetch(`/api/webinar-handouts/${webinarId}`);
				if (fetchResponse.ok) {
					const fetchData = await fetchResponse.json();
					setHandouts(fetchData.handouts || []);
				}
			}
		} catch (error) {
			console.error("Error downloading handout:", error);
		}
	};

	const handleDelete = async (handoutId: string) => {
		if (!confirm("Are you sure you want to delete this handout?")) return;

		try {
			const response = await fetch(`/api/webinar-handouts/${webinarId}/${handoutId}`, {
				method: "DELETE",
			});

			if (response.ok) {
				setHandouts(handouts.filter((h) => h.id !== handoutId));
			}
		} catch (error) {
			console.error("Error deleting handout:", error);
		}
	};

	const formatFileSize = (bytes?: number) => {
		if (!bytes) return "Unknown size";
		if (bytes < 1024) return `${bytes} B`;
		if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
		return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
	};

	const getFileIcon = (fileType: string) => {
		if (fileType === "zip" || fileType === "archive") {
			return <FileArchive className="h-5 w-5" />;
		}
		return <File className="h-5 w-5" />;
	};

	if (handouts.length === 0 && !isModerator) {
		return null;
	}

	return (
		<div className="flex h-full flex-col bg-transparent">
			<div className="flex items-center justify-between bg-gray-a3/40 backdrop-blur-sm px-4 sm:px-6 py-3">
				<Heading size="5" weight="bold" className="text-gray-12">
					Handouts
				</Heading>
			</div>
			<div className="flex-1 px-4 sm:px-6 py-4 overflow-hidden">
				<div className="flex flex-col gap-3 h-full overflow-y-auto">
				{handouts.length === 0 ? (
						<div className="flex flex-col items-center justify-center py-12 text-center">
							<div className="mb-5 flex h-20 w-20 items-center justify-center rounded-xl bg-gray-a4/50 backdrop-blur-sm shadow-sm">
								<File className="h-10 w-10 text-gray-9" />
							</div>
							<Text size="4" weight="semi-bold" className="mb-2 text-gray-12">
						No handouts available
					</Text>
							<Text size="2" className="text-gray-11 max-w-xs">
								Files will appear here when uploaded
							</Text>
						</div>
				) : (
					handouts.map((handout) => (
						<div
							key={handout.id}
								className="flex items-center justify-between p-3 rounded-lg bg-gray-a3/40 backdrop-blur-sm hover:bg-gray-a4/50 transition-all shadow-sm gap-3"
						>
							<div className="flex items-center gap-3 flex-1 min-w-0">
								{getFileIcon(handout.file_type)}
								<div className="flex-1 min-w-0">
									<Text size="2" weight="medium" className="truncate">
										{handout.file_name}
									</Text>
									{handout.description && (
										<Text size="1" color="gray" className="truncate">
											{handout.description}
										</Text>
									)}
									<div className="flex items-center gap-2 mt-1">
										<Text size="1" color="gray">
											{formatFileSize(handout.file_size)}
										</Text>
										{handout.download_count > 0 && (
											<Badge variant="soft" size="1">
												{handout.download_count} downloads
											</Badge>
										)}
									</div>
								</div>
							</div>
							<div className="flex gap-2">
								<Button
									size="2"
									variant="solid"
									color="blue"
									onClick={() => handleDownload(handout)}
									className="min-h-[44px] min-w-[44px] p-2"
									title="Download"
								>
									<Download className="h-4 w-4" />
								</Button>
								{isModerator && (
									<Button
										size="2"
										variant="soft"
										color="danger"
										onClick={() => handleDelete(handout.id)}
										className="min-h-[44px] min-w-[44px] p-2"
										title="Delete"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</div>
						</div>
					))
				)}
			</div>
			</div>
		</div>
	);
}

