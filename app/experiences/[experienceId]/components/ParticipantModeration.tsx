"use client";

import { useEffect, useState } from "react";
import type { DailyCall } from "@daily-co/daily-js";
import { <PERSON><PERSON>, Card, Heading, Text, DropdownMenu, Badge } from "@whop/react/components";
import { <PERSON>, Mic, MicOff, Shield, ShieldOff, UserX } from "lucide-react";

interface Participant {
	session_id: string;
	user_name: string;
	user_id?: string;
	local: boolean;
}

interface ParticipantModerationProps {
	callFrame: DailyCall | null;
	webinarId: string;
	isModerator: boolean;
}

export function ParticipantModeration({ callFrame, webinarId, isModerator }: ParticipantModerationProps) {
	const [participants, setParticipants] = useState<Participant[]>([]);
	const [moderationActions, setModerationActions] = useState<Record<string, { type: string; isActive: boolean }>>({});

	useEffect(() => {
		if (!callFrame) return;

		const updateParticipants = () => {
			const participantsObj = callFrame.participants();
			const participantsList = Object.values(participantsObj).map((p) => ({
				session_id: p.session_id,
				user_name: p.user_name || "Unknown",
				user_id: (p.userData as any)?.userId,
				local: p.local || false,
			}));
			setParticipants(participantsList.filter((p) => !p.local));
		};

		updateParticipants();

		callFrame.on("participant-joined", updateParticipants);
		callFrame.on("participant-left", updateParticipants);
		callFrame.on("participant-updated", updateParticipants);

		return () => {
			callFrame.off("participant-joined", updateParticipants);
			callFrame.off("participant-left", updateParticipants);
			callFrame.off("participant-updated", updateParticipants);
		};
	}, [callFrame]);

	useEffect(() => {
		// Fetch moderation actions
		const fetchModerationActions = async () => {
			if (!webinarId || !isModerator) return;

			try {
				const response = await fetch(`/api/webinar-moderation/${webinarId}`);
				if (response.ok) {
					const data = await response.json();
					const actionsMap: Record<string, { type: string; isActive: boolean }> = {};
					data.actions?.forEach((action: any) => {
						if (action.is_active) {
							actionsMap[action.target_user_id] = {
								type: action.action_type,
								isActive: true,
							};
						}
					});
					setModerationActions(actionsMap);
				}
			} catch (error) {
				console.error("Error fetching moderation actions:", error);
			}
		};

		fetchModerationActions();
		const interval = setInterval(fetchModerationActions, 5000);
		return () => clearInterval(interval);
	}, [webinarId, isModerator]);

	const performModerationAction = async (
		targetUserId: string,
		targetSessionId: string,
		actionType: "mute" | "unmute" | "block" | "unblock" | "kick" | "ban",
	) => {
		if (!webinarId) return;

		try {
			const response = await fetch(`/api/webinar-moderation/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					actionType,
					targetUserId,
				}),
			});

			if (response.ok) {
				// Note: Kick action is handled server-side via Daily.co REST API
				// The moderation action is stored in the database

				// Update local state
				if (actionType === "unmute" || actionType === "unblock") {
					const newActions = { ...moderationActions };
					delete newActions[targetUserId];
					setModerationActions(newActions);
				} else {
					setModerationActions({
						...moderationActions,
						[targetUserId]: { type: actionType, isActive: true },
					});
				}

				// Note: Mute/unmute actions are handled server-side via Daily.co REST API
				// The moderation action is stored in the database
			} else {
				const error = await response.json();
				alert(error.error || "Failed to perform moderation action");
			}
		} catch (error) {
			console.error("Error performing moderation action:", error);
			alert("Failed to perform moderation action");
		}
	};

	if (!isModerator) return null;

	return (
		<Card variant="surface" size="3" className="p-4">
			<div className="flex items-center justify-between mb-4">
				<Heading size="4" weight="bold">
					Participants ({participants.length})
				</Heading>
			</div>
			<div className="flex flex-col gap-2 max-h-64 overflow-y-auto">
				{participants.length === 0 ? (
					<Text size="2" color="gray" className="text-center py-4">
						No other participants
					</Text>
				) : (
					participants.map((participant) => {
						const userId = participant.user_id || participant.session_id;
						const action = moderationActions[userId];
						const isMuted = action?.type === "mute" && action.isActive;
						const isBlocked = action?.type === "block" && action.isActive;

						return (
							<div
								key={participant.session_id}
								className="flex items-center justify-between p-2 rounded-lg bg-gray-a4/50 hover:bg-gray-a4 transition-colors"
							>
								<div className="flex items-center gap-2 flex-1 min-w-0">
									<Text size="2" weight="medium" className="truncate">
										{participant.user_name}
									</Text>
									{isMuted && (
										<Badge variant="soft" color="warning" size="1">
											Muted
										</Badge>
									)}
									{isBlocked && (
										<Badge variant="soft" color="danger" size="1">
											Blocked
										</Badge>
									)}
								</div>
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										<Button variant="ghost" size="1" className="h-6 w-6 p-0">
											<Users className="h-3 w-3" />
										</Button>
									</DropdownMenu.Trigger>
									<DropdownMenu.Content>
										{isMuted ? (
											<DropdownMenu.Item
												onClick={() => performModerationAction(userId, participant.session_id, "unmute")}
											>
												<Mic className="h-4 w-4 mr-2" />
												Unmute
											</DropdownMenu.Item>
										) : (
											<DropdownMenu.Item
												onClick={() => performModerationAction(userId, participant.session_id, "mute")}
											>
												<MicOff className="h-4 w-4 mr-2" />
												Mute
											</DropdownMenu.Item>
										)}
										{isBlocked ? (
											<DropdownMenu.Item
												onClick={() => performModerationAction(userId, participant.session_id, "unblock")}
											>
												<Shield className="h-4 w-4 mr-2" />
												Unblock
											</DropdownMenu.Item>
										) : (
											<DropdownMenu.Item
												onClick={() => performModerationAction(userId, participant.session_id, "block")}
											>
												<ShieldOff className="h-4 w-4 mr-2" />
												Block
											</DropdownMenu.Item>
										)}
										<DropdownMenu.Item
											onClick={() => {
												if (confirm(`Are you sure you want to kick ${participant.user_name}?`)) {
													performModerationAction(userId, participant.session_id, "kick");
												}
											}}
											className="text-danger"
										>
											<UserX className="h-4 w-4 mr-2" />
											Kick
										</DropdownMenu.Item>
										<DropdownMenu.Item
											onClick={() => {
												if (confirm(`Are you sure you want to ban ${participant.user_name}? They will not be able to rejoin.`)) {
													performModerationAction(userId, participant.session_id, "ban");
												}
											}}
											className="text-danger"
										>
											<UserX className="h-4 w-4 mr-2" />
											Ban
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</div>
						);
					})
				)}
			</div>
		</Card>
	);
}

