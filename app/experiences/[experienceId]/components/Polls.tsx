"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, Badge, Progress, RadioGroup, Checkbox } from "@whop/react/components";
import { Play, Square, BarChart3 } from "lucide-react";

interface PollOption {
	id: string;
	text: string;
	order: number;
	count?: number;
	percentage?: number;
}

interface Poll {
	id: string;
	title: string;
	question: string;
	poll_type: "single" | "multiple" | "survey";
	options: PollOption[];
	is_active: boolean;
	userHasResponded: boolean;
	totalResponses?: number;
	show_results?: boolean;
}

interface PollsProps {
	webinarId: string;
	userId: string;
	isModerator: boolean;
	webinarStartTime?: string; // ISO timestamp of when webinar started
}

export function Polls({ webinarId, userId, isModerator, webinarStartTime }: PollsProps) {
	const [polls, setPolls] = useState<Poll[]>([]);
	const [selectedOptions, setSelectedOptions] = useState<Record<string, string[]>>({});
	const [startTime, setStartTime] = useState<string | null>(null);

	useEffect(() => {
		if (webinarStartTime) {
			setStartTime(webinarStartTime);
		} else if (webinarId) {
			// Fetch webinar to get scheduled_at time
			fetch(`/api/webinars/${webinarId}`)
				.then((res) => res.json())
				.then((data) => {
					if (data.webinar?.scheduled_at) {
						setStartTime(data.webinar.scheduled_at);
					}
				})
				.catch((err) => console.error("Error fetching webinar start time:", err));
		}
	}, [webinarId, webinarStartTime]);

	useEffect(() => {
		if (!webinarId) return;

		const fetchPolls = async () => {
			try {
				const response = await fetch(`/api/webinar-polls/${webinarId}?includeResults=${isModerator}`);
				if (response.ok) {
					const data = await response.json();
					setPolls(data.polls || []);
				}
			} catch (error) {
				console.error("Error fetching polls:", error);
			}
		};

		fetchPolls();
		const interval = setInterval(fetchPolls, 3000);
		return () => clearInterval(interval);
	}, [webinarId, isModerator]);

	const captureReplayInteraction = async (
		interactionType: string,
		interactionId: string,
		interactionData: Record<string, unknown>,
	) => {
		if (!startTime || !webinarId) return;

		try {
			const now = new Date();
			const start = new Date(startTime);
			const timestampOffset = (now.getTime() - start.getTime()) / 1000; // seconds

			await fetch(`/api/webinar-replay-data/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					interactionType,
					interactionId,
					timestampOffset,
					webinarStartTime: startTime,
					interactionData,
				}),
			});
		} catch (error) {
			console.error("Error capturing replay data:", error);
		}
	};

	const submitResponse = async (pollId: string) => {
		const selected = selectedOptions[pollId] || [];
		if (selected.length === 0) return;

		try {
			const response = await fetch(`/api/webinar-polls/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					action: "respond",
					pollId,
					selectedOptions: selected,
				}),
			});

			if (response.ok) {
				// Capture timestamp for poll response
				captureReplayInteraction("poll_response", pollId, {
					pollId,
					userId,
					selectedOptions: selected,
				});

				// Refresh polls to show results
				const fetchResponse = await fetch(`/api/webinar-polls/${webinarId}?includeResults=true`);
				if (fetchResponse.ok) {
					const data = await fetchResponse.json();
					setPolls(data.polls || []);
				}
				setSelectedOptions({ ...selectedOptions, [pollId]: [] });
			}
		} catch (error) {
			console.error("Error submitting poll response:", error);
		}
	};

	const togglePoll = async (pollId: string, isActive: boolean) => {
		try {
			const response = await fetch(`/api/webinar-polls/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					action: isActive ? "stop" : "start",
					pollId,
				}),
			});

			if (response.ok) {
				// Capture timestamp for poll start/stop
				const poll = polls.find((p) => p.id === pollId);
				if (poll) {
					captureReplayInteraction("poll", pollId, {
						pollId,
						action: isActive ? "stop" : "start",
						title: poll.title,
						question: poll.question,
						isActive: !isActive,
					});
				}
			}
		} catch (error) {
			console.error("Error toggling poll:", error);
		}
	};

	const handleOptionChange = (pollId: string, optionId: string, isMultiple: boolean) => {
		setSelectedOptions((prev) => {
			const current = prev[pollId] || [];
			if (isMultiple) {
				const newSelection = current.includes(optionId)
					? current.filter((id) => id !== optionId)
					: [...current, optionId];
				return { ...prev, [pollId]: newSelection };
			} else {
				return { ...prev, [pollId]: [optionId] };
			}
		});
	};

	const activePoll = polls.find((p) => p.is_active);

	return (
		<div className="flex h-full flex-col bg-transparent">
			<div className="flex items-center justify-between bg-gray-a3/40 backdrop-blur-sm px-4 sm:px-6 py-3">
				<Heading size="5" weight="bold" className="text-gray-12">
					Polls
				</Heading>
			</div>
			<div className="flex-1 px-4 sm:px-6 py-4 overflow-hidden">
				<div className="flex flex-col gap-4 h-full overflow-y-auto">
			{activePoll && (
						<div className="rounded-lg p-4 backdrop-blur-sm bg-blue-a2/20 border-l-4 border-l-blue-9 shadow-sm">
					<div className="flex items-center justify-between mb-3">
						<Heading size="4" weight="bold">
							{activePoll.title}
						</Heading>
						{isModerator && (
							<Button
								size="2"
								variant="soft"
								color="danger"
								onClick={() => togglePoll(activePoll.id, true)}
							>
								<Square className="h-4 w-4 mr-1" />
								Stop
							</Button>
						)}
					</div>

					<Text size="3" className="mb-4">
						{activePoll.question}
					</Text>

					{activePoll.userHasResponded && activePoll.show_results ? (
						// Show results
						<div className="flex flex-col gap-3">
							{activePoll.options.map((option) => (
								<div key={option.id} className="flex flex-col gap-2">
									<div className="flex items-center justify-between">
										<Text size="2" weight="medium">
											{option.text}
										</Text>
										<Text size="2" color="gray">
											{option.count || 0} ({option.percentage || 0}%)
										</Text>
									</div>
									<Progress value={option.percentage || 0} className="h-2" />
								</div>
							))}
							<Text size="1" color="gray" className="mt-2">
								Total responses: {activePoll.totalResponses || 0}
							</Text>
						</div>
					) : (
						// Show voting options
						<div className="flex flex-col gap-3">
							{activePoll.poll_type === "single" ? (
								<RadioGroup.Root
									value={selectedOptions[activePoll.id]?.[0] || ""}
									onValueChange={(value) => handleOptionChange(activePoll.id, value, false)}
								>
									{activePoll.options.map((option) => (
										<RadioGroup.Item
											key={option.id}
											value={option.id}
											className="min-h-[44px]"
										>
											{option.text}
										</RadioGroup.Item>
									))}
								</RadioGroup.Root>
							) : (
								<div className="flex flex-col gap-2">
									{activePoll.options.map((option) => (
										<Checkbox
											key={option.id}
											checked={selectedOptions[activePoll.id]?.includes(option.id) || false}
											onCheckedChange={() => handleOptionChange(activePoll.id, option.id, true)}
											className="min-h-[44px]"
										>
											{option.text}
										</Checkbox>
									))}
								</div>
							)}
							<Button
								onClick={() => submitResponse(activePoll.id)}
								disabled={!selectedOptions[activePoll.id] || selectedOptions[activePoll.id].length === 0}
								variant="solid"
								color="blue"
								size="3"
								className="w-full mt-2 min-h-[44px]"
							>
								Submit
							</Button>
						</div>
					)}
				</div>
			)}

			{/* Past Polls (for moderators) */}
			{isModerator && polls.filter((p) => !p.is_active).length > 0 && (
				<div className="flex flex-col gap-3 mt-4">
					<Text size="2" weight="semi-bold" className="text-gray-11">
						Past Polls
					</Text>
					{polls
						.filter((p) => !p.is_active)
						.map((poll) => (
							<div key={poll.id} className="rounded-lg p-3 backdrop-blur-sm bg-gray-a3/40 hover:bg-gray-a4/50 shadow-sm transition-all">
								<div className="flex items-center justify-between">
									<div className="flex-1">
										<Text size="2" weight="medium">
											{poll.title}
										</Text>
										<Text size="1" color="gray">
											{poll.totalResponses || 0} responses
										</Text>
									</div>
									<Button
										size="1"
										variant="soft"
										onClick={() => togglePoll(poll.id, false)}
									>
										<Play className="h-3 w-3 mr-1" />
										Start
									</Button>
								</div>
							</div>
						))}
				</div>
			)}
				</div>
			</div>
		</div>
	);
}

