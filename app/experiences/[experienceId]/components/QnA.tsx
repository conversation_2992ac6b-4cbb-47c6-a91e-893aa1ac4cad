"use client";

import { useEffect, useRef, useState, type KeyboardEvent } from "react";
import { <PERSON><PERSON>, <PERSON>, ScrollArea, Text, TextField, Badge, Card } from "@whop/react/components";
import { HelpCircle, ThumbsUp, MessageSquare, CheckCircle, XCircle } from "lucide-react";

interface Question {
	id: string;
	asked_by_user_id: string;
	asked_by_name: string;
	question: string;
	status: "pending" | "answered" | "dismissed";
	answered_by_user_id?: string;
	answered_by_name?: string;
	answer?: string;
	answered_at?: string;
	is_highlighted: boolean;
	upvotes: number;
	userHasUpvoted: boolean;
	created_at: string;
}

interface QnAProps {
	webinarId: string;
	userId: string;
	isModerator: boolean;
}

export function QnA({ webinarId, userId, isModerator }: QnAProps) {
	const [questions, setQuestions] = useState<Question[]>([]);
	const [filter, setFilter] = useState<"all" | "pending" | "answered">("all");
	const [inputQuestion, setInputQuestion] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [answeringId, setAnsweringId] = useState<string | null>(null);
	const [answerText, setAnswerText] = useState("");
	const questionsEndRef = useRef<HTMLDivElement>(null);
	const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

	// Fetch questions from API
	const fetchQuestions = async () => {
		if (!webinarId) return;

		try {
			let url = `/api/webinar-qa/${webinarId}`;
			if (filter !== "all") {
				url += `?status=${filter}`;
			}

			const response = await fetch(url);
			if (response.ok) {
				const data = await response.json();
				setQuestions(data.questions || []);
			}
		} catch (error) {
			console.error("Error fetching questions:", error);
		}
	};

	// Poll for new questions
	useEffect(() => {
		fetchQuestions();
		
		// Poll every 3 seconds for new questions
		pollingIntervalRef.current = setInterval(() => {
			fetchQuestions();
		}, 3000);

		return () => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
			}
		};
	}, [webinarId, filter]);

	useEffect(() => {
		if (questions.length > 0) {
			questionsEndRef.current?.scrollIntoView({ behavior: "smooth" });
		}
	}, [questions]);

	const submitQuestion = async () => {
		if (!inputQuestion.trim() || !webinarId) return;

		setIsLoading(true);
		try {
			const response = await fetch(`/api/webinar-qa/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					question: inputQuestion,
				}),
			});

			if (response.ok) {
				setInputQuestion("");
				await fetchQuestions();
			} else {
				const error = await response.json();
				alert(error.error || "Failed to submit question");
			}
		} catch (error) {
			console.error("Error submitting question:", error);
			alert("Failed to submit question");
		} finally {
			setIsLoading(false);
		}
	};

	const answerQuestion = async (questionId: string) => {
		if (!answerText.trim()) return;

		setIsLoading(true);
		try {
			const response = await fetch(`/api/webinar-qa/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					action: "answer",
					questionId,
					answer: answerText,
				}),
			});

			if (response.ok) {
				setAnsweringId(null);
				setAnswerText("");
				await fetchQuestions();
			} else {
				const error = await response.json();
				alert(error.error || "Failed to answer question");
			}
		} catch (error) {
			console.error("Error answering question:", error);
			alert("Failed to answer question");
		} finally {
			setIsLoading(false);
		}
	};

	const dismissQuestion = async (questionId: string) => {
		if (!confirm("Are you sure you want to dismiss this question?")) return;

		try {
			const response = await fetch(`/api/webinar-qa/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					action: "dismiss",
					questionId,
				}),
			});

			if (response.ok) {
				await fetchQuestions();
			} else {
				const error = await response.json();
				alert(error.error || "Failed to dismiss question");
			}
		} catch (error) {
			console.error("Error dismissing question:", error);
			alert("Failed to dismiss question");
		}
	};

	const toggleUpvote = async (questionId: string, currentUpvoted: boolean) => {
		try {
			const response = await fetch(`/api/webinar-qa/${webinarId}/${questionId}/upvote`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					action: currentUpvoted ? "remove" : "upvote",
				}),
			});

			if (response.ok) {
				await fetchQuestions();
			}
		} catch (error) {
			console.error("Error toggling upvote:", error);
		}
	};

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			submitQuestion();
		}
	};

	const formatTime = (timestamp: string) => {
		const date = new Date(timestamp);
		return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
	};

	// Sort questions: pending first, then by upvotes, then by created_at
	const sortedQuestions = [...questions].sort((a, b) => {
		if (a.status === "pending" && b.status !== "pending") return -1;
		if (a.status !== "pending" && b.status === "pending") return 1;
		if (a.upvotes !== b.upvotes) return b.upvotes - a.upvotes;
		return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
	});

	return (
		<div className="flex h-full flex-col bg-transparent">
			<div className="flex items-center justify-between bg-gray-a3/40 backdrop-blur-sm px-4 sm:px-6 py-3">
				<Heading size="5" weight="bold" className="text-gray-12">
					Q&A
				</Heading>
				{isModerator && (
					<div className="flex gap-2">
						<Button
							variant={filter === "all" ? "solid" : "soft"}
							size="2"
							onClick={() => setFilter("all")}
						>
							All
						</Button>
						<Button
							variant={filter === "pending" ? "solid" : "soft"}
							size="2"
							onClick={() => setFilter("pending")}
						>
							Pending
						</Button>
						<Button
							variant={filter === "answered" ? "solid" : "soft"}
							size="2"
							onClick={() => setFilter("answered")}
						>
							Answered
						</Button>
					</div>
				)}
			</div>
			<div className="flex-1 px-4 sm:px-6 py-4 overflow-hidden">
				<ScrollArea className="h-full pr-2" scrollbars="vertical">
					<div className="flex flex-col gap-3">
						{sortedQuestions.length === 0 ? (
							<div className="flex flex-col items-center justify-center py-12 text-center">
								<div className="mb-5 flex h-20 w-20 items-center justify-center rounded-xl bg-gray-a4/50 backdrop-blur-sm shadow-sm">
									<HelpCircle className="h-10 w-10 text-gray-9" />
								</div>
								<Text size="4" weight="semi-bold" className="mb-2 text-gray-12">
									No questions yet
								</Text>
								<Text size="2" color="gray" className="max-w-xs">
									Be the first to ask a question!
								</Text>
							</div>
						) : (
							sortedQuestions.map((q) => (
								<div
									key={q.id}
									className={`rounded-lg p-3 sm:p-4 backdrop-blur-sm shadow-sm transition-all ${
										q.status === "pending"
											? "bg-blue-a2/30 border-l-4 border-l-blue-9"
											: q.status === "answered"
												? "bg-success-a2/20 border-l-4 border-l-success-9"
												: "bg-gray-a3/40 hover:bg-gray-a4/50"
									}`}
								>
									<div className="flex flex-col gap-3">
										<div className="flex items-start justify-between gap-3">
											<div className="flex-1">
												<div className="flex items-center gap-2 mb-2">
													<Text size="2" weight="semi-bold" highContrast>
														{q.asked_by_name}
													</Text>
													{q.is_highlighted && (
														<Badge variant="soft" color="blue" size="1">
															Featured
														</Badge>
													)}
													{q.status === "pending" && (
														<Badge variant="soft" color="blue" size="1">
															Pending
														</Badge>
													)}
													{q.status === "answered" && (
														<Badge variant="soft" color="success" size="1">
															Answered
														</Badge>
													)}
												</div>
												<Text size="2" color="gray" highContrast className="mb-2">
													{q.question}
												</Text>
												{q.status === "answered" && q.answer && (
													<div className="mt-3 p-3 bg-success-a2/20 backdrop-blur-sm rounded-lg border-l-4 border-l-success-9">
														<Text size="2" weight="semi-bold" className="mb-1 text-success-11">
															{q.answered_by_name || "Host"}
														</Text>
														<Text size="2" color="gray" highContrast>
															{q.answer}
														</Text>
													</div>
												)}
											</div>
											<div className="flex flex-col items-end gap-2">
												<Text size="1" color="gray">
													{formatTime(q.created_at)}
												</Text>
												<Button
													variant="ghost"
													size="1"
													onClick={() => toggleUpvote(q.id, q.userHasUpvoted)}
													className="flex items-center gap-1"
												>
													<ThumbsUp className={`h-4 w-4 ${q.userHasUpvoted ? "fill-current" : ""}`} />
													<Text size="1">{q.upvotes}</Text>
												</Button>
											</div>
										</div>
										{isModerator && q.status === "pending" && (
											<div className="flex gap-2 pt-3 mt-2">
												{answeringId === q.id ? (
													<div className="flex-1 flex flex-col gap-2">
														<TextField.Root size="2">
															<TextField.Input
																value={answerText}
																onChange={(e) => setAnswerText(e.target.value)}
																placeholder="Type your answer..."
																className="w-full"
															/>
														</TextField.Root>
														<div className="flex gap-2">
															<Button
																size="2"
																variant="solid"
																color="blue"
																onClick={() => answerQuestion(q.id)}
																disabled={!answerText.trim() || isLoading}
															>
																<CheckCircle className="h-3 w-3 mr-1" />
																Answer
															</Button>
															<Button
																size="2"
																variant="soft"
																onClick={() => {
																	setAnsweringId(null);
																	setAnswerText("");
																}}
															>
																Cancel
															</Button>
															<Button
																size="2"
																variant="soft"
																color="danger"
																onClick={() => dismissQuestion(q.id)}
															>
																<XCircle className="h-3 w-3 mr-1" />
																Dismiss
															</Button>
														</div>
													</div>
												) : (
													<>
														<Button
															size="2"
															variant="solid"
															color="blue"
															onClick={() => setAnsweringId(q.id)}
														>
															<MessageSquare className="h-3 w-3 mr-1" />
															Answer
														</Button>
														<Button
															size="2"
															variant="soft"
															color="danger"
															onClick={() => dismissQuestion(q.id)}
														>
															<XCircle className="h-3 w-3 mr-1" />
															Dismiss
														</Button>
													</>
												)}
											</div>
										)}
									</div>
								</div>
							))
						)}
						<div ref={questionsEndRef} />
					</div>
				</ScrollArea>
			</div>
			{!isModerator && (
				<div className="bg-gray-a3/40 backdrop-blur-sm px-4 sm:px-6 py-3">
					<div className="flex gap-3">
						<TextField.Root className="flex-1" variant="surface" size="3" color="gray">
							<TextField.Input
								value={inputQuestion}
								onChange={(e) => setInputQuestion(e.target.value)}
								onKeyDown={handleKeyDown}
								placeholder="Ask a question..."
								disabled={isLoading}
								className="w-full"
							/>
						</TextField.Root>
						<Button
							onClick={submitQuestion}
							disabled={!inputQuestion.trim() || isLoading}
							variant="solid"
							color="blue"
							size="3"
							className="shadow-md transition-all hover:shadow-lg"
						>
							Submit
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}

