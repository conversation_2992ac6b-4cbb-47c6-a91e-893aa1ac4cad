"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, Badge, Progress } from "@whop/react/components";
import { ThumbsUp, X, Play, Square } from "lucide-react";

interface Reaction {
	id: string;
	reaction_type: string;
	user_name: string;
	created_at: string;
	duration_ms: number;
}

interface ReactionsProps {
	webinarId: string;
	userId: string;
	webinarStartTime?: string; // ISO timestamp of when webinar started
}

export function Reactions({ webinarId, userId, webinarStartTime }: ReactionsProps) {
	const [recentReactions, setRecentReactions] = useState<Reaction[]>([]);
	const [startTime, setStartTime] = useState<string | null>(null);

	const reactions = ["👍", "❤️", "😂", "🎉", "🔥", "👏", "💯", "🚀"];

	useEffect(() => {
		if (webinarStartTime) {
			setStartTime(webinarStartTime);
		} else if (webinarId) {
			// Fetch webinar to get scheduled_at time
			fetch(`/api/webinars/${webinarId}`)
				.then((res) => res.json())
				.then((data) => {
					if (data.webinar?.scheduled_at) {
						setStartTime(data.webinar.scheduled_at);
					}
				})
				.catch((err) => console.error("Error fetching webinar start time:", err));
		}
	}, [webinarId, webinarStartTime]);

	useEffect(() => {
		if (!webinarId) return;

		const fetchReactions = async () => {
			try {
				const response = await fetch(`/api/webinar-reactions/${webinarId}?limit=20`);
				if (response.ok) {
					const data = await response.json();
					setRecentReactions(data.reactions || []);
				}
			} catch (error) {
				console.error("Error fetching reactions:", error);
			}
		};

		fetchReactions();
		const interval = setInterval(fetchReactions, 2000);
		return () => clearInterval(interval);
	}, [webinarId]);

	const sendReaction = async (reactionType: string) => {
		try {
			const response = await fetch(`/api/webinar-reactions/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					reactionType,
					durationMs: 3000,
				}),
			});

			if (response.ok) {
				const data = await response.json();
				const reactionId = data.reaction?.id;

				// Capture timestamp for replay synchronization
				if (reactionId && startTime) {
					const now = new Date();
					const start = new Date(startTime);
					const timestampOffset = (now.getTime() - start.getTime()) / 1000; // seconds

					try {
						await fetch(`/api/webinar-replay-data/${webinarId}`, {
							method: "POST",
							headers: {
								"Content-Type": "application/json",
							},
							body: JSON.stringify({
								interactionType: "reaction",
								interactionId: reactionId,
								timestampOffset,
								webinarStartTime: startTime,
								interactionData: {
									reactionType,
									userId,
									durationMs: 3000,
								},
							}),
						});
					} catch (replayError) {
						console.error("Error capturing replay data:", replayError);
					}
				}
			}
		} catch (error) {
			console.error("Error sending reaction:", error);
		}
	};

	return (
		<div className="flex items-center gap-1">
			{/* Reaction Buttons */}
				{reactions.map((reaction) => (
				<button
						key={reaction}
						onClick={() => sendReaction(reaction)}
					className="text-lg sm:text-xl p-1.5 w-8 h-8 sm:w-9 sm:h-9 rounded-lg hover:bg-gray-a4/60 transition-all hover:scale-110 active:scale-95 flex items-center justify-center"
						title={reaction}
					>
						{reaction}
				</button>
					))}
		</div>
	);
}

