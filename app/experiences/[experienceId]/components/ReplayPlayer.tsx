"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>, Heading, Text, Badge, Progress, ScrollArea } from "@whop/react/components";
import { Play, Pause, SkipForward, SkipBack, Volume2, VolumeX, Maximize2 } from "lucide-react";

interface ReplayInteraction {
	id: string;
	interaction_type: string;
	interaction_id: string;
	timestamp_offset: number;
	interaction_data: Record<string, unknown>;
}

interface ReplayData {
	replay: {
		id: string;
		recording_id: string;
		webinar_recordings: {
			recording_url: string;
			duration_seconds: number;
		};
	};
	interactions: ReplayInteraction[];
}

interface ReplayPlayerProps {
	webinarId: string;
	replayId?: string;
}

export function ReplayPlayer({ webinarId, replayId }: ReplayPlayerProps) {
	const [replayData, setReplayData] = useState<ReplayData | null>(null);
	const [isPlaying, setIsPlaying] = useState(false);
	const [currentTime, setCurrentTime] = useState(0);
	const [duration, setDuration] = useState(0);
	const [volume, setVolume] = useState(1);
	const [isMuted, setIsMuted] = useState(false);
	const [playbackRate, setPlaybackRate] = useState(1);
	const [visibleInteractions, setVisibleInteractions] = useState<ReplayInteraction[]>([]);
	const videoRef = useRef<HTMLVideoElement>(null);
	const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

	// Fetch replay data
	useEffect(() => {
		if (!webinarId) return;

		const fetchReplayData = async () => {
			try {
				const url = replayId
					? `/api/webinar-replays/${webinarId}?replayId=${replayId}`
					: `/api/webinar-replays/${webinarId}`;
				
				const replayResponse = await fetch(url);
				if (replayResponse.ok) {
					const replayResult = await replayResponse.json();
					const replay = replayResult.replays?.[0];

					if (replay) {
						// Fetch interactions
						const interactionsResponse = await fetch(`/api/webinar-replay-data/${webinarId}`);
						if (interactionsResponse.ok) {
							const interactionsResult = await interactionsResponse.json();
							setReplayData({
								replay,
								interactions: interactionsResult.interactions || [],
							});
							setDuration(replay.webinar_recordings?.duration_seconds || 0);
						}
					}
				}
			} catch (error) {
				console.error("Error fetching replay data:", error);
			}
		};

		fetchReplayData();
	}, [webinarId, replayId]);

	// Update visible interactions based on current time
	useEffect(() => {
		if (!replayData) return;

		const visible = replayData.interactions.filter(
			(interaction) => interaction.timestamp_offset <= currentTime
		);
		setVisibleInteractions(visible);
	}, [currentTime, replayData]);

	// Update video time
	useEffect(() => {
		if (isPlaying && videoRef.current) {
			progressIntervalRef.current = setInterval(() => {
				if (videoRef.current) {
					setCurrentTime(videoRef.current.currentTime);
				}
			}, 100);
		} else {
			if (progressIntervalRef.current) {
				clearInterval(progressIntervalRef.current);
			}
		}

		return () => {
			if (progressIntervalRef.current) {
				clearInterval(progressIntervalRef.current);
			}
		};
	}, [isPlaying]);

	const togglePlay = () => {
		if (videoRef.current) {
			if (isPlaying) {
				videoRef.current.pause();
			} else {
				videoRef.current.play();
			}
			setIsPlaying(!isPlaying);
		}
	};

	const handleSeek = (newTime: number) => {
		if (videoRef.current) {
			videoRef.current.currentTime = newTime;
			setCurrentTime(newTime);
		}
	};

	const handleVolumeChange = (newVolume: number) => {
		setVolume(newVolume);
		if (videoRef.current) {
			videoRef.current.volume = newVolume;
		}
	};

	const toggleMute = () => {
		if (videoRef.current) {
			videoRef.current.muted = !isMuted;
			setIsMuted(!isMuted);
		}
	};

	const changePlaybackRate = (rate: number) => {
		if (videoRef.current) {
			videoRef.current.playbackRate = rate;
			setPlaybackRate(rate);
		}
	};

	const formatTime = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = Math.floor(seconds % 60);
		return `${mins}:${secs.toString().padStart(2, "0")}`;
	};

	if (!replayData || !replayData.replay.webinar_recordings?.recording_url) {
		return (
			<div className="flex items-center justify-center min-h-screen p-8">
				<Card variant="surface" size="4" className="p-8 text-center">
					<Heading size="5" weight="bold" className="mb-4">
						Replay Not Available
					</Heading>
					<Text size="3" color="gray">
						The replay recording is not yet available or has expired.
					</Text>
				</Card>
			</div>
		);
	}

	const chatMessages = visibleInteractions.filter((i) => i.interaction_type === "chat");
	const polls = visibleInteractions.filter((i) => i.interaction_type === "poll");
	const ctas = visibleInteractions.filter((i) => i.interaction_type === "cta");
	const reactions = visibleInteractions.filter((i) => i.interaction_type === "reaction");

	return (
		<div className="flex flex-col h-screen w-full bg-gradient-to-br from-gray-a1 via-gray-a2 to-gray-a3">
			<div className="flex-1 flex overflow-hidden gap-4 p-4">
				{/* Main Video Player */}
				<div className="flex-1 flex flex-col gap-4">
					<Card variant="surface" size="4" className="flex-1 flex flex-col overflow-hidden">
						{/* Video */}
						<div className="relative flex-1 bg-black rounded-lg overflow-hidden">
							<video
								ref={videoRef}
								src={replayData.replay.webinar_recordings.recording_url}
								className="w-full h-full object-contain"
								onLoadedMetadata={() => {
									if (videoRef.current) {
										setDuration(videoRef.current.duration);
									}
								}}
								onEnded={() => setIsPlaying(false)}
							/>

							{/* Reactions Overlay */}
							{reactions.map((reaction) => {
								const data = reaction.interaction_data as { reactionType?: string };
								return (
									<div
										key={reaction.id}
										className="absolute text-4xl animate-float-up"
										style={{
											left: `${Math.random() * 80 + 10}%`,
											top: `${Math.random() * 60 + 20}%`,
										}}
									>
										{data.reactionType || "👍"}
									</div>
								);
							})}

							{/* CTA Overlay */}
							{ctas.map((cta) => {
								const data = cta.interaction_data as {
									title?: string;
									buttonText?: string;
									buttonUrl?: string;
								};
								return (
									<div
										key={cta.id}
										className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-blue-9 text-white p-4 rounded-lg shadow-xl animate-slide-up"
									>
										<Text size="3" weight="bold" className="mb-2">
											{data.title || "Special Offer"}
										</Text>
										<Button
											variant="solid"
											color="blue"
											size="3"
											onClick={() => {
												if (data.buttonUrl) {
													window.open(data.buttonUrl, "_blank");
												}
											}}
										>
											{data.buttonText || "Learn More"}
										</Button>
									</div>
								);
							})}
						</div>

						{/* Controls */}
						<div className="p-4 border-t border-gray-a6 bg-gray-a4/80">
							{/* Progress Bar */}
							<div className="mb-4">
								<input
									type="range"
									min="0"
									max={duration}
									value={currentTime}
									onChange={(e) => handleSeek(Number(e.target.value))}
									className="w-full h-2 bg-gray-a6 rounded-lg appearance-none cursor-pointer"
									style={{
										background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(currentTime / duration) * 100}%, #e5e7eb ${(currentTime / duration) * 100}%, #e5e7eb 100%)`,
									}}
								/>
								<div className="flex justify-between text-xs text-gray-11 mt-1">
									<span>{formatTime(currentTime)}</span>
									<span>{formatTime(duration)}</span>
								</div>
							</div>

							{/* Control Buttons */}
							<div className="flex items-center justify-between gap-4">
								<div className="flex items-center gap-2">
									<Button
										variant="soft"
										size="3"
										onClick={togglePlay}
										className="min-h-[44px] min-w-[44px]"
									>
										{isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
									</Button>
									<Button
										variant="soft"
										size="3"
										onClick={() => handleSeek(Math.max(0, currentTime - 10))}
										className="min-h-[44px] min-w-[44px]"
									>
										<SkipBack className="h-5 w-5" />
									</Button>
									<Button
										variant="soft"
										size="3"
										onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
										className="min-h-[44px] min-w-[44px]"
									>
										<SkipForward className="h-5 w-5" />
									</Button>
									<Button
										variant="soft"
										size="3"
										onClick={toggleMute}
										className="min-h-[44px] min-w-[44px]"
									>
										{isMuted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
									</Button>
									<input
										type="range"
										min="0"
										max="1"
										step="0.1"
										value={volume}
										onChange={(e) => handleVolumeChange(Number(e.target.value))}
										className="w-20 h-1 bg-gray-a6 rounded-lg appearance-none cursor-pointer"
									/>
								</div>

								<div className="flex items-center gap-2">
									<Text size="2" color="gray">
										Speed:
									</Text>
									{[0.5, 1, 1.5, 2].map((rate) => (
										<Button
											key={rate}
											variant={playbackRate === rate ? "solid" : "soft"}
											size="2"
											onClick={() => changePlaybackRate(rate)}
											className="min-h-[32px] min-w-[32px]"
										>
											{rate}x
										</Button>
									))}
								</div>
							</div>
						</div>
					</Card>
				</div>

				{/* Sidebar with Interactions */}
				<div className="w-80 flex flex-col gap-4">
					{/* Chat Messages */}
					<Card variant="surface" size="3" className="flex-1 flex flex-col overflow-hidden">
						<Heading size="4" weight="bold" className="p-4 border-b border-gray-a6">
							Chat ({chatMessages.length})
						</Heading>
						<ScrollArea className="flex-1 p-4">
							<div className="flex flex-col gap-3">
								{chatMessages.length === 0 ? (
									<Text size="2" color="gray" className="text-center py-8">
										No messages yet
									</Text>
								) : (
									chatMessages.map((msg) => {
										const data = msg.interaction_data as {
											message?: string;
											userName?: string;
											userRole?: string;
										};
										return (
											<div
												key={msg.id}
												className="p-3 rounded-lg bg-gray-a4/70 border border-gray-a6"
											>
												<div className="flex items-center gap-2 mb-1">
													<Text size="2" weight="medium">
														{data.userName || "User"}
													</Text>
													{data.userRole && data.userRole !== "attendee" && (
														<Badge variant="soft" color="blue" size="1">
															{data.userRole}
														</Badge>
													)}
													<Text size="1" color="gray">
														{formatTime(msg.timestamp_offset)}
													</Text>
												</div>
												<Text size="2" color="gray">
													{data.message || ""}
												</Text>
											</div>
										);
									})
								)}
							</div>
						</ScrollArea>
					</Card>

					{/* Active Polls */}
					{polls.length > 0 && (
						<Card variant="surface" size="3" className="p-4">
							<Heading size="4" weight="bold" className="mb-3">
								Active Polls
							</Heading>
							{polls.map((poll) => {
								const data = poll.interaction_data as {
									title?: string;
									question?: string;
								};
								return (
									<div key={poll.id} className="p-3 rounded-lg bg-blue-a2/20 border border-blue-a6 mb-3">
										<Text size="2" weight="medium" className="mb-1">
											{data.title || "Poll"}
										</Text>
										<Text size="1" color="gray">
											{data.question || ""}
										</Text>
									</div>
								);
							})}
						</Card>
					)}
				</div>
			</div>

			<style jsx>{`
				@keyframes float-up {
					from {
						opacity: 1;
						transform: translateY(0);
					}
					to {
						opacity: 0;
						transform: translateY(-100px);
					}
				}
				@keyframes slide-up {
					from {
						opacity: 0;
						transform: translateY(20px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}
				.animate-float-up {
					animation: float-up 3s ease-out forwards;
				}
				.animate-slide-up {
					animation: slide-up 0.3s ease-out;
				}
			`}</style>
		</div>
	);
}

