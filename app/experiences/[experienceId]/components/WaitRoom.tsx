"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Heading, Text, <PERSON><PERSON>, Badge } from "@whop/react/components";
import { Users, Clock, CheckCircle, XCircle } from "lucide-react";

interface WaitRoomEntry {
	id: string;
	user_id: string;
	user_name: string;
	user_email: string;
	joined_at: string;
	admitted_at: string | null;
	status: "waiting" | "admitted" | "denied";
	queue_position: number | null;
}

interface WaitRoomProps {
	webinarId: string;
	userId: string;
	isModerator: boolean;
	onAdmitted?: () => void;
}

export function WaitRoom({ webinarId, userId, isModerator, onAdmitted }: WaitRoomProps) {
	const [waitRoomData, setWaitRoomData] = useState<{
		isStarted: boolean;
		waitRoomMode: boolean;
		userEntry: WaitRoomEntry | null;
		queue: WaitRoomEntry[];
		totalWaiting: number;
	} | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		if (!webinarId) return;

		const fetchWaitRoomStatus = async () => {
			try {
				const response = await fetch(`/api/webinar-wait-room/${webinarId}`);
				if (response.ok) {
					const data = await response.json();
					setWaitRoomData(data);

					// If user is admitted and webinar started, notify parent
					if (data.userEntry?.status === "admitted" && data.isStarted && onAdmitted) {
						onAdmitted();
					}
				}
			} catch (error) {
				console.error("Error fetching wait room status:", error);
			} finally {
				setIsLoading(false);
			}
		};

		fetchWaitRoomStatus();
		const interval = setInterval(fetchWaitRoomStatus, 3000);
		return () => clearInterval(interval);
	}, [webinarId, onAdmitted]);

	useEffect(() => {
		// Join wait room automatically if not already joined
		if (waitRoomData?.waitRoomMode && !waitRoomData.userEntry && !isLoading) {
			const joinWaitRoom = async () => {
				try {
					await fetch(`/api/webinar-wait-room/${webinarId}`, {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							action: "join",
						}),
					});
				} catch (error) {
					console.error("Error joining wait room:", error);
				}
			};
			joinWaitRoom();
		}
	}, [waitRoomData, webinarId, isLoading]);

	const handleAdmit = async (targetUserId: string) => {
		try {
			await fetch(`/api/webinar-wait-room/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					action: "admit",
					userId: targetUserId,
				}),
			});
		} catch (error) {
			console.error("Error admitting user:", error);
		}
	};

	const handleDeny = async (targetUserId: string) => {
		try {
			await fetch(`/api/webinar-wait-room/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					action: "deny",
					userId: targetUserId,
				}),
			});
		} catch (error) {
			console.error("Error denying user:", error);
		}
	};

	if (isLoading) {
		return (
			<Card variant="surface" size="4" className="p-8 text-center">
				<Text>Loading...</Text>
			</Card>
		);
	}

	if (!waitRoomData || !waitRoomData.waitRoomMode) {
		return null;
	}

	const { userEntry, queue, totalWaiting } = waitRoomData;

	return (
		<div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gradient-to-br from-gray-a1 via-gray-a2 to-gray-a3">
			<Card variant="surface" size="4" className="w-full max-w-2xl p-8">
				{!isModerator ? (
					// Attendee view
					<div className="flex flex-col items-center gap-6 text-center">
						<Clock className="h-16 w-16 text-blue-9" />
						<Heading size="6" weight="bold">
							You're in the Wait Room
						</Heading>
						<Text size="3" color="gray">
							Please wait while the host prepares the webinar. You'll be admitted shortly.
						</Text>

						{userEntry && (
							<div className="w-full mt-4">
								<div className="flex items-center justify-between p-4 bg-gray-a4 rounded-lg">
									<div className="flex items-center gap-3">
										<Badge variant="soft" color="blue" size="2">
											#{userEntry.queue_position || "?"}
										</Badge>
										<Text size="2" weight="medium">
											{totalWaiting} {totalWaiting === 1 ? "person" : "people"} waiting
										</Text>
									</div>
									{userEntry.status === "admitted" && (
										<Badge variant="soft" color="success" size="2">
											<CheckCircle className="h-4 w-4 mr-1" />
											Admitted
										</Badge>
									)}
									{userEntry.status === "denied" && (
										<Badge variant="soft" color="danger" size="2">
											<XCircle className="h-4 w-4 mr-1" />
											Access Denied
										</Badge>
									)}
								</div>
							</div>
						)}
					</div>
				) : (
					// Moderator view
					<div className="flex flex-col gap-6">
						<div className="flex items-center justify-between">
							<Heading size="5" weight="bold" className="flex items-center gap-2">
								<Users className="h-5 w-5" />
								Wait Room ({totalWaiting})
							</Heading>
						</div>

						{queue.length === 0 ? (
							<Text size="2" color="gray" className="text-center py-8">
								No one waiting
							</Text>
						) : (
							<div className="flex flex-col gap-3 max-h-96 overflow-y-auto">
								{queue.map((entry) => (
									<div
										key={entry.id}
										className="flex items-center justify-between p-4 bg-gray-a4 rounded-lg"
									>
										<div className="flex items-center gap-4 flex-1">
											<Badge variant="soft" color="blue" size="2">
												#{entry.queue_position}
											</Badge>
											<div className="flex-1">
												<Text size="2" weight="medium">
													{entry.user_name}
												</Text>
												<Text size="1" color="gray">
													{entry.user_email}
												</Text>
											</div>
											{entry.status === "admitted" && (
												<Badge variant="soft" color="success" size="1">
													Admitted
												</Badge>
											)}
										</div>
										{entry.status === "waiting" && (
											<div className="flex gap-2">
												<Button
													size="2"
													variant="solid"
													color="success"
													onClick={() => handleAdmit(entry.user_id)}
													className="min-h-[44px]"
												>
													Admit
												</Button>
												<Button
													size="2"
													variant="soft"
													color="danger"
													onClick={() => handleDeny(entry.user_id)}
													className="min-h-[44px]"
												>
													Deny
												</Button>
											</div>
										)}
									</div>
								))}
							</div>
						)}
					</div>
				)}
			</Card>
		</div>
	);
}

