"use client";

import { <PERSON><PERSON>, <PERSON>, Heading, Text, Badge } from "@whop/react/components";
import { Calendar, Clock, Users, Video, Repeat, Lock, DollarSign, UserPlus, Edit } from "lucide-react";
import { useRouter } from "next/navigation";
import { useRegistrationPages } from "@/lib/queries/registrations";
import type { Webinar } from "@/lib/supabase";

interface WebinarCardProps {
	webinar: Webinar;
	onJoin: (webinarId: string) => void;
	isHost?: boolean;
	onEdit?: (webinar: Webinar) => void;
}

const EVENT_TYPE_LABELS: Record<Webinar["event_type"], string> = {
	live: "Live Event",
	right_now: "Right Now",
	recurring: "Recurring",
	always_on: "Always On",
	evergreen_room: "Evergreen Room",
};

const CURRENCY_SYMBOLS: Record<string, string> = {
	USD: "$",
	EUR: "€",
	GBP: "£",
	JPY: "¥",
	CAD: "C$",
	AUD: "A$",
};

export function WebinarCard({ webinar, onJoin, isHost, onEdit }: WebinarCardProps) {
	const router = useRouter();
	const { data: registrationPages = [] } = useRegistrationPages({ webinarId: webinar.id });
	const registrationPage = registrationPages.find((page) => page.is_active);

	const scheduledDate = new Date(webinar.scheduled_at);
	const now = new Date();
	
	// Calculate end time based on event type
	let endTime: Date | null = null;
	let isLive = false;
	let canJoin = false;

	if (webinar.event_type === "always_on" || webinar.event_type === "evergreen_room") {
		// Always available
		isLive = true;
		canJoin = true;
	} else if (webinar.event_type === "right_now") {
		// Right now events are always live
		isLive = true;
		canJoin = true;
	} else {
		// Live and recurring events have time-based availability
		endTime = new Date(
			scheduledDate.getTime() + webinar.duration_minutes * 60 * 1000,
		);
		isLive = webinar.status === "live" || (now >= scheduledDate && now <= endTime);
		canJoin = isLive || now >= scheduledDate;
	}

	const formattedDate = scheduledDate.toLocaleDateString("en-US", {
		month: "short",
		day: "numeric",
		year: "numeric",
		timeZone: webinar.timezone || "UTC",
	});
	const formattedTime = scheduledDate.toLocaleTimeString("en-US", {
		hour: "numeric",
		minute: "2-digit",
		timeZone: webinar.timezone || "UTC",
	});

	const getTimeUntilStart = () => {
		if (webinar.event_type === "always_on" || webinar.event_type === "evergreen_room" || webinar.event_type === "right_now") {
			return null;
		}
		const diff = scheduledDate.getTime() - now.getTime();
		if (diff <= 0) return null;

		const days = Math.floor(diff / (1000 * 60 * 60 * 24));
		const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

		if (days > 0) return `${days}d ${hours}h`;
		if (hours > 0) return `${hours}h ${minutes}m`;
		return `${minutes}m`;
	};

	const getRecurrenceText = () => {
		if (webinar.event_type !== "recurring" || !webinar.recurrence_pattern) return null;
		
		const interval = webinar.recurrence_interval || 1;
		const pattern = webinar.recurrence_pattern;
		const patternText = pattern === "daily" ? "day" : pattern === "weekly" ? "week" : "month";
		
		if (webinar.recurrence_end_date) {
			const endDate = new Date(webinar.recurrence_end_date);
			return `Every ${interval} ${patternText}(s) until ${endDate.toLocaleDateString()}`;
		} else if (webinar.recurrence_count) {
			return `Every ${interval} ${patternText}(s) for ${webinar.recurrence_count} occurrence(s)`;
		}
		return `Every ${interval} ${patternText}(s)`;
	};

	const timeUntilStart = getTimeUntilStart();
	const recurrenceText = getRecurrenceText();

	const getEventTypeBadgeColor = (): "blue" | "red" | "green" | "purple" | "orange" => {
		switch (webinar.event_type) {
			case "live":
				return "blue";
			case "right_now":
				return "red";
			case "recurring":
				return "purple";
			case "always_on":
				return "green";
			case "evergreen_room":
				return "orange";
			default:
				return "blue";
		}
	};

	const getPresenterRolesText = () => {
		if (!webinar.presenter_roles || Object.keys(webinar.presenter_roles).length === 0) {
			return null;
		}
		const roles = Object.values(webinar.presenter_roles);
		const roleCounts: Record<string, number> = {};
		roles.forEach((role) => {
			roleCounts[role] = (roleCounts[role] || 0) + 1;
		});
		return Object.entries(roleCounts)
			.map(([role, count]) => `${count} ${role}${count > 1 ? "s" : ""}`)
			.join(", ");
	};

	const presenterRolesText = getPresenterRolesText();
	const currencySymbol = CURRENCY_SYMBOLS[webinar.price_currency || "USD"] || "$";
	const priceDisplay = webinar.is_paid && webinar.price_amount
		? `${currencySymbol}${webinar.price_amount.toFixed(2)} ${webinar.price_currency || "USD"}`
		: null;

	return (
		<Card 
			className="p-3 sm:p-4 bg-gray-a2/40 backdrop-blur-sm border border-white/10 hover:bg-gray-a3/50 hover:shadow-lg transition-all h-full flex flex-col"
			style={{
				backgroundColor: webinar.custom_branding_background_color ? `${webinar.custom_branding_background_color}80` : undefined,
				color: webinar.custom_branding_text_color || undefined,
			}}
		>
			{webinar.custom_branding_logo_url && (
				<div className="mb-3 sm:mb-4">
					<img
						src={webinar.custom_branding_logo_url}
						alt="Event branding"
						className="h-10 sm:h-12 object-contain"
						onError={(e) => {
							(e.target as HTMLImageElement).style.display = "none";
						}}
					/>
				</div>
			)}
			{/* Card layout - vertical stack for grid */}
			<div className="flex flex-col gap-3">
				<div className="flex-1 min-w-0">
					{/* Badges - wrap */}
					<div className="flex flex-wrap items-center gap-2 mb-2">
						<Heading size="3" weight="bold" className="break-words">
							{webinar.title}
						</Heading>
						<Badge color={getEventTypeBadgeColor()} variant="solid" className="flex-shrink-0">
							{EVENT_TYPE_LABELS[webinar.event_type]}
						</Badge>
						{isLive && (
							<Badge color="blue" variant="soft" className="flex-shrink-0 bg-blue-a2/40 backdrop-blur-sm">
								Live Now
							</Badge>
						)}
						{webinar.is_password_protected && (
							<Badge color="orange" variant="solid" className="flex items-center gap-1 flex-shrink-0">
								<Lock className="h-3 w-3" />
								<span className="hidden sm:inline">Password Protected</span>
								<span className="sm:hidden">Locked</span>
							</Badge>
						)}
						{webinar.is_paid && priceDisplay && (
							<Badge color="green" variant="solid" className="flex items-center gap-1 flex-shrink-0">
								<DollarSign className="h-3 w-3" />
								<span className="hidden sm:inline">{priceDisplay}</span>
								<span className="sm:hidden">Paid</span>
							</Badge>
						)}
					</div>
					{webinar.description && (
						<Text size="2" color="gray" className="mb-2 break-words line-clamp-2">
							{webinar.description}
						</Text>
					)}
					{recurrenceText && (
						<div className="flex items-center gap-2 mb-2 text-purple-11">
							<Repeat className="h-4 w-4 flex-shrink-0" />
							<Text size="1" weight="semi-bold" className="break-words">
								{recurrenceText}
							</Text>
						</div>
					)}
					{/* Info grid - vertical stack */}
					<div className="flex flex-col gap-2 mt-2">
						{webinar.event_type !== "always_on" && webinar.event_type !== "evergreen_room" && (
							<>
								<div className="flex items-center gap-2 text-gray-11">
									<Calendar className="h-4 w-4 flex-shrink-0" />
									<Text size="1" className="sm:text-2 break-words">{formattedDate}</Text>
								</div>
								{webinar.event_type !== "right_now" && (
									<div className="flex items-center gap-2 text-gray-11">
										<Clock className="h-4 w-4 flex-shrink-0" />
										<Text size="1" className="sm:text-2 break-words">
											{formattedTime}
											{webinar.duration_minutes > 0 && ` (${webinar.duration_minutes} min)`}
											{webinar.timezone && ` ${webinar.timezone}`}
										</Text>
									</div>
								)}
							</>
						)}
						{(webinar.event_type === "always_on" || webinar.event_type === "evergreen_room") && (
							<div className="flex items-center gap-2 text-green-11">
								<Video className="h-4 w-4 flex-shrink-0" />
								<Text size="1" weight="semi-bold" className="sm:text-2">
									Available 24/7
								</Text>
							</div>
						)}
						{(webinar.host_ids.length > 0 || presenterRolesText) && (
							<div className="flex items-center gap-2 text-gray-11">
								<Users className="h-4 w-4 flex-shrink-0" />
								<Text size="1" className="sm:text-2 break-words">
									{presenterRolesText || `${webinar.host_ids.length} host(s)`}
								</Text>
							</div>
						)}
						{timeUntilStart && !isLive && (
							<div className="flex items-center gap-2 text-blue-11">
								<Clock className="h-4 w-4 flex-shrink-0" />
								<Text size="1" weight="semi-bold" className="sm:text-2">
									Starts in {timeUntilStart}
								</Text>
							</div>
						)}
					</div>
				</div>
				{/* Join button - full width for grid */}
				<div className="flex flex-col gap-2 mt-auto">
					{isHost && onEdit && (
						<Button
							variant="surface"
							size="2"
							onClick={() => onEdit(webinar)}
							className="flex items-center justify-center gap-2 w-full"
						>
							<Edit className="h-4 w-4" />
							Edit Webinar
						</Button>
					)}
					{registrationPage && (
						<Button
							variant="soft"
							size="2"
							onClick={() => router.push(`/register/${registrationPage.slug}`)}
							className="flex items-center justify-center gap-2 w-full"
						>
							<UserPlus className="h-4 w-4" />
							Register
						</Button>
					)}
					<Button
						variant="classic"
						color={webinar.custom_branding_accent_color ? undefined : "blue"}
						size="2"
						onClick={() => onJoin(webinar.id)}
						disabled={!canJoin}
						className="flex items-center justify-center gap-2 w-full shadow-lg hover:shadow-xl transition-all font-semibold relative overflow-hidden bg-gradient-to-br from-blue-9 via-blue-10 to-blue-11 backdrop-blur-sm border border-white/20 px-4 py-2"
						style={{
							backgroundColor: webinar.custom_branding_accent_color ? undefined : undefined,
							boxShadow: webinar.custom_branding_accent_color ? undefined : '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(0, 0, 0, 0.2)',
						}}
					>
						<span className="flex items-center gap-2 relative z-10">
							<Video className="h-4 w-4" />
							{isLive ? "Join Now" : canJoin ? "Join" : "Join Soon"}
						</span>
						{/* Frost overlay for depth */}
						{!webinar.custom_branding_accent_color && (
							<div className="absolute inset-0 bg-gradient-to-b from-white/20 via-transparent to-black/10 pointer-events-none rounded-lg" />
						)}
					</Button>
				</div>
			</div>
		</Card>
	);
}
