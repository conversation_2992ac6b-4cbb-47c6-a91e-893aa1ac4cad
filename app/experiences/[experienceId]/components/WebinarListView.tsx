"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@whop/react/components";
import { Plus } from "lucide-react";
import { WebinarCard } from "./WebinarCard";
import { AdminWebinarCard } from "./AdminWebinarCard";
import { CreateWebinarForm } from "./CreateWebinarForm";
import { useWebinars, useDeleteWebinar } from "@/lib/queries/webinars";
import type { Webinar } from "@/lib/supabase";

interface WebinarListViewProps {
	experienceId: string;
	companyId: string;
	isHost: boolean;
	canCreateWebinars: boolean;
	onJoinWebinar: (webinarId: string) => void;
}

export function WebinarListView({
	experienceId,
	companyId,
	isHost,
	canCreateWebinars,
	onJoinWebinar,
}: WebinarListViewProps) {
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [editingWebinar, setEditingWebinar] = useState<Webinar | null>(null);

	const { data: webinars = [], isLoading } = useWebinars({ experienceId });
	const deleteMutation = useDeleteWebinar();

	// Determine current and upcoming webinars based on event type
	const now = new Date();
	const current = webinars.find((w: Webinar) => {
		// Always on and evergreen rooms are always current
		if (w.event_type === "always_on" || w.event_type === "evergreen_room") {
			return true;
		}
		// Right now events are always current
		if (w.event_type === "right_now") {
			return true;
		}
		// For live and recurring events, check time-based status
		const scheduledAt = new Date(w.scheduled_at);
		const endTime = new Date(
			scheduledAt.getTime() + w.duration_minutes * 60 * 1000,
		);
		return (
			w.status === "live" || (now >= scheduledAt && now <= endTime)
		);
	});

	const upcoming = webinars.filter((w: Webinar) => {
		// Always on and evergreen rooms are never "upcoming"
		if (w.event_type === "always_on" || w.event_type === "evergreen_room") {
			return false;
		}
		// Right now events are never "upcoming"
		if (w.event_type === "right_now") {
			return false;
		}
		// For live and recurring events, check if scheduled for future
		const scheduledAt = new Date(w.scheduled_at);
		const endTime = new Date(
			scheduledAt.getTime() + w.duration_minutes * 60 * 1000,
		);
		return (
			w.status === "scheduled" &&
			scheduledAt > now &&
			w.id !== current?.id
		);
	});

	const handleCreate = () => {
		setEditingWebinar(null);
		setShowCreateForm(true);
	};

	const handleEdit = (webinar: Webinar) => {
		setEditingWebinar(webinar);
		setShowCreateForm(true);
	};

	const handleDelete = async (webinarId: string) => {
		if (!confirm("Are you sure you want to delete this webinar?")) return;

		try {
			await deleteMutation.mutateAsync(webinarId);
		} catch (error) {
			console.error("Error deleting webinar:", error);
			alert("Failed to delete webinar. Please try again.");
		}
	};

	const handleFormClose = () => {
		setShowCreateForm(false);
		setEditingWebinar(null);
	};

	if (showCreateForm) {
		return (
			<CreateWebinarForm
				experienceId={experienceId}
				companyId={companyId}
				initialData={editingWebinar || undefined}
				onClose={handleFormClose}
			/>
		);
	}

	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-[400px] p-4">
				<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-11" />
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-4 p-4 md:gap-6 md:p-6 lg:gap-8 lg:p-8 max-w-7xl mx-auto w-full relative">
			{/* Blue glow gradient - flows from top-left corner */}
			<div className="fixed top-0 left-0 w-[800px] h-[700px] bg-gradient-to-br from-blue-9/50 via-blue-10/30 to-blue-11/10 rounded-full blur-[150px] pointer-events-none opacity-70" 
				style={{ 
					transform: 'translate(-25%, -25%)',
					zIndex: 0
				}} />
			
			{/* Header - Mobile-first: stack on mobile, side-by-side on desktop */}
			<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between relative z-10">
				<div className="relative">
					<Heading 
						size="7" 
						weight="bold" 
						className="sm:text-8"
						style={{
							fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI (Custom)', Roboto, 'Helvetica Neue', 'Open Sans (Custom)', system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji'"
						}}
					>
						Webinars
					</Heading>
				</div>
				{canCreateWebinars && (
					<Button
						variant="surface"
						size="2"
						onClick={handleCreate}
						className="flex items-center justify-center gap-2 w-full sm:w-auto"
					>
						<Plus className="h-4 w-4" />
						<span className="sm:inline">Schedule Webinar</span>
					</Button>
				)}
			</div>

		{(current || upcoming.length > 0) ? (
			<div className="flex flex-col gap-3 md:gap-4 relative z-10">
				{current && (
					<Heading size="5" weight="bold" className="sm:text-6">
						Live Now
					</Heading>
				)}
				{current && (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
						{isHost ? (
							<AdminWebinarCard
								key={current.id}
								webinar={current}
								onEdit={handleEdit}
								onDelete={handleDelete}
								onJoin={onJoinWebinar}
							/>
						) : (
							<WebinarCard
								key={current.id}
								webinar={current}
								onJoin={onJoinWebinar}
								isHost={isHost}
								onEdit={handleEdit}
							/>
						)}
					</div>
				)}
				{upcoming.length > 0 && (
					<>
						<Heading size="5" weight="bold" className="sm:text-6">
							Upcoming Webinars
						</Heading>
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
							{upcoming.map((webinar) =>
								isHost ? (
									<AdminWebinarCard
										key={webinar.id}
										webinar={webinar}
										onEdit={handleEdit}
										onDelete={handleDelete}
										onJoin={onJoinWebinar}
									/>
								) : (
									<WebinarCard
										key={webinar.id}
										webinar={webinar}
										onJoin={onJoinWebinar}
										isHost={isHost}
										onEdit={handleEdit}
									/>
								),
							)}
						</div>
					</>
				)}
			</div>
		) : (
				<div className="text-center py-8 md:py-12 px-4 bg-gray-a2/40 backdrop-blur-sm border border-white/10 rounded-lg relative z-10">
					<Heading size="4" weight="bold" className="mb-2 sm:text-5">
						No Webinars Scheduled
					</Heading>
					<Text size="2" color="gray" className="sm:text-3">
						There are no upcoming webinars for this experience at the moment.
						{canCreateWebinars && " Click 'Schedule Webinar' to create your first one."}
					</Text>
				</div>
			)}
		</div>
	);
}
