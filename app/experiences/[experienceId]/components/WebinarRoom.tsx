"use client";

import { useEffect, useRef, useState } from "react";
import DailyIframe, { type DailyCall } from "@daily-co/daily-js";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Heading, Text, DropdownMenu } from "@whop/react/components";
import {
	Volume2,
	VolumeX,
	Video,
	VideoOff,
	Monitor,
	Settings,
	MessageSquare,
	ChevronRight,
	Clock,
	CheckCircle,
	AlertTriangle,
	Users,
	Signal,
	RefreshCw,
	Minimize2,
	Maximize2,
	X,
} from "lucide-react";
import { Chat } from "./Chat";
import { QnA } from "./QnA";
import { ParticipantModeration } from "./ParticipantModeration";
import { Reactions } from "./Reactions";
import { Polls } from "./Polls";
import { Handouts } from "./Handouts";
import { AttendeeList } from "./AttendeeList";
import { CTAPopup } from "./CTAPopup";
import { WaitRoom } from "./WaitRoom";

interface WebinarRoomProps {
	roomUrl: string;
	token: string;
	userName: string;
	userId: string;
	isHost: boolean;
	webinarId?: string;
	userEmail?: string;
	userRole?: "host" | "co-host" | "moderator" | "attendee";
	onLeave: () => void;
}

const enableDebugLogging = process.env.NEXT_PUBLIC_DAILY_DEBUG === "true";

type DailyWindow = Window & {
	__dailyCallFrame?: DailyCall | null;
};

const getDailyWindow = (): DailyWindow | undefined => {
	if (typeof window === "undefined") return undefined;
	return window as DailyWindow;
};

const destroyExistingGlobalFrame = async () => {
	const dailyWindow = getDailyWindow();
	const existing = dailyWindow?.__dailyCallFrame;
	if (!existing) return;

	try {
		await existing.leave().catch(() => {});
	} catch (leaveError) {
		console.warn("Error leaving existing Daily call frame before destroy", leaveError);
	}

	try {
		await existing.destroy();
	} catch (destroyError) {
		console.warn("Error destroying existing Daily call frame", destroyError);
	}

	if (dailyWindow) {
		dailyWindow.__dailyCallFrame = null;
	}
};

// Reactions Button Component with Popup
function ReactionsButton({ webinarId, userId, webinarStartTime }: { webinarId: string; userId: string; webinarStartTime?: string }) {
	const [showReactions, setShowReactions] = useState(false);
	const reactionsRef = useRef<HTMLDivElement>(null);
	const reactions = ["👍", "❤️", "😂", "🎉", "🔥", "👏", "💯", "🚀"];

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (reactionsRef.current && !reactionsRef.current.contains(event.target as Node)) {
				setShowReactions(false);
			}
		};

		if (showReactions) {
			document.addEventListener("mousedown", handleClickOutside);
			return () => {
				document.removeEventListener("mousedown", handleClickOutside);
			};
		}
	}, [showReactions]);

	const sendReaction = async (reactionType: string) => {
		try {
			await fetch(`/api/webinar-reactions/${webinarId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					reactionType,
					durationMs: 3000,
				}),
			});
			setShowReactions(false);
		} catch (error) {
			console.error("Error sending reaction:", error);
		}
	};

	return (
		<div className="relative" ref={reactionsRef}>
			<Button
				variant="soft"
				color="gray"
				size="2"
				onClick={() => setShowReactions(!showReactions)}
				className="flex items-center justify-center w-10 h-10 rounded-full shadow-sm hover:shadow-md transition-all p-0 aspect-square"
				title="Reactions"
			>
				<span className="text-lg">👍</span>
			</Button>
			{showReactions && (
				<div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-gray-a3/95 backdrop-blur-md rounded-xl shadow-2xl border border-white/10 p-3 z-[100]">
					<div className="flex items-center gap-1.5 flex-wrap justify-center min-w-[200px]">
						{reactions.map((reaction) => (
							<button
								key={reaction}
								onClick={() => sendReaction(reaction)}
								className="text-xl sm:text-2xl p-2 w-10 h-10 rounded-lg hover:bg-gray-a4/60 transition-all hover:scale-110 active:scale-95 flex items-center justify-center"
								title={reaction}
							>
								{reaction}
							</button>
						))}
					</div>
				</div>
			)}
		</div>
	);
}

interface ParticipantVideoElement {
	sessionId: string;
	videoElement: HTMLVideoElement;
	audioElement: HTMLAudioElement;
	userName: string;
	isLocal: boolean;
}

export function WebinarRoom({
	roomUrl,
	token,
	userName,
	userId,
	isHost,
	webinarId,
	userEmail,
	userRole = "attendee",
	onLeave,
}: WebinarRoomProps) {
	const attendanceIdRef = useRef<string | null>(null);
	const [callObject, setCallObject] = useState<DailyCall | null>(null);
	const [dailySessionId, setDailySessionId] = useState<string | undefined>(undefined);
	const isModerator = isHost || userRole === "co-host" || userRole === "moderator";
	const [isJoined, setIsJoined] = useState(false);
	const [isVideoOn, setIsVideoOn] = useState(true);
	const [isAudioOn, setIsAudioOn] = useState(true);
	const [isScreenSharing, setIsScreenSharing] = useState(false);
	const [isFullscreen, setIsFullscreen] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const videoContainerRef = useRef<HTMLDivElement>(null);
	const [showChat, setShowChat] = useState(false);
	const [chatTab, setChatTab] = useState<"chat" | "qa" | "polls" | "handouts" | "attendees">("chat");
	const [showSettings, setShowSettings] = useState(false);
	const callObjectRef = useRef<DailyCall | null>(null);
	const videoElementsRef = useRef<Map<string, ParticipantVideoElement>>(new Map());
	const [availableDevices, setAvailableDevices] = useState<{
		cameras: { deviceId: string; label: string }[];
		microphones: { deviceId: string; label: string }[];
	}>({ cameras: [], microphones: [] });
	const [selectedCamera, setSelectedCamera] = useState<string | null>(null);
	const [selectedMicrophone, setSelectedMicrophone] = useState<string | null>(null);
	const [participantCount, setParticipantCount] = useState(0);
	const [networkQuality, setNetworkQuality] = useState<"good" | "poor" | "unknown">("unknown");
	const [accessState, setAccessState] = useState<"unknown" | "full" | "lobby">("unknown");
	const [showPreJoin, setShowPreJoin] = useState(true);
	const [preJoinVideoEnabled, setPreJoinVideoEnabled] = useState(true);
	const [preJoinAudioEnabled, setPreJoinAudioEnabled] = useState(true);
	const preJoinVideoRef = useRef<HTMLVideoElement>(null);
	const preJoinStreamRef = useRef<MediaStream | null>(null);
	const eventHandlersRef = useRef<Array<{
		event: string;
		handler: (...args: unknown[]) => void;
	}>>([]);
	const [webinarStartTime, setWebinarStartTime] = useState<string | null>(null);
	const [waitRoomMode, setWaitRoomMode] = useState(false);
	const [isInWaitRoom, setIsInWaitRoom] = useState(false);
	const [isRecording, setIsRecording] = useState(false);
	const recordingRef = useRef<{ id: string | null; dailyRecordingId: string | null }>({ id: null, dailyRecordingId: null });

	// Fetch webinar data to get start time
	useEffect(() => {
		if (!webinarId) return;

		const fetchWebinarData = async () => {
			try {
				const response = await fetch(`/api/webinars/${webinarId}`);
				if (response.ok) {
					const data = await response.json();
					if (data.webinar?.scheduled_at) {
						setWebinarStartTime(data.webinar.scheduled_at);
					}
				}
			} catch (error) {
				console.error("Error fetching webinar data:", error);
			}
		};

		fetchWebinarData();
	}, [webinarId]);

	// Check wait room status
	useEffect(() => {
		if (!webinarId || !isJoined) return;

		const checkWaitRoomStatus = async () => {
			try {
				const response = await fetch(`/api/webinar-wait-room/${webinarId}`);
				if (response.ok) {
					const data = await response.json();
					setWaitRoomMode(data.waitRoomMode || false);
					setIsInWaitRoom(!data.isStarted && data.waitRoomMode && data.userEntry?.status !== "admitted");
				}
			} catch (error) {
				console.error("Error checking wait room status:", error);
			}
		};

		checkWaitRoomStatus();
		const interval = setInterval(checkWaitRoomStatus, 3000);
		return () => clearInterval(interval);
	}, [webinarId, isJoined]);

	// Auto-start recording when webinar goes live (for hosts/moderators)
	useEffect(() => {
		if (!webinarId || !isModerator || !isJoined || !callObject || isRecording) return;

		const checkAndStartRecording = async () => {
			try {
				// Check if webinar is live
				const response = await fetch(`/api/webinars/${webinarId}`);
				if (response.ok) {
					const data = await response.json();
					if (data.webinar?.status === "live") {
						// Start recording
						try {
							callObject.startRecording({ type: "cloud" });
							
							// Create recording record
							const recordingResponse = await fetch(`/api/webinar-recording/${webinarId}`, {
								method: "POST",
								headers: {
									"Content-Type": "application/json",
								},
								body: JSON.stringify({
									action: "start",
									recordingType: "cloud",
								}),
							});

							if (recordingResponse.ok) {
								const recordingData = await recordingResponse.json();
								setIsRecording(true);
								recordingRef.current.id = recordingData.recording?.id || null;
							}
						} catch (recordError) {
							console.error("Error starting recording:", recordError);
						}
					}
				}
			} catch (error) {
				console.error("Error checking webinar status:", error);
			}
		};

		checkAndStartRecording();
		const interval = setInterval(checkAndStartRecording, 5000);
		return () => clearInterval(interval);
	}, [webinarId, isModerator, isJoined, callObject, isRecording]);

	// Calculate grid layout based on participant count
	const getGridClasses = (count: number): string => {
		if (count === 0) return "grid-cols-1";
		if (count === 1) return "grid-cols-1";
		if (count === 2) return "grid-cols-2";
		if (count === 3) return "grid-cols-2 grid-rows-2";
		if (count === 4) return "grid-cols-2 grid-rows-2";
		if (count <= 6) return "grid-cols-3 grid-rows-2";
		if (count <= 9) return "grid-cols-3 grid-rows-3";
		if (count <= 12) return "grid-cols-4 grid-rows-3";
		if (count <= 16) return "grid-cols-4 grid-rows-4";
		return "grid-cols-5 grid-rows-4";
	};

	// Setup pre-join camera preview
	useEffect(() => {
		if (!showPreJoin) return;

		const setupPreJoinPreview = async () => {
			try {
				// Get user media for preview
				const stream = await navigator.mediaDevices.getUserMedia({
					video: preJoinVideoEnabled,
					audio: preJoinAudioEnabled,
				});

				preJoinStreamRef.current = stream;

				if (preJoinVideoRef.current && preJoinVideoEnabled) {
					preJoinVideoRef.current.srcObject = stream;
				}

				// Enumerate devices for settings
				const devices = await navigator.mediaDevices.enumerateDevices();
				const cameras = devices
					.filter((d) => d.kind === "videoinput")
					.map((d) => ({ deviceId: d.deviceId, label: d.label || `Camera ${d.deviceId.slice(0, 8)}` }));
				const microphones = devices
					.filter((d) => d.kind === "audioinput")
					.map((d) => ({ deviceId: d.deviceId, label: d.label || `Microphone ${d.deviceId.slice(0, 8)}` }));
				setAvailableDevices({ cameras, microphones });
			} catch (err) {
				console.error("Failed to setup pre-join preview:", err);
				setError("Failed to access camera/microphone. Please check permissions.");
			}
		};

		setupPreJoinPreview();

		return () => {
			// Cleanup preview stream
			if (preJoinStreamRef.current) {
				preJoinStreamRef.current.getTracks().forEach((track) => track.stop());
				preJoinStreamRef.current = null;
			}
			if (preJoinVideoRef.current) {
				preJoinVideoRef.current.srcObject = null;
			}
		};
	}, [showPreJoin]); // Removed preJoinVideoEnabled and preJoinAudioEnabled from dependencies

	// Toggle pre-join video preview
	const togglePreJoinVideo = async () => {
		const newState = !preJoinVideoEnabled;
		setPreJoinVideoEnabled(newState);

		try {
			if (newState) {
				// Turning video ON
				const stream = await navigator.mediaDevices.getUserMedia({ video: true });
				const videoTrack = stream.getVideoTracks()[0];
				
				// Stop old video tracks if they exist
				if (preJoinStreamRef.current) {
					const oldVideoTracks = preJoinStreamRef.current.getVideoTracks();
					oldVideoTracks.forEach((track) => {
						track.stop();
						preJoinStreamRef.current?.removeTrack(track);
					});
				}

				// Create or update stream
				if (!preJoinStreamRef.current) {
					preJoinStreamRef.current = new MediaStream();
				}

				// Preserve existing audio tracks
				const existingAudioTracks = preJoinStreamRef.current.getAudioTracks();
				
				// Remove all tracks temporarily
				const audioTracksToPreserve: MediaStreamTrack[] = [];
				existingAudioTracks.forEach((track) => {
					audioTracksToPreserve.push(track);
					preJoinStreamRef.current?.removeTrack(track);
				});

				// Add new video track
				preJoinStreamRef.current.addTrack(videoTrack);

				// Re-add audio tracks
				audioTracksToPreserve.forEach((track) => {
					preJoinStreamRef.current?.addTrack(track);
				});

				// Update video element
				if (preJoinVideoRef.current) {
					preJoinVideoRef.current.srcObject = preJoinStreamRef.current;
				}
			} else {
				// Turning video OFF
				if (preJoinStreamRef.current) {
					const videoTracks = preJoinStreamRef.current.getVideoTracks();
					videoTracks.forEach((track) => {
						track.stop();
						preJoinStreamRef.current?.removeTrack(track);
					});
				}
				if (preJoinVideoRef.current) {
					preJoinVideoRef.current.srcObject = null;
				}
			}
		} catch (err) {
			console.error("Failed to toggle video:", err);
			setPreJoinVideoEnabled(!newState); // Revert state on error
		}
	};

	// Toggle pre-join audio preview
	const togglePreJoinAudio = async () => {
		const newState = !preJoinAudioEnabled;
		setPreJoinAudioEnabled(newState);

		try {
			if (newState) {
				// Turning audio ON
				const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
				const audioTrack = stream.getAudioTracks()[0];

				// Stop old audio tracks if they exist
				if (preJoinStreamRef.current) {
					const oldAudioTracks = preJoinStreamRef.current.getAudioTracks();
					oldAudioTracks.forEach((track) => {
						track.stop();
						preJoinStreamRef.current?.removeTrack(track);
					});
				}

				// Create or update stream
				if (!preJoinStreamRef.current) {
					preJoinStreamRef.current = new MediaStream();
				}

				// Preserve existing video tracks
				const existingVideoTracks = preJoinStreamRef.current.getVideoTracks();
				const videoTracksToPreserve: MediaStreamTrack[] = [];
				existingVideoTracks.forEach((track) => {
					videoTracksToPreserve.push(track);
					preJoinStreamRef.current?.removeTrack(track);
				});

				// Add new audio track
				preJoinStreamRef.current.addTrack(audioTrack);

				// Re-add video tracks
				videoTracksToPreserve.forEach((track) => {
					preJoinStreamRef.current?.addTrack(track);
				});
			} else {
				// Turning audio OFF
				if (preJoinStreamRef.current) {
					const audioTracks = preJoinStreamRef.current.getAudioTracks();
					audioTracks.forEach((track) => {
						track.stop();
						preJoinStreamRef.current?.removeTrack(track);
					});
				}
			}
		} catch (err) {
			console.error("Failed to toggle audio:", err);
			setPreJoinAudioEnabled(!newState); // Revert state on error
		}
	};

	// Handle join button click
	const handleJoinCall = () => {
		// Stop preview stream
		if (preJoinStreamRef.current) {
			preJoinStreamRef.current.getTracks().forEach((track) => track.stop());
			preJoinStreamRef.current = null;
		}
		if (preJoinVideoRef.current) {
			preJoinVideoRef.current.srcObject = null;
		}

		// Update initial video/audio state
		setIsVideoOn(preJoinVideoEnabled);
		setIsAudioOn(preJoinAudioEnabled);

		// Hide pre-join screen
		setShowPreJoin(false);
	};

	// Create video/audio elements for participants
	const createParticipantElements = (sessionId: string, userName: string, isLocal: boolean) => {
		if (videoElementsRef.current.has(sessionId)) {
			return videoElementsRef.current.get(sessionId)!;
		}

		const videoElement = document.createElement("video");
		videoElement.autoplay = true;
		videoElement.playsInline = true;
		videoElement.muted = isLocal; // Local video should be muted to prevent feedback
		videoElement.style.width = "100%";
		videoElement.style.height = "100%";
		videoElement.style.objectFit = "cover";
		videoElement.style.borderRadius = "0.5rem";

		const audioElement = document.createElement("audio");
		audioElement.autoplay = true;
		audioElement.muted = isLocal; // Local audio should be muted to prevent feedback

		const container = document.createElement("div");
		container.className = "relative w-full h-full rounded-xl overflow-hidden bg-gradient-to-br from-gray-a4 to-gray-a5 shadow-lg hover:shadow-xl transition-shadow";
		container.setAttribute("data-session-id", sessionId);
		container.style.aspectRatio = "16/9";

		// Add participant name overlay
		const nameOverlay = document.createElement("div");
		nameOverlay.className = "absolute bottom-3 left-3 px-3 py-1.5 rounded-lg bg-black/70 backdrop-blur-sm text-white text-sm font-medium shadow-lg";
		nameOverlay.textContent = userName;
		container.appendChild(nameOverlay);

		container.appendChild(videoElement);
		// Audio element doesn't need to be visible
		document.body.appendChild(audioElement);

		if (videoContainerRef.current) {
			videoContainerRef.current.appendChild(container);
		}

		const participantElement: ParticipantVideoElement = {
			sessionId,
			videoElement,
			audioElement,
			userName,
			isLocal,
		};

		videoElementsRef.current.set(sessionId, participantElement);
		return participantElement;
	};

	// Remove participant elements
	const removeParticipantElements = (sessionId: string) => {
		const participant = videoElementsRef.current.get(sessionId);
		if (!participant) return;

		// Remove video element container
		const container = videoContainerRef.current?.querySelector(`[data-session-id="${sessionId}"]`);
		if (container) {
			container.remove();
		}

		// Remove audio element
		if (participant.audioElement.parentNode) {
			participant.audioElement.parentNode.removeChild(participant.audioElement);
		}

		videoElementsRef.current.delete(sessionId);
	};

	// Attach track to video/audio element
	const attachTrack = (sessionId: string, track: MediaStreamTrack, trackType: "video" | "audio") => {
		const participant = videoElementsRef.current.get(sessionId);
		if (!participant) return;

		// Don't attach audio tracks for local participant to prevent feedback
		if (trackType === "audio" && participant.isLocal) {
			return;
		}

		const element = trackType === "video" ? participant.videoElement : participant.audioElement;
		const stream = new MediaStream([track]);
		
		if (trackType === "video") {
			participant.videoElement.srcObject = stream;
		} else {
			participant.audioElement.srcObject = stream;
		}
	};

	useEffect(() => {
		if (!roomUrl) {
			setError("Room URL is required");
			return;
		}

		if (!roomUrl.startsWith("https://") && !roomUrl.startsWith("http://")) {
			setError(`Invalid room URL format: ${roomUrl}`);
			return;
		}

		if (!token) {
			setError("Missing meeting token. Please refresh and try again.");
			return;
		}

		let call: DailyCall | null = null;
		let isMounted = true;
		let joinedHandled = false;
		eventHandlersRef.current = [];

		const createCall = async () => {
			try {
				const dailyWindow = getDailyWindow();
				await destroyExistingGlobalFrame();

				// Clean up existing call
				if (callObjectRef.current) {
					try {
						await callObjectRef.current.leave().catch(() => {});
						await callObjectRef.current.destroy();
					} catch (err) {
						console.warn("Error cleaning up existing call:", err);
					}
					callObjectRef.current = null;
				}

				// Clear video container if it exists
				if (videoContainerRef.current) {
					videoContainerRef.current.innerHTML = "";
				}
				videoElementsRef.current.clear();

				if (enableDebugLogging) {
					console.log("🎬 Creating Daily.co call object...");
				}

				// Create call object (no iframe!)
				call = DailyIframe.createCallObject({
					url: roomUrl,
					token,
					userName,
					userData: {
						userId,
						isHost,
					},
					dailyConfig: {
						preferH264: true,
					},
					subscribeToTracksAutomatically: true,
					startVideoOff: !preJoinVideoEnabled,
					startAudioOff: !preJoinAudioEnabled,
				});

				callObjectRef.current = call;
				if (dailyWindow) {
					dailyWindow.__dailyCallFrame = call;
				}
				setCallObject(call);

				if (enableDebugLogging) {
					console.log("✅ Call object created");
				}

				// Handle track-started event
				const onTrackStarted = (event: any) => {
					if (enableDebugLogging) {
						console.log("📹 Track started:", event);
					}
					if (!isMounted || !call) return;

					const { participant, track } = event;
					if (!participant || !track) return;

					const sessionId = participant.session_id;
					const participantData = call.participants()[sessionId];
					if (!participantData) return;

					// Create elements if they don't exist
					if (!videoElementsRef.current.has(sessionId)) {
						createParticipantElements(
							sessionId,
							participantData.user_name || "Unknown",
							participantData.local || false,
						);
					}

					// Attach track
					if (track.kind === "video") {
						// Check if this is a screen share track
						if (track.source === "screen" || track.mediaStreamTrack?.label?.includes("screen")) {
							attachTrack(sessionId, track, "video");
							// Update screen sharing state for local participant
							if (participantData.local) {
								setIsScreenSharing(true);
								setError(null); // Clear any previous errors
							}
						} else {
							attachTrack(sessionId, track, "video");
							// Update state for local participant
							if (participantData.local) {
								setIsVideoOn(true);
							}
						}
					} else if (track.kind === "audio") {
						attachTrack(sessionId, track, "audio");
						// Update state for local participant
						if (participantData.local) {
							setIsAudioOn(true);
						}
					}
				};

				// Handle track-stopped event
				const onTrackStopped = (event: any) => {
					if (enableDebugLogging) {
						console.log("📹 Track stopped:", event);
					}
					if (!isMounted || !call) return;

					const { participant, track } = event;
					if (!participant) return;

					const sessionId = participant.session_id;
					const participantData = call.participants()[sessionId];
					const participantEl = videoElementsRef.current.get(sessionId);
					if (!participantEl) return;

					if (track.kind === "video") {
						// Check if this is a screen share track
						if (track.source === "screen" || track.mediaStreamTrack?.label?.includes("screen")) {
							participantEl.videoElement.srcObject = null;
							// Update screen sharing state for local participant
							if (participantData?.local) {
								setIsScreenSharing(false);
							}
						} else {
							participantEl.videoElement.srcObject = null;
							// Update state for local participant
							if (participantData?.local) {
								setIsVideoOn(false);
							}
						}
					} else if (track.kind === "audio") {
						participantEl.audioElement.srcObject = null;
						// Update state for local participant
						if (participantData?.local) {
							setIsAudioOn(false);
						}
					}
				};

				// Handle joined-meeting event
				const onJoinedMeeting = async () => {
					if (enableDebugLogging) {
						console.log("✅ Successfully joined meeting");
					}
					joinedHandled = true;
					if (isMounted && call) {
						setIsJoined(true);
						setError(null);
						// Get local session ID from participants
						const participants = call.participants();
						const localParticipant = Object.values(participants).find((p) => p.local);
						if (localParticipant) {
							setDailySessionId(localParticipant.session_id);
						}

						// Track attendance if webinarId is provided
						// Email will be looked up from registration or can be passed
						if (webinarId) {
							try {
								// Try to get email from user data or use a placeholder
								// In a real implementation, you'd get this from Whop user data
								const response = await fetch("/api/attendance", {
									method: "POST",
									headers: {
										"Content-Type": "application/json",
									},
									body: JSON.stringify({
										action: "join",
										webinarId,
										email: userEmail || `${userId}@whop.user`, // Fallback email
										userId,
										dailySessionId: dailySessionId,
									}),
								});

								if (response.ok) {
									const data = await response.json();
									attendanceIdRef.current = data.attendance?.id || null;

									// Track device info
									if (attendanceIdRef.current) {
										try {
											// Detect device type and browser
											const userAgent = navigator.userAgent;
											const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
											const isTablet = /iPad|Android/i.test(userAgent) && !isMobile;
											const deviceType = isMobile ? "mobile" : isTablet ? "tablet" : "desktop";

											let platform: string | null = null;
											if (/iPhone|iPad|iPod/i.test(userAgent)) {
												platform = "iOS";
											} else if (/Android/i.test(userAgent)) {
												platform = "Android";
											} else if (/Win/i.test(userAgent)) {
												platform = "Windows";
											} else if (/Mac/i.test(userAgent)) {
												platform = "macOS";
											} else if (/Linux/i.test(userAgent)) {
												platform = "Linux";
											} else if (/CrOS/i.test(userAgent)) {
												platform = "Chrome OS";
											}

											let browser: string | null = null;
											if (/Chrome/i.test(userAgent) && !/Edg|OPR/i.test(userAgent)) {
												browser = "Chrome";
											} else if (/Firefox/i.test(userAgent)) {
												browser = "Firefox";
											} else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
												browser = "Safari";
											} else if (/Edg/i.test(userAgent)) {
												browser = "Edge";
											} else if (/OPR/i.test(userAgent)) {
												browser = "Opera";
											}

											await fetch("/api/analytics/device", {
												method: "POST",
												headers: {
													"Content-Type": "application/json",
												},
												body: JSON.stringify({
													attendanceId: attendanceIdRef.current,
													webinarId,
													userId,
													deviceType,
													platform,
													browser,
													userAgent,
													screenWidth: window.screen.width,
													screenHeight: window.screen.height,
												}),
											});

											// Track join event
											await fetch("/api/analytics/event", {
												method: "POST",
												headers: {
													"Content-Type": "application/json",
												},
												body: JSON.stringify({
													webinarId,
													userId,
													eventType: "join",
													deviceInfo: {
														deviceType,
														platform,
														browser,
													},
													browserInfo: {
														userAgent,
														screenWidth: window.screen.width,
														screenHeight: window.screen.height,
													},
												}),
											});
										} catch (err) {
											console.error("Failed to track device info:", err);
										}
									}
								}
							} catch (err) {
								console.error("Failed to track attendance:", err);
							}
						}

						try {
							// Enumerate devices
							const devices = await call.enumerateDevices();
							const cameras = devices.devices
								.filter((d) => d.kind === "videoinput")
								.map((d) => ({ deviceId: d.deviceId, label: d.label || `Camera ${d.deviceId.slice(0, 8)}` }));
							const microphones = devices.devices
								.filter((d) => d.kind === "audioinput")
								.map((d) => ({ deviceId: d.deviceId, label: d.label || `Microphone ${d.deviceId.slice(0, 8)}` }));
							setAvailableDevices({ cameras, microphones });

							// Update participant count
							const participants = call.participants();
							const count = Object.keys(participants).length;
							setParticipantCount(count);
							
							// Set initial grid layout
							if (videoContainerRef.current) {
								const gridClasses = getGridClasses(count);
								videoContainerRef.current.className = `h-full w-full overflow-auto rounded-2xl bg-gray-a3 relative grid gap-4 p-4 ${gridClasses}`;
							}
							
							// Check access state
							checkAccessState();

							// Setup local participant video/audio
							const localParticipant = Object.values(participants).find((p) => p.local);
							if (localParticipant) {
								const localSessionId = localParticipant.session_id;
								createParticipantElements(
									localSessionId,
									localParticipant.user_name || userName,
									true,
								);

								// Attach local tracks if available
								if (localParticipant.tracks.video.persistentTrack) {
									attachTrack(localSessionId, localParticipant.tracks.video.persistentTrack, "video");
								}
								if (localParticipant.tracks.audio.persistentTrack) {
									attachTrack(localSessionId, localParticipant.tracks.audio.persistentTrack, "audio");
								}
							}
						} catch (err) {
							console.error("Error configuring UI after join:", err);
						}
					}
				};

				call.on("track-started", onTrackStarted);
				eventHandlersRef.current.push({ event: "track-started", handler: onTrackStarted });

				call.on("track-stopped", onTrackStopped);
				eventHandlersRef.current.push({ event: "track-stopped", handler: onTrackStopped });

				call.on("joined-meeting", onJoinedMeeting);
				eventHandlersRef.current.push({ event: "joined-meeting", handler: onJoinedMeeting });

				// Handle access state changes
				call.on("access-state-updated", (event) => {
					if (enableDebugLogging) {
						console.log("🔐 Access state updated:", event);
					}
					if (isMounted && event?.access) {
						const access = event.access as any;
						if (access === "full" || (typeof access === "object" && access.level === "full")) {
							setAccessState("full");
						} else if (typeof access === "object" && access.level === "lobby") {
							setAccessState("lobby");
						} else {
							setAccessState("unknown");
						}
					}
				});

				// Request access if in lobby
				const checkAccessState = () => {
					if (call && isMounted) {
					const accessState = call.accessState();
					if (enableDebugLogging) {
						console.log("🔐 Current access state:", accessState);
					}
					
					const access = accessState.access as any;
					if (access === "full" || (typeof access === "object" && access.level === "full")) {
						setAccessState("full");
					} else if (access === "unknown" || (typeof access === "object" && access.level === "lobby")) {
						setAccessState("lobby");
						// Request full access
						if (isHost) {
							call.requestAccess({} as any).catch((err) => {
								console.warn("Failed to request access:", err);
							});
						}
						}
					}
				};

				call.on("left-meeting", () => {
					if (enableDebugLogging) {
						console.log("👋 Left meeting");
					}
					if (isMounted) {
						setIsJoined(false);
						// Clean up all video elements
						videoElementsRef.current.forEach((_, sessionId) => {
							removeParticipantElements(sessionId);
						});
					}
				});

				call.on("error", (err) => {
					console.error("❌ Daily.co error:", err);
					const errorMsg = (err as any)?.errorMsg || (err as any)?.message || JSON.stringify(err);
					if (isMounted) {
						setError(errorMsg);
					}
				});

				// Note: camera-error and microphone-error are not standard Daily.co events
				// These errors are typically handled via the general "error" event handler above

				call.on("participant-left", (event) => {
					if (enableDebugLogging) {
						console.log("👋 Participant left:", event?.participant?.user_name);
					}
					if (isMounted && call && event?.participant?.session_id) {
						removeParticipantElements(event.participant.session_id);
						const participants = call.participants();
						const count = Object.keys(participants).length;
						setParticipantCount(count);
						// Update grid layout
						if (videoContainerRef.current) {
							const gridClasses = getGridClasses(count);
							videoContainerRef.current.className = `h-full w-full overflow-auto rounded-2xl bg-gray-a3 relative grid gap-4 p-4 ${gridClasses}`;
						}
					}
				});

				call.on("participant-joined", (event) => {
					if (enableDebugLogging) {
						console.log("👋 Participant joined:", event?.participant?.user_name);
					}
					if (isMounted && call) {
						const participants = call.participants();
						const count = Object.keys(participants).length;
						setParticipantCount(count);
						// Update grid layout
						if (videoContainerRef.current) {
							const gridClasses = getGridClasses(count);
							videoContainerRef.current.className = `h-full w-full overflow-auto rounded-2xl bg-gray-a3 relative grid gap-4 p-4 ${gridClasses}`;
						}
					}
				});

				call.on("participant-updated", (event) => {
					if (isMounted && call) {
						// Update video/audio tracks if they changed
						if (event?.participant) {
							const sessionId = event.participant.session_id;
							const participantData = call.participants()[sessionId];
							if (participantData) {
								// Update screen share track (check screenVideo first)
								if (participantData.tracks.screenVideo?.persistentTrack) {
									attachTrack(sessionId, participantData.tracks.screenVideo.persistentTrack, "video");
									// Update screen sharing state for local participant
									if (participantData.local) {
										setIsScreenSharing(participantData.tracks.screenVideo.state === "playable");
										setError(null); // Clear any previous errors
									}
								} else if (participantData.local && participantData.tracks.screenVideo) {
									// Screen share stopped/not available
									setIsScreenSharing(false);
								}
								// Update video track
								if (participantData.tracks.video.persistentTrack) {
									attachTrack(sessionId, participantData.tracks.video.persistentTrack, "video");
									// Update state for local participant
									if (participantData.local) {
										setIsVideoOn(participantData.tracks.video.state === "playable");
									}
								} else if (participantData.local) {
									// Track stopped/not available
									setIsVideoOn(false);
								}
								// Update audio track
								if (participantData.tracks.audio.persistentTrack) {
									attachTrack(sessionId, participantData.tracks.audio.persistentTrack, "audio");
									// Update state for local participant
									if (participantData.local) {
										setIsAudioOn(participantData.tracks.audio.state === "playable");
									}
								} else if (participantData.local) {
									// Track stopped/not available
									setIsAudioOn(false);
								}
							}
						}
					}
				});

				call.on("network-quality-change", (event) => {
					if (enableDebugLogging) {
						console.log("📶 Network quality change:", event);
					}
					if (isMounted && event?.threshold) {
						const quality = event.threshold === 'good' ? 'good' : event.threshold === 'low' || event.threshold === 'very-low' ? 'poor' : 'good';
						setNetworkQuality(quality);
					}
				});

				// Don't join automatically - wait for user to click join button
				// Join will be triggered by handleJoinCall function
			} catch (err: any) {
				console.error("❌ Failed to create call object:", err);
				if (isMounted) {
					setError(
						err?.message ||
							err?.errorMsg ||
							"Failed to initialize video call. Please refresh and try again.",
					);
				}
			}
		};

		createCall();

		return () => {
			isMounted = false;

			// Clean up event listeners
			const callToClean = callObjectRef.current || call;
			if (callToClean && eventHandlersRef.current.length > 0) {
				for (const { event, handler } of eventHandlersRef.current) {
					try {
						callToClean.off(event as any, handler);
					} catch (err) {
						if (enableDebugLogging) {
							console.warn(`Error removing ${event} listener:`, err);
						}
					}
				}
				eventHandlersRef.current = [];
			}

			// Clean up call object
			if (callObjectRef.current) {
				try {
					callObjectRef.current.leave().catch(() => {});
					callObjectRef.current.destroy().catch(() => {});
					callObjectRef.current = null;
				} catch (err) {
					console.error("Error destroying call object:", err);
				}
			}

			// Clean up local call variable
			if (call) {
				try {
					call.leave().catch(() => {});
					call.destroy().catch(() => {});
				} catch (err) {
					console.error("Error destroying call:", err);
				}
			}

			// Clean up video elements
			videoElementsRef.current.forEach((_, sessionId) => {
				removeParticipantElements(sessionId);
			});

			const dailyWindow = getDailyWindow();
			if (dailyWindow) {
				dailyWindow.__dailyCallFrame = null;
			}
		};
	}, [roomUrl, token, userName, userId, isHost]);

	// Update grid layout when participant count changes
	useEffect(() => {
		if (videoContainerRef.current && participantCount > 0) {
			const gridClasses = getGridClasses(participantCount);
			videoContainerRef.current.className = `h-full w-full overflow-auto rounded-2xl bg-gray-a3 relative grid gap-4 p-4 ${gridClasses}`;
		}
	}, [participantCount]);

	const toggleVideo = async () => {
		const call = callObjectRef.current;
		if (!call) {
			console.warn("Cannot toggle video: call object is null");
			return;
		}
		try {
			const newState = !isVideoOn;
			await call.setLocalVideo(newState);
			// Optimistically update - track events will confirm
			setIsVideoOn(newState);
			
			// If enabling, wait a bit and check for track
			if (newState) {
				setTimeout(() => {
					const participants = call.participants();
					const localParticipant = Object.values(participants).find((p) => p.local);
					if (localParticipant) {
						const sessionId = localParticipant.session_id;
						// Check if track is available
						if (localParticipant.tracks.video.persistentTrack) {
							attachTrack(sessionId, localParticipant.tracks.video.persistentTrack, "video");
							setIsVideoOn(true);
						} else {
							// Track might not be ready yet, wait for track-started event
							console.log("Video track not yet available, waiting for track-started event...");
						}
					}
				}, 500);
			}
		} catch (err) {
			console.error("Failed to toggle video:", err);
		}
	};

	const toggleAudio = async () => {
		const call = callObjectRef.current;
		if (!call) {
			console.warn("Cannot toggle audio: call object is null");
			return;
		}
		try {
			const newState = !isAudioOn;
			await call.setLocalAudio(newState);
			// Optimistically update - track events will confirm
			setIsAudioOn(newState);
			
			// If enabling, wait a bit and check for track
			if (newState) {
				setTimeout(() => {
					const participants = call.participants();
					const localParticipant = Object.values(participants).find((p) => p.local);
					if (localParticipant) {
						const sessionId = localParticipant.session_id;
						// Check if track is available
						if (localParticipant.tracks.audio.persistentTrack) {
							attachTrack(sessionId, localParticipant.tracks.audio.persistentTrack, "audio");
							setIsAudioOn(true);
						} else {
							// Track might not be ready yet, wait for track-started event
							console.log("Audio track not yet available, waiting for track-started event...");
						}
					}
				}, 500);
			}
		} catch (err) {
			console.error("Failed to toggle audio:", err);
		}
	};

	const toggleScreenShare = async () => {
		const call = callObjectRef.current;
		if (!call || !isHost) return;
		try {
			if (isScreenSharing) {
				await call.stopScreenShare();
				// State will be updated via track-stopped event
			} else {
				// Start screen sharing - state will be updated via track-started or participant-updated events
				await call.startScreenShare();
				// Clear any previous errors when attempting to start
				setError(null);
			}
		} catch (err: any) {
			console.error("Failed to toggle screen share:", err);
			// Handle specific error types
			const errorMessage = err?.message || err?.errorMsg || String(err);
			if (errorMessage.includes("Permission denied") || 
			    errorMessage.includes("NotAllowedError") || 
			    errorMessage.includes("blocked-by-browser") ||
			    errorMessage.includes("permission")) {
				setError("Screen sharing permission was denied. Please allow screen sharing in your browser settings and try again.");
			} else if (errorMessage.includes("not available") || errorMessage.includes("not supported")) {
				setError("Screen sharing is not available in your browser. Please use a supported browser.");
			} else {
				setError("Failed to start screen sharing. Please try again.");
			}
			// Ensure state is reset on error
			setIsScreenSharing(false);
		}
	};

	const toggleFullscreen = async () => {
		if (!videoContainerRef.current) return;
		try {
			if (isFullscreen) {
				if (document.exitFullscreen) {
					await document.exitFullscreen();
				}
				setIsFullscreen(false);
			} else {
				if (videoContainerRef.current.requestFullscreen) {
					await videoContainerRef.current.requestFullscreen();
					setIsFullscreen(true);
				}
			}
		} catch (err) {
			console.error("Failed to toggle fullscreen:", err);
		}
	};

	const changeCamera = async (deviceId: string) => {
		const call = callObjectRef.current;
		if (!call) return;
		try {
			await call.setInputDevicesAsync({ videoDeviceId: deviceId });
			setSelectedCamera(deviceId);
		} catch (err) {
			console.error("Failed to change camera:", err);
		}
	};

	const changeMicrophone = async (deviceId: string) => {
		const call = callObjectRef.current;
		if (!call) return;
		try {
			await call.setInputDevicesAsync({ audioDeviceId: deviceId });
			setSelectedMicrophone(deviceId);
		} catch (err) {
			console.error("Failed to change microphone:", err);
		}
	};

	const refreshDevices = async () => {
		const call = callObjectRef.current;
		if (!call) return;
		try {
			const devices = await call.enumerateDevices();
			const cameras = devices.devices
				.filter((d) => d.kind === "videoinput")
				.map((d) => ({ deviceId: d.deviceId, label: d.label || `Camera ${d.deviceId.slice(0, 8)}` }));
			const microphones = devices.devices
				.filter((d) => d.kind === "audioinput")
				.map((d) => ({ deviceId: d.deviceId, label: d.label || `Microphone ${d.deviceId.slice(0, 8)}` }));
			setAvailableDevices({ cameras, microphones });
		} catch (err) {
			console.error("Failed to refresh devices:", err);
		}
	};

	const joinCall = async () => {
		// Ensure call object exists, create it if it doesn't
		if (!callObjectRef.current) {
			try {
				if (enableDebugLogging) {
					console.log("📞 Call object not found, creating it...");
				}
				const newCall = DailyIframe.createCallObject({
					url: roomUrl,
					token,
					userName,
					userData: {
						userId,
						isHost,
					},
					dailyConfig: {
						preferH264: true,
					},
					subscribeToTracksAutomatically: true,
					startVideoOff: !preJoinVideoEnabled,
					startAudioOff: !preJoinAudioEnabled,
				});
				callObjectRef.current = newCall;
				setCallObject(newCall);
				
				// Wait a moment for the call object to be fully initialized
				await new Promise(resolve => setTimeout(resolve, 100));
			} catch (err) {
				console.error("Failed to create call object:", err);
				setError("Failed to initialize call. Please try again.");
				return;
			}
		}

		const call = callObjectRef.current;

		try {
			await call.join({
				url: roomUrl,
				token,
				userName: userName,
				userData: {
					userId,
					isHost,
				},
			});

			// Set initial video/audio state based on pre-join settings
			if (!preJoinVideoEnabled) {
				await call.setLocalVideo(false);
			}
			if (!preJoinAudioEnabled) {
				await call.setLocalAudio(false);
			}

			if (enableDebugLogging) {
				console.log("📞 Join call initiated successfully");
			}

			// Check access state after join
			setTimeout(() => {
				if (call) {
					const accessState = call.accessState();
					const access = accessState.access as any;
					if (access === "full" || (typeof access === "object" && access.level === "full")) {
						setAccessState("full");
					} else if (access === "unknown" || (typeof access === "object" && access.level === "lobby")) {
						setAccessState("lobby");
						if (isHost) {
							call.requestAccess({} as any).catch((err) => {
								console.warn("Failed to request access:", err);
							});
						}
					}
				}
			}, 500);
		} catch (joinErr: any) {
			console.error("❌ Join call failed:", joinErr);
			setError(
				joinErr?.message ||
					joinErr?.errorMsg ||
					"Failed to join room. Please check the room URL and try again.",
			);
		}
	};

	const leaveRoom = async () => {
		// Stop recording if active
		if (isRecording && callObject && recordingRef.current.id) {
			try {
				callObject.stopRecording();
				await fetch(`/api/webinar-recording/${webinarId}`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						action: "stop",
					}),
				});
			} catch (error) {
				console.error("Error stopping recording:", error);
			}
		}

		// Track leave if attendance was tracked
		if (attendanceIdRef.current) {
			try {
				const leaveResponse = await fetch("/api/attendance", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						action: "leave",
						attendanceId: attendanceIdRef.current,
					}),
				});

				if (leaveResponse.ok) {
					const leaveData = await leaveResponse.json();
					const attendance = leaveData.attendance;

					// Track dropoff if webinarId is available
					if (webinarId && attendance) {
						try {
							// Get webinar duration
							const webinarResponse = await fetch(`/api/webinars/${webinarId}`);
							if (webinarResponse.ok) {
								const webinarData = await webinarResponse.json();
								const webinar = webinarData.webinar;
								const webinarDuration = webinar?.duration_minutes || 60;

								// Calculate left_at_minute based on duration_minutes
								const leftAtMinute = attendance.duration_minutes || 0;

								// Track dropoff
								await fetch("/api/analytics/dropoff", {
									method: "POST",
									headers: {
										"Content-Type": "application/json",
									},
									body: JSON.stringify({
										attendanceId: attendanceIdRef.current,
										webinarId,
										userId,
										leftAtMinute,
										webinarDurationMinutes: webinarDuration,
									}),
								});

								// Track leave event
								await fetch("/api/analytics/event", {
									method: "POST",
									headers: {
										"Content-Type": "application/json",
									},
									body: JSON.stringify({
										webinarId,
										userId,
										eventType: "leave",
										eventData: {
											attendance_id: attendanceIdRef.current,
											duration_minutes: attendance.duration_minutes,
											status: attendance.status,
										},
									}),
								});
							}
						} catch (err) {
							console.error("Failed to track dropoff:", err);
						}
					}
				}
			} catch (err) {
				console.error("Failed to track leave:", err);
			}
		}

		const call = callObjectRef.current;
		if (call) {
			await call.leave();
			call.destroy();
			callObjectRef.current = null;
			setCallObject(null);
		}
		onLeave();
	};

	// Show pre-join screen
	if (showPreJoin) {
		// Check if user should be in wait room instead
		if (isInWaitRoom && webinarId) {
			return (
				<WaitRoom
					webinarId={webinarId}
					userId={userId}
					isModerator={isModerator}
					onAdmitted={() => {
						setIsInWaitRoom(false);
						setShowPreJoin(false);
					}}
				/>
			);
		}

		return (
			<div className="flex flex-col h-screen w-full bg-gradient-to-br from-gray-a1 via-gray-a2 to-gray-a3">
				{/* Pre-join Content */}
				<div className="flex-1 flex overflow-hidden px-6 py-6">
					<div className="flex-1 flex flex-col items-center justify-center gap-8 max-w-5xl mx-auto w-full">
						{/* Camera Preview */}
						<div className="relative w-full max-w-3xl aspect-video rounded-3xl overflow-hidden bg-gray-a4 shadow-2xl">
							{preJoinVideoEnabled ? (
								<video
									ref={preJoinVideoRef}
									autoPlay
									playsInline
									muted
									className="w-full h-full object-cover"
								/>
							) : (
								<div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-a4 to-gray-a5">
									<div className="w-32 h-32 rounded-full bg-gray-a6 flex items-center justify-center mb-4 shadow-inner">
										<VideoOff className="w-16 h-16 text-gray-9" />
									</div>
									<Text size="3" color="gray" weight="medium">
										Camera Off
									</Text>
								</div>
							)}
							{/* Preview Controls Overlay */}
							<div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex gap-3 z-10">
								<Button
									onClick={togglePreJoinVideo}
									variant="solid"
									color={preJoinVideoEnabled ? "blue" : "danger"}
									size="4"
									className="flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all p-0 aspect-square"
									title={preJoinVideoEnabled ? "Camera On" : "Camera Off"}
								>
									{preJoinVideoEnabled ? (
										<Video className="w-5 h-5" />
									) : (
										<div className="relative w-5 h-5">
											<VideoOff className="w-5 h-5" />
											<div className="absolute inset-0 flex items-center justify-center">
												<div className="w-6 h-0.5 bg-white rotate-45 rounded-full" />
											</div>
										</div>
									)}
								</Button>
								<Button
									onClick={togglePreJoinAudio}
									variant="solid"
									color={preJoinAudioEnabled ? "blue" : "danger"}
									size="4"
									className="flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all p-0 aspect-square"
									title={preJoinAudioEnabled ? "Microphone On" : "Microphone Off"}
								>
									{preJoinAudioEnabled ? (
										<Volume2 className="w-5 h-5" />
									) : (
										<div className="relative w-5 h-5">
											<VolumeX className="w-5 h-5" />
											<div className="absolute inset-0 flex items-center justify-center">
												<div className="w-6 h-0.5 bg-white rotate-45 rounded-full" />
											</div>
										</div>
									)}
								</Button>
							</div>
							{/* Gradient overlay at bottom */}
							<div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/60 to-transparent pointer-events-none" />
						</div>

						{/* Join Button Section */}
						<div className="flex flex-col items-center gap-5 w-full">
							<Button
								onClick={() => {
									handleJoinCall();
									joinCall();
								}}
								variant="classic"
								color="blue"
								size="2"
								className="px-6 sm:px-8 py-2.5 sm:py-3 shadow-lg hover:shadow-xl transition-all font-semibold relative overflow-hidden bg-gradient-to-br from-blue-9 via-blue-10 to-blue-11 backdrop-blur-sm border border-white/20"
								style={{
									boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(0, 0, 0, 0.2)',
								}}
							>
								<span className="flex items-center gap-2 relative z-10">
									<CheckCircle className="w-4 h-4" />
									<span className="text-sm sm:text-base">Join Webinar</span>
								</span>
								{/* Frost overlay for depth */}
								<div className="absolute inset-0 bg-gradient-to-b from-white/20 via-transparent to-black/10 pointer-events-none rounded-lg" />
							</Button>
							<Text size="2" color="gray" className="text-center max-w-md">
								By joining, you agree to be respectful and follow community guidelines.
							</Text>
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-screen w-full bg-gradient-to-br from-gray-a1 via-gray-a2 to-gray-a3 relative">
			{/* CTA Popup */}
			{webinarId && (
				<CTAPopup 
					webinarId={webinarId} 
					userId={userId} 
					isModerator={isModerator}
					webinarStartTime={webinarStartTime || undefined}
				/>
			)}
			{/* Main Content */}
			<div className="flex-1 flex overflow-hidden px-3 sm:px-4 py-2 sm:py-3">
				<div className="flex-1 flex overflow-hidden gap-3 sm:gap-4 relative">
					{/* Video Container */}
					<div className={`relative flex-1 transition-all duration-300 ease-out ${showChat ? "mr-3 sm:mr-4" : ""}`}>
						{error && (
							<div className="absolute inset-0 flex items-center justify-center rounded-3xl border-2 border-red-a6 bg-red-a2/95 backdrop-blur-sm p-8 z-50 shadow-2xl">
								<div className="text-center max-w-md">
									<Badge variant="soft" color="danger" size="2" highContrast className="mb-4 flex items-center gap-2 w-fit mx-auto shadow-lg">
										<AlertTriangle className="w-5 h-5" />
										Error
									</Badge>
									<p className="text-4 text-red-11 font-medium">
										{error}
									</p>
								</div>
							</div>
						)}
						{accessState === "lobby" && (
							<div className="absolute inset-0 flex items-center justify-center rounded-3xl border-2 border-blue-a6 bg-blue-a2/95 backdrop-blur-sm p-8 z-50 shadow-2xl">
								<div className="text-center max-w-md">
									<Badge variant="soft" color="info" size="2" highContrast className="mb-4 flex items-center gap-2 w-fit mx-auto shadow-lg">
										<Clock className="w-5 h-5 animate-pulse" />
										Waiting for Host
									</Badge>
									<p className="text-4 text-blue-11 font-medium">
										You are in the waiting room. The host will admit you shortly.
									</p>
								</div>
							</div>
						)}
						<div
							ref={videoContainerRef}
							className="h-full w-full overflow-auto rounded-3xl bg-gray-a4 relative grid gap-4 p-6 grid-cols-1 shadow-xl"
						></div>

						{/* Top Status Bar - Grouped at top */}
						{isJoined && (
							<div className="absolute top-4 right-4 z-40 flex items-center gap-2">
								{/* Recording Indicator */}
								{isRecording && (
									<Badge variant="soft" color="danger" size="1" className="flex items-center gap-1.5 px-2 py-1 bg-gray-a3/90 backdrop-blur-md">
										<div className="h-1.5 w-1.5 rounded-full bg-red-9 animate-pulse"></div>
										<span className="text-xs hidden sm:inline">Recording</span>
									</Badge>
								)}
								{/* Participant Count */}
								<Badge variant="surface" size="1" color="gray" className="flex items-center gap-1.5 px-2 py-1 bg-gray-a3/90 backdrop-blur-md">
									<Users className="w-3 h-3" />
									<span className="text-xs">{participantCount}</span>
								</Badge>
							</div>
						)}

						{/* Bottom Controls Overlay */}
						{isJoined && (
							<div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-40 flex items-center gap-2 bg-gray-a2/95 backdrop-blur-xl rounded-full px-3 py-2 shadow-xl border border-white/20">
								{/* Audio */}
								<Button
									onClick={toggleAudio}
									variant="solid"
									color={isAudioOn ? "blue" : "danger"}
									size="2"
									className="flex items-center justify-center w-10 h-10 rounded-full shadow-sm hover:shadow-md transition-all p-0 aspect-square"
									title={isAudioOn ? "Mute" : "Unmute"}
								>
									{isAudioOn ? (
										<Volume2 className="w-4 h-4" />
									) : (
										<div className="relative w-4 h-4">
											<VolumeX className="w-4 h-4" />
											<div className="absolute inset-0 flex items-center justify-center">
												<div className="w-5 h-0.5 bg-white rotate-45 rounded-full" />
											</div>
										</div>
									)}
								</Button>

								{/* Video */}
								<Button
									onClick={toggleVideo}
									variant="solid"
									color={isVideoOn ? "blue" : "danger"}
									size="2"
									className="flex items-center justify-center w-10 h-10 rounded-full shadow-sm hover:shadow-md transition-all p-0 aspect-square"
									title={isVideoOn ? "Video Off" : "Video On"}
								>
									{isVideoOn ? (
										<Video className="w-4 h-4" />
									) : (
										<div className="relative w-4 h-4">
											<VideoOff className="w-4 h-4" />
											<div className="absolute inset-0 flex items-center justify-center">
												<div className="w-5 h-0.5 bg-white rotate-45 rounded-full" />
											</div>
										</div>
									)}
								</Button>

								{/* Screen Share */}
								{isHost && (
									<Button
										onClick={toggleScreenShare}
										variant={isScreenSharing ? "solid" : "soft"}
										color={isScreenSharing ? "blue" : "gray"}
										size="2"
										className="flex items-center justify-center w-10 h-10 rounded-full shadow-sm hover:shadow-md transition-all p-0"
										title={isScreenSharing ? "Stop Sharing" : "Share Screen"}
									>
										<Monitor className="w-4 h-4" />
									</Button>
								)}

								{/* Reactions Button */}
								{webinarId && (
									<ReactionsButton 
										webinarId={webinarId} 
										userId={userId} 
										webinarStartTime={webinarStartTime || undefined} 
									/>
								)}

								{/* Chat Toggle */}
								{webinarId && (
									<Button
										onClick={() => setShowChat(!showChat)}
										variant={showChat ? "solid" : "soft"}
										color={showChat ? "blue" : "gray"}
										size="2"
										className="flex items-center justify-center w-10 h-10 rounded-full shadow-sm hover:shadow-md transition-all p-0 aspect-square"
										title={showChat ? "Hide Chat" : "Show Chat"}
									>
										<MessageSquare className="w-4 h-4" />
									</Button>
								)}

								{/* Settings */}
								<DropdownMenu.Root open={showSettings} onOpenChange={setShowSettings}>
									<DropdownMenu.Trigger>
										<Button variant="soft" color="gray" size="2" className="flex items-center justify-center w-10 h-10 rounded-full shadow-sm hover:shadow-md transition-all p-0 aspect-square" title="Settings">
											<Settings className="w-4 h-4" />
										</Button>
									</DropdownMenu.Trigger>
									<DropdownMenu.Content className="z-[100]">
										<DropdownMenu.Label>Camera</DropdownMenu.Label>
										{availableDevices.cameras.length > 0 ? (
											availableDevices.cameras.map((camera) => (
												<DropdownMenu.Item
													key={camera.deviceId}
													onClick={() => changeCamera(camera.deviceId)}
												>
													{camera.label}
												</DropdownMenu.Item>
											))
										) : (
											<DropdownMenu.Item disabled>No cameras found</DropdownMenu.Item>
										)}
										<DropdownMenu.Separator />
										<DropdownMenu.Label>Microphone</DropdownMenu.Label>
										{availableDevices.microphones.length > 0 ? (
											availableDevices.microphones.map((mic) => (
												<DropdownMenu.Item
													key={mic.deviceId}
													onClick={() => changeMicrophone(mic.deviceId)}
												>
													{mic.label}
												</DropdownMenu.Item>
											))
										) : (
											<DropdownMenu.Item disabled>No microphones found</DropdownMenu.Item>
										)}
										<DropdownMenu.Separator />
										<DropdownMenu.Item onClick={refreshDevices} className="flex items-center gap-2">
											<RefreshCw className="w-4 h-4" />
											Refresh Devices
										</DropdownMenu.Item>
										{isModerator && webinarId && (
											<>
												<DropdownMenu.Separator />
												<DropdownMenu.Label>Moderation</DropdownMenu.Label>
												<DropdownMenu.Item
													onClick={() => {
														setShowSettings(false);
													}}
													className="flex items-center gap-2"
												>
													<Users className="w-4 h-4" />
													Manage Participants
												</DropdownMenu.Item>
											</>
										)}
									</DropdownMenu.Content>
								</DropdownMenu.Root>

								{/* Leave */}
								<Button
									onClick={leaveRoom}
									variant="solid"
									color="danger"
									size="2"
									className="flex items-center gap-1.5 px-4 py-2 shadow-sm hover:shadow-md transition-all font-semibold h-10 text-xs sm:text-sm rounded-full"
								>
									Leave
								</Button>
							</div>
						)}
					</div>

					{/* Chat Sliding Panel */}
					{showChat && webinarId && (
						<div className="h-full w-full sm:w-[380px] bg-gray-a4/95 backdrop-blur-xl rounded-l-xl overflow-hidden flex flex-col transition-all duration-300 ease-out shadow-2xl">
								{/* Tab Selector */}
								<div className="flex bg-gray-a3/40 backdrop-blur-sm overflow-x-auto border-b border-white/5 flex-shrink-0">
									<div className="flex min-w-full">
										<button
											onClick={() => setChatTab("chat")}
											className={`flex-1 px-4 py-3 text-sm font-medium transition-all whitespace-nowrap min-h-[48px] ${
												chatTab === "chat"
													? "text-blue-11 border-b-2 border-blue-10/50 bg-blue-a2/10"
													: "text-gray-11 hover:bg-gray-a4/30"
											}`}
										>
											Chat
										</button>
										<button
											onClick={() => setChatTab("qa")}
											className={`flex-1 px-4 py-3 text-sm font-medium transition-all whitespace-nowrap min-h-[48px] ${
												chatTab === "qa"
													? "text-blue-11 border-b-2 border-blue-10/50 bg-blue-a2/10"
													: "text-gray-11 hover:bg-gray-a4/30"
											}`}
										>
											Q&A
										</button>
										<button
											onClick={() => setChatTab("polls")}
											className={`flex-1 px-4 py-3 text-sm font-medium transition-all whitespace-nowrap min-h-[48px] ${
												chatTab === "polls"
													? "text-blue-11 border-b-2 border-blue-10/50 bg-blue-a2/10"
													: "text-gray-11 hover:bg-gray-a4/30"
											}`}
										>
											Polls
										</button>
										<button
											onClick={() => setChatTab("handouts")}
											className={`flex-1 px-4 py-3 text-sm font-medium transition-all whitespace-nowrap min-h-[48px] ${
												chatTab === "handouts"
													? "text-blue-11 border-b-2 border-blue-10/50 bg-blue-a2/10"
													: "text-gray-11 hover:bg-gray-a4/30"
											}`}
										>
											Files
										</button>
										{isModerator && (
											<button
												onClick={() => setChatTab("attendees")}
												className={`flex-1 px-4 py-3 text-sm font-medium transition-all whitespace-nowrap min-h-[48px] ${
													chatTab === "attendees"
														? "text-blue-11 border-b-2 border-blue-10/50 bg-blue-a2/10"
														: "text-gray-11 hover:bg-gray-a4/30"
												}`}
											>
												Attendees
											</button>
										)}
									</div>
								</div>
								
								{/* Content */}
								<div className="flex-1 overflow-hidden">
									{chatTab === "chat" ? (
										<Chat 
											callFrame={callObject} 
											userName={userName}
											userId={userId}
											webinarId={webinarId}
											userRole={userRole}
											isModerator={isModerator}
											dailySessionId={dailySessionId}
											webinarStartTime={webinarStartTime || undefined}
										/>
									) : chatTab === "qa" ? (
										<QnA 
											webinarId={webinarId}
											userId={userId}
											isModerator={isModerator}
										/>
									) : chatTab === "polls" ? (
										<Polls 
											webinarId={webinarId}
											userId={userId}
											isModerator={isModerator}
											webinarStartTime={webinarStartTime || undefined}
										/>
									) : chatTab === "handouts" ? (
										<Handouts 
											webinarId={webinarId}
											isModerator={isModerator}
										/>
									) : (
										<AttendeeList 
											callFrame={callObject}
											webinarId={webinarId}
											isModerator={isModerator}
										/>
									)}
								</div>
							</div>
						)}
					</div>
				</div>
			</div>
	);
}
