import { Suspense } from "react";
import { headers } from "next/headers";
import { whopsdk } from "@/lib/whop-sdk";
import { ExperienceContent } from "./components/ExperienceContent";

function ExperienceContentFallback() {
	return (
		<div className="flex items-center justify-center min-h-screen">
			<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-11" />
		</div>
	);
}

export default async function ExperiencePage({
	params,
	searchParams,
}: {
	params: Promise<{ experienceId: string }>;
	searchParams: Promise<{ webinarId?: string }>;
}) {
	const { experienceId } = await params;
	const { webinarId } = await searchParams;
	const requestHeaders = await headers();
	// Ensure the user is logged in on whop.
	const { userId } = await whopsdk.verifyUserToken(requestHeaders);

	// Fetch the neccessary data we want from whop.
	const [experience, user, access] = await Promise.all([
		whopsdk.experiences.retrieve(experienceId),
		whopsdk.users.retrieve(userId),
		whopsdk.users.checkAccess(experienceId, { id: userId }),
	]);

	if (!access.has_access) {
		return (
			<div className="flex flex-col items-center justify-center min-h-screen p-8">
				<div className="max-w-md text-center">
					<h1 className="text-8 font-bold text-gray-12 mb-4">Access Restricted</h1>
					<p className="text-4 text-gray-10 mb-4">
						You don't currently have access to this experience. Please check your
						membership status or contact support for assistance.
					</p>
				</div>
			</div>
		);
	}

	const displayName = user.name || `@${user.username}`;

	// Determine if user is host/admin
	// First check basic ownership
	const experienceAny = experience as any;
	let isHost =
		experienceAny.created_by === userId ||
		(experienceAny.company_id && experienceAny.company_id === user.id);

	// If not owner, check if user has admin role or can manage the experience
	if (!isHost) {
		// Check access object for admin indicators
		const accessObj = access as any;
		isHost =
			accessObj.is_admin === true ||
			accessObj.role === "admin" ||
			accessObj.can_admin === true ||
			accessObj.permissions?.includes("admin") ||
			accessObj.permissions?.includes("manage");
	}

	// Also check if user has created webinars for this experience (indicates admin)
	if (!isHost) {
		try {
			const { supabaseServer } = await import("@/lib/supabase");
			const { data: existingWebinars } = await supabaseServer
				.from("webinars")
				.select("created_by")
				.eq("experience_id", experienceId)
				.eq("created_by", userId)
				.limit(1);
			
			if (existingWebinars && existingWebinars.length > 0) {
				isHost = true;
			}
		} catch (error) {
			// Silently fail if we can't check
			console.error("Error checking webinar ownership:", error);
		}
	}

	// If user has access to the experience, allow them to create webinars
	// This enables first-time users to create their first webinar
	const canCreateWebinars = isHost || access.has_access;

	return (
		<Suspense fallback={<ExperienceContentFallback />}>
			<ExperienceContent
				experienceId={experienceId}
				userId={userId}
				userName={displayName}
				isHost={isHost}
				canCreateWebinars={canCreateWebinars}
				companyId={experienceAny.company_id || ""}
				initialWebinarId={webinarId || null}
			/>
		</Suspense>
	);
}
