@layer theme, base, frosted_ui, components, utilities;

@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/preflight.css" layer(base);
@import "tailwindcss/utilities.css" layer(utilities);
@import "@whop/react/styles.css" layer(frosted_ui);

/* biome-ignore lint/suspicious/noUnknownAtRules: this rule is imported from whop */
@config '../tailwind.config.cjs';

body {
	background: var(--background);
	color: var(--foreground);
	font-family: Arial, Helvetica, sans-serif;
}

/* Hide Daily.co's built-in control bar */
iframe[src*="daily.co"] {
	pointer-events: auto;
}

iframe[src*="daily.co"] [data-testid="control-bar"],
iframe[src*="daily.co"] [data-testid="control-bar-bottom"],
iframe[src*="daily.co"] .daily-control-bar,
iframe[src*="daily.co"] .daily-controls-bar,
iframe[src*="daily.co"] .daily-video-control-bar,
iframe[src*="daily.co"] .daily-iframe-control-bar,
iframe[src*="daily.co"] .daily-bar,
iframe[src*="daily.co"] .daily-tray,
iframe[src*="daily.co"] .daily-tray-wrapper,
iframe[src*="daily.co"] button[aria-label*="Leave"],
iframe[src*="daily.co"] button[aria-label*="Fullscreen"],
iframe[src*="daily.co"] button[aria-label*="Mute"],
iframe[src*="daily.co"] button[aria-label*="Video"] {
	display: none !important;
	visibility: hidden !important;
	opacity: 0 !important;
	height: 0 !important;
	width: 0 !important;
	overflow: hidden !important;
	pointer-events: none !important;
}

/* @rc-component/color-picker Styles */
.rc-color-picker {
	font-family: inherit;
}

.rc-color-picker-panel {
	border-radius: 0.5rem;
	border: none !important;
	box-shadow: none !important;
	padding: 0 !important;
}

.rc-color-picker-trigger {
	border-radius: 0.5rem;
	border: 1px solid var(--gray-a4, #f5f5f5);
}

.rc-color-picker-panel-inner {
	border-radius: 0.5rem;
	border: none !important;
	padding: 0 !important;
}

.rc-color-picker-panel-inner::after {
	display: none !important;
}

.rc-color-picker-panel-inner::before {
	display: none !important;
}

/* Remove ALL black strokes/borders from color picker - make completely borderless */
.rc-color-picker * {
	border: none !important;
	outline: none !important;
	box-shadow: none !important;
}

.rc-color-picker-saturation,
.rc-color-picker-saturation > *,
.rc-color-picker-saturation-inner,
.rc-color-picker-saturation-inner > *,
.rc-color-picker-hue,
.rc-color-picker-hue > *,
.rc-color-picker-hue-inner,
.rc-color-picker-hue-inner > *,
.rc-color-picker-alpha,
.rc-color-picker-alpha > *,
.rc-color-picker-alpha-inner,
.rc-color-picker-alpha-inner > * {
	border: none !important;
	outline: none !important;
	box-shadow: none !important;
}

.rc-color-picker-saturation {
	border-radius: 0.5rem 0.5rem 0 0 !important;
}

.rc-color-picker-hue {
	border-radius: 0 !important;
	margin-top: 0 !important;
}

.rc-color-picker-alpha {
	border-radius: 0 0 0.5rem 0.5rem !important;
}

/* Remove pointer borders */
.rc-color-picker-handler,
.rc-color-picker-handler-dragging {
	border: 2px solid rgba(255, 255, 255, 0.95) !important;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
}

/* Ensure no borders anywhere in color picker */
.rc-color-picker-panel,
.rc-color-picker-panel-inner,
.rc-color-picker-panel-inner * {
	border: none !important;
}
