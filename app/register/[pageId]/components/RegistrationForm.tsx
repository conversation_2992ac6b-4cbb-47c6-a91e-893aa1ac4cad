"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, Card, Heading, Text, TextField, TextArea, Select, Checkbox, RadioGroup } from "@whop/react/components";
import { useCreateRegistration, useRegistrationPageBySlug, useRegistrationPage } from "@/lib/queries/registrations";
import { useWebinar } from "@/lib/queries/webinars";
import type { CustomField } from "@/lib/supabase";

interface RegistrationFormProps {
	pageId: string;
	slug?: string;
}

export function RegistrationForm({ pageId, slug }: RegistrationFormProps) {
	const router = useRouter();
	const [formData, setFormData] = useState<{
		name: string;
		email: string;
		phone: string;
		[key: string]: string;
	}>({
		name: "",
		email: "",
		phone: "",
	});
	const [errors, setErrors] = useState<Record<string, string>>({});
	const [isSubmitting, setIsSubmitting] = useState(false);

	const { data: registrationPage, isLoading: pageLoading } = slug
		? useRegistrationPageBySlug(slug)
		: useRegistrationPage(pageId);
	const registrationPageData = registrationPage;

	const webinarId = registrationPageData?.webinar_id;
	const { data: webinar } = useWebinar(webinarId || null);

	const createRegistration = useCreateRegistration();

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		if (errors[field]) {
			setErrors((prev) => {
				const newErrors = { ...prev };
				delete newErrors[field];
				return newErrors;
			});
		}
	};

	const validateField = (field: CustomField, value: string): string | null => {
		if (field.required && !value.trim()) {
			return `${field.label} is required`;
		}

		if (field.validation) {
			if (field.validation.pattern) {
				const regex = new RegExp(field.validation.pattern);
				if (!regex.test(value)) {
					return `Invalid ${field.label.toLowerCase()}`;
				}
			}
			if (field.validation.min && value.length < field.validation.min) {
				return `${field.label} must be at least ${field.validation.min} characters`;
			}
			if (field.validation.max && value.length > field.validation.max) {
				return `${field.label} must be at most ${field.validation.max} characters`;
			}
		}

		return null;
	};

	const validateForm = (): boolean => {
		const newErrors: Record<string, string> = {};

		// Validate standard fields
		if (!formData.name.trim()) {
			newErrors.name = "Name is required";
		}
		if (!formData.email.trim()) {
			newErrors.email = "Email is required";
		} else {
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(formData.email)) {
				newErrors.email = "Invalid email address";
			}
		}

		// Validate custom fields
		if (registrationPageData?.custom_fields) {
			registrationPageData.custom_fields.forEach((field) => {
				const value = formData[field.id] || "";
				const error = validateField(field, value);
				if (error) {
					newErrors[field.id] = error;
				}
			});
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateForm() || !registrationPageData || !webinarId) {
			return;
		}

		setIsSubmitting(true);

		try {
			// Prepare custom field data
			const customFieldData: Record<string, unknown> = {};
			if (registrationPageData.custom_fields) {
				registrationPageData.custom_fields.forEach((field) => {
					if (formData[field.id]) {
						customFieldData[field.id] = formData[field.id];
					}
				});
			}

			await createRegistration.mutateAsync({
				registration_page_id: registrationPageData.id,
				webinar_id: webinarId,
				email: formData.email,
				name: formData.name,
				phone: formData.phone || undefined,
				custom_field_data: customFieldData,
				source: "public",
			});

			// Redirect to thank you page
			const thankYouSlug = slug || registrationPageData.slug;
			router.push(`/register/${thankYouSlug}/thank-you`);
		} catch (error) {
			console.error("Registration error:", error);
			setErrors({
				submit: error instanceof Error ? error.message : "Failed to submit registration",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const renderField = (field: CustomField) => {
		const value = formData[field.id] || "";
		const error = errors[field.id];

		switch (field.type) {
			case "textarea":
				return (
					<div key={field.id} className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							{field.label}
							{field.required && <span className="text-red-500">*</span>}
						</Text>
						<TextArea
							id={field.id}
							value={value}
							onChange={(e) => handleInputChange(field.id, e.target.value)}
							placeholder={field.placeholder}
							required={field.required}
							className={error ? "border-red-500" : ""}
						/>
						{error && <Text size="1" color="red">{error}</Text>}
					</div>
				);

			case "select":
				return (
					<div key={field.id} className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							{field.label}
							{field.required && <span className="text-red-500">*</span>}
						</Text>
						<Select.Root
							value={value}
							onValueChange={(newValue) => handleInputChange(field.id, newValue)}
							required={field.required}
						>
							<Select.Trigger className={error ? "border-red-500" : ""}>
								<Select.Value placeholder={field.placeholder || "Select..."} />
							</Select.Trigger>
							<Select.Content>
								{field.options?.map((option) => (
									<Select.Item key={option} value={option}>
										{option}
									</Select.Item>
								))}
							</Select.Content>
						</Select.Root>
						{error && <Text size="1" color="red">{error}</Text>}
					</div>
				);

			case "checkbox":
				return (
					<div key={field.id} className="flex flex-col gap-2">
						<Checkbox
							id={field.id}
							checked={value === "true"}
							onCheckedChange={(checked) =>
								handleInputChange(field.id, checked ? "true" : "")
							}
							required={field.required}
							className={error ? "border-red-500" : ""}
						>
							{field.label}
							{field.required && <span className="text-red-500">*</span>}
						</Checkbox>
						{error && <Text size="1" color="red">{error}</Text>}
					</div>
				);

			case "radio":
				return (
					<div key={field.id} className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							{field.label}
							{field.required && <span className="text-red-500">*</span>}
						</Text>
						<RadioGroup.Root
							value={value}
							onValueChange={(newValue) => handleInputChange(field.id, newValue)}
							required={field.required}
						>
							{field.options?.map((option) => (
								<RadioGroup.Item key={option} value={option} id={`${field.id}-${option}`}>
									{option}
								</RadioGroup.Item>
							))}
						</RadioGroup.Root>
						{error && <Text size="1" color="red">{error}</Text>}
					</div>
				);

			default:
				return (
					<div key={field.id} className="flex flex-col gap-2">
						<Text size="2" weight="semi-bold">
							{field.label}
							{field.required && <span className="text-red-500">*</span>}
						</Text>
						<TextField.Root>
							<TextField.Input
								id={field.id}
								type={field.type === "number" ? "number" : field.type === "date" ? "date" : field.type === "url" ? "url" : "text"}
								value={value}
								onChange={(e) => handleInputChange(field.id, e.target.value)}
								placeholder={field.placeholder}
								required={field.required}
								className={error ? "border-red-500" : ""}
							/>
						</TextField.Root>
						{error && <Text size="1" color="red">{error}</Text>}
					</div>
				);
		}
	};

	if (pageLoading) {
		return (
			<div className="flex items-center justify-center min-h-screen p-4">
				<Text>Loading registration form...</Text>
			</div>
		);
	}

	if (!registrationPageData) {
		return (
			<div className="flex items-center justify-center min-h-screen p-4">
				<Text>Registration page not found</Text>
			</div>
		);
	}

	if (!registrationPageData.is_active) {
		return (
			<div className="flex items-center justify-center min-h-screen p-4">
				<Text>This registration page is no longer active</Text>
			</div>
		);
	}

	// Sort custom fields by order
	const sortedFields = [...(registrationPageData.custom_fields || [])].sort(
		(a, b) => a.order - b.order,
	);

	return (
		<div className="min-h-screen p-4 sm:p-8 flex items-center justify-center">
			<Card className="w-full max-w-lg">
				<div className="p-6 sm:p-8">
					<Heading size="6" className="mb-2">
						{registrationPageData.title}
					</Heading>
					{registrationPageData.description && (
						<Text className="text-gray-10 mb-6">{registrationPageData.description}</Text>
					)}

					<form onSubmit={handleSubmit} className="flex flex-col gap-4">
						{/* Standard Fields */}
						<div className="flex flex-col gap-2">
							<Text size="2" weight="semi-bold">
								Full Name <span className="text-red-500">*</span>
							</Text>
							<TextField.Root>
								<TextField.Input
									id="name"
									type="text"
									value={formData.name}
									onChange={(e) => handleInputChange("name", e.target.value)}
									placeholder="Enter your full name"
									required
									className={errors.name ? "border-red-500" : ""}
								/>
							</TextField.Root>
							{errors.name && (
								<Text size="1" color="red">{errors.name}</Text>
							)}
						</div>

						<div className="flex flex-col gap-2">
							<Text size="2" weight="semi-bold">
								Email Address <span className="text-red-500">*</span>
							</Text>
							<TextField.Root>
								<TextField.Input
									id="email"
									type="email"
									value={formData.email}
									onChange={(e) => handleInputChange("email", e.target.value)}
									placeholder="<EMAIL>"
									required
									className={errors.email ? "border-red-500" : ""}
								/>
							</TextField.Root>
							{errors.email && (
								<Text size="1" color="red">{errors.email}</Text>
							)}
						</div>

						<div className="flex flex-col gap-2">
							<Text size="2" weight="semi-bold">
								Phone Number
							</Text>
							<TextField.Root>
								<TextField.Input
									id="phone"
									type="tel"
									value={formData.phone}
									onChange={(e) => handleInputChange("phone", e.target.value)}
									placeholder="(*************"
									className={errors.phone ? "border-red-500" : ""}
								/>
							</TextField.Root>
							{errors.phone && (
								<Text size="1" color="red">{errors.phone}</Text>
							)}
						</div>

						{/* Custom Fields */}
						{sortedFields.map((field) => renderField(field))}

						{errors.submit && (
							<Text size="1" color="red">{errors.submit}</Text>
						)}

						<Button
							type="submit"
							disabled={isSubmitting}
							className="w-full mt-4"
						>
							{isSubmitting ? "Submitting..." : "Register"}
						</Button>
					</form>
				</div>
			</Card>
		</div>
	);
}

