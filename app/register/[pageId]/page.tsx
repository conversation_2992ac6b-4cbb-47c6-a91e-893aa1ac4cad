import { RegistrationForm } from "./components/RegistrationForm";

export default async function RegisterPage({
	params,
	searchParams,
}: {
	params: Promise<{ pageId: string }>;
	searchParams: Promise<{ slug?: string }>;
}) {
	const resolvedParams = await params;
	const resolvedSearchParams = await searchParams;

	return (
		<RegistrationForm
			pageId={resolvedParams.pageId}
			slug={resolvedSearchParams.slug}
		/>
	);
}

