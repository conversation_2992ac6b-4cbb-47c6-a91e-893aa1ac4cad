"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, Heading, Text, Button } from "@whop/react/components";
import { CalendarDownloadButton } from "@/app/components/CalendarDownloadButton";
import { useRegistrationPageBySlug, useRegistrationPage } from "@/lib/queries/registrations";
import { useWebinar } from "@/lib/queries/webinars";

interface RegistrationThankYouPageProps {
	pageId: string;
	slug?: string;
}

export function RegistrationThankYouPage({
	pageId,
	slug,
}: RegistrationThankYouPageProps) {
	const router = useRouter();
	const { data: registrationPage } = slug
		? useRegistrationPageBySlug(slug)
		: useRegistrationPage(pageId);

	const webinarId = registrationPage?.webinar_id;
	const { data: webinar } = useWebinar(webinarId || null);

	const config = registrationPage?.thank_you_page_config || {};
	const {
		title = "Thank You!",
		message = "Your registration is confirmed. We'll send you a reminder before the webinar starts.",
		showCalendarDownload = true,
		redirectUrl,
		redirectDelay,
		backgroundColor,
		textColor,
		customContent,
	} = config;

	useEffect(() => {
		if (redirectUrl && redirectDelay) {
			const timer = setTimeout(() => {
				router.push(redirectUrl);
			}, redirectDelay * 1000);

			return () => clearTimeout(timer);
		}
	}, [redirectUrl, redirectDelay, router]);

	if (!registrationPage || !webinar) {
		return (
			<div className="flex items-center justify-center min-h-screen p-4">
				<Text>Loading...</Text>
			</div>
		);
	}

	const registrationData = {
		name: "User", // In a real app, you'd get this from the registration submission
		email: "<EMAIL>",
	};

	return (
		<div
			className="min-h-screen p-4 sm:p-8 flex items-center justify-center"
			style={{
				backgroundColor: backgroundColor || undefined,
				color: textColor || undefined,
			}}
		>
			<Card className="w-full max-w-lg">
				<div className="p-6 sm:p-8 text-center">
					<Heading size="6" className="mb-4">
						{title}
					</Heading>
					<Text className="text-gray-10 mb-6">{message}</Text>

					{customContent && (
						<div
							className="mb-6 prose prose-sm max-w-none"
							dangerouslySetInnerHTML={{ __html: customContent }}
						/>
					)}

					{showCalendarDownload && webinar && (
						<div className="mt-8">
							<Text className="text-sm text-gray-10 mb-4">
								Add this event to your calendar:
							</Text>
							<CalendarDownloadButton
								webinar={webinar}
								registrationData={registrationData}
							/>
						</div>
					)}

					{redirectUrl && redirectDelay && (
						<Text className="text-sm text-gray-10 mt-6">
							Redirecting in {redirectDelay} seconds...
						</Text>
					)}

					<Button
						onClick={() => router.push("/")}
						className="mt-6 w-full"
						variant="surface"
					>
						Return Home
					</Button>
				</div>
			</Card>
		</div>
	);
}

