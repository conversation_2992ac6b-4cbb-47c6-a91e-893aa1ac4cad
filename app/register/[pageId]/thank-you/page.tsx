import { RegistrationThankYouPage } from "./components/RegistrationThankYouPage";

export default async function ThankYouPage({
	params,
	searchParams,
}: {
	params: Promise<{ pageId: string }>;
	searchParams: Promise<{ slug?: string }>;
}) {
	const resolvedParams = await params;
	const resolvedSearchParams = await searchParams;

	return (
		<RegistrationThankYouPage
			pageId={resolvedParams.pageId}
			slug={resolvedSearchParams.slug}
		/>
	);
}

