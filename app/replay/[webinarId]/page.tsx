import { headers } from "next/headers";
import { whopsdk } from "@/lib/whop-sdk";
import { supabaseServer } from "@/lib/supabase";
import { ReplayPlayer } from "@/app/experiences/[experienceId]/components/ReplayPlayer";
import { notFound } from "next/navigation";
import { Card, Heading, Text } from "@whop/react/components";

export default async function ReplayPage({
	params,
	searchParams,
}: {
	params: Promise<{ webinarId: string }>;
	searchParams: Promise<{ replayId?: string }>;
}) {
	const { webinarId } = await params;
	const { replayId } = await searchParams;
	const { userId } = await whopsdk.verifyUserToken(await headers());

	// Get webinar
	const { data: webinar } = await supabaseServer
		.from("webinars")
		.select("id, title, experience_id")
		.eq("id", webinarId)
		.single();

	if (!webinar) {
		notFound();
	}

	// Check access to experience
	const access = await whopsdk.users.checkAccess(webinar.experience_id, { id: userId });
	if (!access.has_access) {
		return (
			<div className="flex items-center justify-center min-h-screen p-8">
				<Card variant="surface" size="4" className="p-8 text-center">
					<Heading size="5" weight="bold" className="mb-4">
						Access Denied
					</Heading>
					<Text size="3" color="gray">
						You do not have access to this replay.
					</Text>
				</Card>
			</div>
		);
	}

	// Get replays
	const { data: replays } = await supabaseServer
		.from("webinar_replays")
		.select(`
			*,
			webinar_recordings (*)
		`)
		.eq("webinar_id", webinarId)
		.eq("is_active", true);

	if (!replays || replays.length === 0) {
		return (
			<div className="flex items-center justify-center min-h-screen p-8">
				<div className="text-center">
					<h1 className="text-2xl font-bold mb-4">No Replay Available</h1>
					<p className="text-gray-10">No replay has been created for this webinar yet.</p>
				</div>
			</div>
		);
	}

	// Filter expired replays
	const now = new Date();
	const validReplays = replays.filter((replay) => {
		if (replay.expires_at && new Date(replay.expires_at) < now) {
			return false;
		}
		return true;
	});

	if (validReplays.length === 0) {
		return (
			<div className="flex items-center justify-center min-h-screen p-8">
				<div className="text-center">
					<h1 className="text-2xl font-bold mb-4">Replay Expired</h1>
					<p className="text-gray-10">This replay has expired and is no longer available.</p>
				</div>
			</div>
		);
	}

	const selectedReplay = replayId
		? validReplays.find((r) => r.id === replayId)
		: validReplays[0];

	if (!selectedReplay) {
		return (
			<div className="flex items-center justify-center min-h-screen p-8">
				<div className="text-center">
					<h1 className="text-2xl font-bold mb-4">Replay Not Found</h1>
					<p className="text-gray-10">The requested replay could not be found.</p>
				</div>
			</div>
		);
	}

	return (
		<ReplayPlayer webinarId={webinarId} replayId={selectedReplay.id} />
	);
}

