import { supabaseServer } from "@/lib/supabase";
import type {
	WebinarAnalyticsEvent,
	WebinarDropoff,
	WebinarConversion,
	WebinarRevenue,
	WebinarDeviceAnalytics,
	CRMTag,
	WebinarAttendance,
} from "@/lib/supabase";

export interface TrackEventParams {
	webinarId: string;
	userId?: string;
	submissionId?: string;
	eventType: WebinarAnalyticsEvent["event_type"];
	eventData?: Record<string, unknown>;
	deviceInfo?: Record<string, unknown>;
	browserInfo?: Record<string, unknown>;
}

export interface TrackDropoffParams {
	attendanceId: string;
	webinarId: string;
	userId?: string;
	submissionId?: string;
	leftAtMinute: number;
	webinarDurationMinutes?: number;
	reason?: string;
}

export interface TrackConversionParams {
	webinarId: string;
	userId: string;
	submissionId?: string;
	conversionType: WebinarConversion["conversion_type"];
	productId?: string;
	amount: number;
	currency?: string;
	whopPaymentId?: string;
	metadata?: Record<string, unknown>;
}

export interface TrackDeviceInfoParams {
	attendanceId: string;
	webinarId: string;
	userId?: string;
	submissionId?: string;
	deviceType: WebinarDeviceAnalytics["device_type"];
	platform?: WebinarDeviceAnalytics["platform"];
	browser?: WebinarDeviceAnalytics["browser"];
	userAgent?: string;
	screenWidth?: number;
	screenHeight?: number;
}

export interface RegistrationStats {
	total_registrants: number;
	conversion_rate: number;
	registrations_over_time: Array<{ date: string; count: number }>;
}

export interface AttendanceStats {
	total_joined: number;
	percent_stayed: number;
	attendance_rate: number;
	join_times: Array<{ time: string; count: number }>;
}

export interface WatchTimeStats {
	average_watch_time: number;
	individual_watch_times: Array<{ user_id: string; email: string; watch_time_minutes: number }>;
	engagement_score: number;
}

export interface DropoffAnalysis {
	dropoff_points: Array<{ minute: number; count: number }>;
	retention_curve: Array<{ minute: number; retention_percent: number }>;
	average_duration_before_leave: number;
}

export interface DeviceAnalytics {
	device_breakdown: Array<{ device_type: string; count: number; percent: number }>;
	browser_breakdown: Array<{ browser: string; count: number; percent: number }>;
	platform_breakdown: Array<{ platform: string; count: number; percent: number }>;
}

export interface OfferAnalytics {
	cta_impressions: number;
	cta_clicks: number;
	click_through_rate: number;
	conversion_rate: number;
	offer_performance: Array<{ cta_id: string; title: string; impressions: number; clicks: number; ctr: number }>;
}

export interface ConversionAnalytics {
	total_conversions: number;
	conversion_rate: number;
	revenue: number;
	purchase_details: Array<{
		user_id: string;
		email: string;
		conversion_type: string;
		amount: number;
		created_at: string;
	}>;
}

export interface RevenueAnalytics {
	total_revenue: number;
	platform_cut: number;
	creator_revenue: number;
	transaction_count: number;
	currency: string;
}

export interface LiveVsReplayComparison {
	live_stats: {
		total_views: number;
		average_watch_time: number;
		completion_rate: number;
		engagement_score: number;
	};
	replay_stats: {
		total_views: number;
		average_watch_time: number;
		completion_rate: number;
		engagement_score: number;
	};
	comparison_metrics: {
		view_difference: number;
		watch_time_difference: number;
		completion_rate_difference: number;
	};
}

/**
 * Track analytics event
 */
export async function trackEvent(
	params: TrackEventParams,
): Promise<WebinarAnalyticsEvent> {
	const { data, error } = await supabaseServer
		.from("webinar_analytics_events")
		.insert([
			{
				webinar_id: params.webinarId,
				user_id: params.userId || null,
				submission_id: params.submissionId || null,
				event_type: params.eventType,
				event_data: params.eventData || {},
				device_info: params.deviceInfo || {},
				browser_info: params.browserInfo || {},
				timestamp: new Date().toISOString(),
			},
		])
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to track event: ${error.message}`);
	}

	return data;
}

/**
 * Track user dropoff
 */
export async function trackDropoff(
	params: TrackDropoffParams,
): Promise<WebinarDropoff> {
	const { data, error } = await supabaseServer
		.from("webinar_dropoffs")
		.insert([
			{
				attendance_id: params.attendanceId,
				webinar_id: params.webinarId,
				user_id: params.userId || null,
				submission_id: params.submissionId || null,
				left_at_minute: params.leftAtMinute,
				webinar_duration_minutes: params.webinarDurationMinutes || null,
				reason: params.reason || null,
			},
		])
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to track dropoff: ${error.message}`);
	}

	return data;
}

/**
 * Track conversion/purchase
 */
export async function trackConversion(
	params: TrackConversionParams,
): Promise<WebinarConversion> {
	const { data, error } = await supabaseServer
		.from("webinar_conversions")
		.insert([
			{
				webinar_id: params.webinarId,
				user_id: params.userId,
				submission_id: params.submissionId || null,
				conversion_type: params.conversionType,
				product_id: params.productId || null,
				amount: params.amount,
				currency: params.currency || "USD",
				whop_payment_id: params.whopPaymentId || null,
				metadata: params.metadata || {},
			},
		])
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to track conversion: ${error.message}`);
	}

	return data;
}

/**
 * Track device/platform information
 */
export async function trackDeviceInfo(
	params: TrackDeviceInfoParams,
): Promise<WebinarDeviceAnalytics> {
	const { data, error } = await supabaseServer
		.from("webinar_device_analytics")
		.insert([
			{
				attendance_id: params.attendanceId,
				webinar_id: params.webinarId,
				user_id: params.userId || null,
				submission_id: params.submissionId || null,
				device_type: params.deviceType,
				platform: params.platform || null,
				browser: params.browser || null,
				user_agent: params.userAgent || null,
				screen_width: params.screenWidth || null,
				screen_height: params.screenHeight || null,
			},
		])
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to track device info: ${error.message}`);
	}

	return data;
}

/**
 * Calculate engagement score based on various factors
 */
export function calculateEngagementScore(
	watchTimeMinutes: number,
	totalDurationMinutes: number,
	pollResponses: number = 0,
	questionsAsked: number = 0,
	reactions: number = 0,
): number {
	const watchTimeScore = Math.min((watchTimeMinutes / totalDurationMinutes) * 50, 50);
	const interactionScore = Math.min((pollResponses + questionsAsked + reactions) * 10, 50);
	return Math.round(watchTimeScore + interactionScore);
}

/**
 * Get registration statistics
 */
export async function getRegistrationStats(
	webinarId: string,
): Promise<RegistrationStats> {
	// Get total registrations
	const { data: registrations, error: regError } = await supabaseServer
		.from("registration_submissions")
		.select("id, created_at")
		.eq("webinar_id", webinarId);

	if (regError) {
		throw new Error(`Failed to fetch registrations: ${regError.message}`);
	}

	const totalRegistrants = registrations?.length || 0;

	// Get registrations over time
	const registrationsOverTime = registrations?.reduce(
		(acc, reg) => {
			const date = new Date(reg.created_at).toISOString().split("T")[0];
			const existing = acc.find((item) => item.date === date);
			if (existing) {
				existing.count += 1;
			} else {
				acc.push({ date, count: 1 });
			}
			return acc;
		},
		[] as Array<{ date: string; count: number }>,
	) || [];

	// Get attendance count for conversion rate
	const { data: attendance, error: attError } = await supabaseServer
		.from("webinar_attendance")
		.select("id")
		.eq("webinar_id", webinarId);

	if (attError) {
		throw new Error(`Failed to fetch attendance: ${attError.message}`);
	}

	const attendanceCount = attendance?.length || 0;
	const conversionRate =
		totalRegistrants > 0 ? (attendanceCount / totalRegistrants) * 100 : 0;

	return {
		total_registrants: totalRegistrants,
		conversion_rate: Math.round(conversionRate * 100) / 100,
		registrations_over_time: registrationsOverTime.sort((a, b) =>
			a.date.localeCompare(b.date),
		),
	};
}

/**
 * Get attendance statistics
 */
export async function getAttendanceStats(
	webinarId: string,
): Promise<AttendanceStats> {
	const { data: attendance, error } = await supabaseServer
		.from("webinar_attendance")
		.select("id, joined_at, left_at, duration_minutes, status")
		.eq("webinar_id", webinarId);

	if (error) {
		throw new Error(`Failed to fetch attendance: ${error.message}`);
	}

	const totalJoined = attendance?.length || 0;
	const stayedCount = attendance?.filter((a) => a.status === "attended").length || 0;
	const percentStayed = totalJoined > 0 ? (stayedCount / totalJoined) * 100 : 0;

	// Get registrations for attendance rate
	const { data: registrations } = await supabaseServer
		.from("registration_submissions")
		.select("id")
		.eq("webinar_id", webinarId);

	const totalRegistrants = registrations?.length || 0;
	const attendanceRate =
		totalRegistrants > 0 ? (totalJoined / totalRegistrants) * 100 : 0;

	// Get join times
	const joinTimes = attendance?.reduce(
		(acc, att) => {
			const hour = new Date(att.joined_at).getHours();
			const timeKey = `${hour}:00`;
			const existing = acc.find((item) => item.time === timeKey);
			if (existing) {
				existing.count += 1;
			} else {
				acc.push({ time: timeKey, count: 1 });
			}
			return acc;
		},
		[] as Array<{ time: string; count: number }>,
	) || [];

	return {
		total_joined: totalJoined,
		percent_stayed: Math.round(percentStayed * 100) / 100,
		attendance_rate: Math.round(attendanceRate * 100) / 100,
		join_times: joinTimes.sort((a, b) => a.time.localeCompare(b.time)),
	};
}

/**
 * Get watch time statistics
 */
export async function getWatchTimeStats(
	webinarId: string,
): Promise<WatchTimeStats> {
	const { data: attendance, error } = await supabaseServer
		.from("webinar_attendance")
		.select("id, user_id, email, duration_minutes")
		.eq("webinar_id", webinarId)
		.not("duration_minutes", "is", null);

	if (error) {
		throw new Error(`Failed to fetch attendance: ${error.message}`);
	}

	const watchTimes = attendance?.map((a) => ({
		user_id: a.user_id || "",
		email: a.email,
		watch_time_minutes: a.duration_minutes || 0,
	})) || [];

	const totalWatchTime = watchTimes.reduce(
		(sum, wt) => sum + wt.watch_time_minutes,
		0,
	);
	const averageWatchTime =
		watchTimes.length > 0 ? totalWatchTime / watchTimes.length : 0;

	// Get webinar duration for engagement score
	const { data: webinar } = await supabaseServer
		.from("webinars")
		.select("duration_minutes")
		.eq("id", webinarId)
		.single();

	const webinarDuration = webinar?.duration_minutes || 60;
	const engagementScore = calculateEngagementScore(
		averageWatchTime,
		webinarDuration,
	);

	return {
		average_watch_time: Math.round(averageWatchTime * 100) / 100,
		individual_watch_times: watchTimes,
		engagement_score: engagementScore,
	};
}

/**
 * Get dropoff analysis
 */
export async function getDropoffAnalysis(
	webinarId: string,
): Promise<DropoffAnalysis> {
	const { data: dropoffs, error } = await supabaseServer
		.from("webinar_dropoffs")
		.select("left_at_minute, webinar_duration_minutes")
		.eq("webinar_id", webinarId);

	if (error) {
		throw new Error(`Failed to fetch dropoffs: ${error.message}`);
	}

	const dropoffPoints = dropoffs?.reduce(
		(acc, d) => {
			const existing = acc.find((item) => item.minute === d.left_at_minute);
			if (existing) {
				existing.count += 1;
			} else {
				acc.push({ minute: d.left_at_minute, count: 1 });
			}
			return acc;
		},
		[] as Array<{ minute: number; count: number }>,
	) || [];

	const totalDropoffs = dropoffs?.length || 0;
	const averageDuration =
		totalDropoffs > 0
			? dropoffs.reduce((sum, d) => sum + d.left_at_minute, 0) / totalDropoffs
			: 0;

	// Build retention curve
	const webinarDuration =
		dropoffs?.[0]?.webinar_duration_minutes ||
		(await supabaseServer
			.from("webinars")
			.select("duration_minutes")
			.eq("id", webinarId)
			.single()).data?.duration_minutes ||
		60;

	const retentionCurve: Array<{ minute: number; retention_percent: number }> = [];
	for (let minute = 0; minute <= webinarDuration; minute += 5) {
		const leftBefore = dropoffs?.filter((d) => d.left_at_minute <= minute).length || 0;
		const retention = totalDropoffs > 0
			? ((totalDropoffs - leftBefore) / totalDropoffs) * 100
			: 100;
		retentionCurve.push({
			minute,
			retention_percent: Math.round(retention * 100) / 100,
		});
	}

	return {
		dropoff_points: dropoffPoints.sort((a, b) => a.minute - b.minute),
		retention_curve: retentionCurve,
		average_duration_before_leave: Math.round(averageDuration * 100) / 100,
	};
}

/**
 * Get device/platform analytics
 */
export async function getDeviceAnalytics(
	webinarId: string,
): Promise<DeviceAnalytics> {
	const { data: devices, error } = await supabaseServer
		.from("webinar_device_analytics")
		.select("device_type, platform, browser")
		.eq("webinar_id", webinarId);

	if (error) {
		throw new Error(`Failed to fetch device analytics: ${error.message}`);
	}

	const total = devices?.length || 0;

	const deviceBreakdown = devices?.reduce(
		(acc, d) => {
			const existing = acc.find((item) => item.device_type === d.device_type);
			if (existing) {
				existing.count += 1;
			} else {
				acc.push({ device_type: d.device_type, count: 1, percent: 0 });
			}
			return acc;
		},
		[] as Array<{ device_type: string; count: number; percent: number }>,
	) || [];

	deviceBreakdown.forEach((item) => {
		item.percent = total > 0 ? Math.round((item.count / total) * 100 * 100) / 100 : 0;
	});

	const browserBreakdown = devices?.reduce(
		(acc, d) => {
			const browser = d.browser || "unknown";
			const existing = acc.find((item) => item.browser === browser);
			if (existing) {
				existing.count += 1;
			} else {
				acc.push({ browser, count: 1, percent: 0 });
			}
			return acc;
		},
		[] as Array<{ browser: string; count: number; percent: number }>,
	) || [];

	browserBreakdown.forEach((item) => {
		item.percent = total > 0 ? Math.round((item.count / total) * 100 * 100) / 100 : 0;
	});

	const platformBreakdown = devices?.reduce(
		(acc, d) => {
			const platform = d.platform || "unknown";
			const existing = acc.find((item) => item.platform === platform);
			if (existing) {
				existing.count += 1;
			} else {
				acc.push({ platform, count: 1, percent: 0 });
			}
			return acc;
		},
		[] as Array<{ platform: string; count: number; percent: number }>,
	) || [];

	platformBreakdown.forEach((item) => {
		item.percent = total > 0 ? Math.round((item.count / total) * 100 * 100) / 100 : 0;
	});

	return {
		device_breakdown: deviceBreakdown,
		browser_breakdown: browserBreakdown,
		platform_breakdown: platformBreakdown,
	};
}

/**
 * Get offer/CTA analytics
 */
export async function getOfferAnalytics(
	webinarId: string,
): Promise<OfferAnalytics> {
	// Get CTAs first
	const { data: ctas, error: ctaError } = await supabaseServer
		.from("webinar_ctas")
		.select("id, title, times_shown, clicks")
		.eq("webinar_id", webinarId);

	if (ctaError) {
		throw new Error(`Failed to fetch CTAs: ${ctaError.message}`);
	}

	const ctaIds = ctas?.map((cta) => cta.id) || [];

	// Get CTA interactions
	const { data: interactions, error: intError } = await supabaseServer
		.from("webinar_cta_interactions")
		.select("cta_id, interaction_type")
		.in("cta_id", ctaIds);

	if (intError) {
		throw new Error(`Failed to fetch CTA interactions: ${intError.message}`);
	}

	const ctaImpressions = ctas?.reduce((sum, cta) => sum + (cta.times_shown || 0), 0) || 0;
	const ctaClicks = ctas?.reduce((sum, cta) => sum + (cta.clicks || 0), 0) || 0;
	const clickThroughRate = ctaImpressions > 0 ? (ctaClicks / ctaImpressions) * 100 : 0;

	// Get conversions from CTAs
	const { data: conversions } = await supabaseServer
		.from("webinar_conversions")
		.select("id")
		.eq("webinar_id", webinarId)
		.eq("conversion_type", "purchase");

	const conversionCount = conversions?.length || 0;
	const conversionRate = ctaClicks > 0 ? (conversionCount / ctaClicks) * 100 : 0;

	const offerPerformance = ctas?.map((cta) => ({
		cta_id: cta.id,
		title: cta.title,
		impressions: cta.times_shown || 0,
		clicks: cta.clicks || 0,
		ctr: (cta.times_shown || 0) > 0
			? Math.round(((cta.clicks || 0) / (cta.times_shown || 0)) * 100 * 100) / 100
			: 0,
	})) || [];

	return {
		cta_impressions: ctaImpressions,
		cta_clicks: ctaClicks,
		click_through_rate: Math.round(clickThroughRate * 100) / 100,
		conversion_rate: Math.round(conversionRate * 100) / 100,
		offer_performance: offerPerformance,
	};
}

/**
 * Get conversion analytics
 */
export async function getConversionAnalytics(
	webinarId: string,
): Promise<ConversionAnalytics> {
	const { data: conversions, error } = await supabaseServer
		.from("webinar_conversions")
		.select("id, user_id, conversion_type, amount, created_at")
		.eq("webinar_id", webinarId);

	if (error) {
		throw new Error(`Failed to fetch conversions: ${error.message}`);
	}

	const totalConversions = conversions?.length || 0;
	const totalRevenue = conversions?.reduce((sum, c) => sum + (c.amount || 0), 0) || 0;

	// Get registrations for conversion rate
	const { data: registrations } = await supabaseServer
		.from("registration_submissions")
		.select("id")
		.eq("webinar_id", webinarId);

	const totalRegistrants = registrations?.length || 0;
	const conversionRate =
		totalRegistrants > 0 ? (totalConversions / totalRegistrants) * 100 : 0;

	// Get user emails for purchase details
	const purchaseDetails = await Promise.all(
		(conversions || []).map(async (conv) => {
			const { data: submission } = await supabaseServer
				.from("registration_submissions")
				.select("email")
				.eq("webinar_id", webinarId)
				.eq("user_id", conv.user_id)
				.single();

			return {
				user_id: conv.user_id,
				email: submission?.email || "",
				conversion_type: conv.conversion_type,
				amount: conv.amount,
				created_at: conv.created_at,
			};
		}),
	);

	return {
		total_conversions: totalConversions,
		conversion_rate: Math.round(conversionRate * 100) / 100,
		revenue: Math.round(totalRevenue * 100) / 100,
		purchase_details: purchaseDetails,
	};
}

/**
 * Get revenue analytics
 */
export async function getRevenueAnalytics(
	webinarId: string,
): Promise<RevenueAnalytics> {
	const { data: revenue, error } = await supabaseServer
		.from("webinar_revenue")
		.select("*")
		.eq("webinar_id", webinarId)
		.single();

	if (error && error.code !== "PGRST116") {
		throw new Error(`Failed to fetch revenue: ${error.message}`);
	}

	if (!revenue) {
		return {
			total_revenue: 0,
			platform_cut: 0,
			creator_revenue: 0,
			transaction_count: 0,
			currency: "USD",
		};
	}

	return {
		total_revenue: revenue.total_revenue,
		platform_cut: revenue.platform_cut_amount,
		creator_revenue: revenue.creator_revenue,
		transaction_count: revenue.total_purchases,
		currency: revenue.currency,
	};
}

/**
 * Compare live vs replay performance
 */
export async function compareLiveVsReplay(
	webinarId: string,
): Promise<LiveVsReplayComparison> {
	// Get live attendance
	const { data: liveAttendance, error: liveError } = await supabaseServer
		.from("webinar_attendance")
		.select("id, duration_minutes")
		.eq("webinar_id", webinarId);

	if (liveError) {
		throw new Error(`Failed to fetch live attendance: ${liveError.message}`);
	}

	const liveViews = liveAttendance?.length || 0;
	const liveTotalWatchTime = liveAttendance?.reduce(
		(sum, att) => sum + (att.duration_minutes || 0),
		0,
	) || 0;
	const liveAverageWatchTime =
		liveViews > 0 ? liveTotalWatchTime / liveViews : 0;

	// Get webinar duration
	const { data: webinar } = await supabaseServer
		.from("webinars")
		.select("duration_minutes")
		.eq("id", webinarId)
		.single();

	const webinarDuration = webinar?.duration_minutes || 60;
	const liveCompletionRate =
		webinarDuration > 0 ? (liveAverageWatchTime / webinarDuration) * 100 : 0;
	const liveEngagementScore = calculateEngagementScore(
		liveAverageWatchTime,
		webinarDuration,
	);

	// Get replay views (from analytics events)
	const { data: replayEvents, error: replayError } = await supabaseServer
		.from("webinar_analytics_events")
		.select("id, event_data")
		.eq("webinar_id", webinarId)
		.eq("event_type", "replay_view");

	if (replayError) {
		throw new Error(`Failed to fetch replay events: ${replayError.message}`);
	}

	const replayViews = replayEvents?.length || 0;
	const replayWatchTimes = replayEvents?.map((e) => {
		const watchTime = (e.event_data as { watch_time_minutes?: number })
			?.watch_time_minutes || 0;
		return watchTime;
	}) || [];
	const replayTotalWatchTime = replayWatchTimes.reduce((sum, wt) => sum + wt, 0);
	const replayAverageWatchTime =
		replayViews > 0 ? replayTotalWatchTime / replayViews : 0;
	const replayCompletionRate =
		webinarDuration > 0 ? (replayAverageWatchTime / webinarDuration) * 100 : 0;
	const replayEngagementScore = calculateEngagementScore(
		replayAverageWatchTime,
		webinarDuration,
	);

	return {
		live_stats: {
			total_views: liveViews,
			average_watch_time: Math.round(liveAverageWatchTime * 100) / 100,
			completion_rate: Math.round(liveCompletionRate * 100) / 100,
			engagement_score: liveEngagementScore,
		},
		replay_stats: {
			total_views: replayViews,
			average_watch_time: Math.round(replayAverageWatchTime * 100) / 100,
			completion_rate: Math.round(replayCompletionRate * 100) / 100,
			engagement_score: replayEngagementScore,
		},
		comparison_metrics: {
			view_difference: replayViews - liveViews,
			watch_time_difference: Math.round(
				(replayAverageWatchTime - liveAverageWatchTime) * 100,
			) / 100,
			completion_rate_difference: Math.round(
				(replayCompletionRate - liveCompletionRate) * 100,
			) / 100,
		},
	};
}

/**
 * Generate CRM tags based on user behavior
 */
export async function generateCRMTags(
	webinarId: string,
): Promise<CRMTag[]> {
	// Get all registrations
	const { data: registrations, error: regError } = await supabaseServer
		.from("registration_submissions")
		.select("id, user_id, email")
		.eq("webinar_id", webinarId);

	if (regError) {
		throw new Error(`Failed to fetch registrations: ${regError.message}`);
	}

	// Get attendance
	const { data: attendance, error: attError } = await supabaseServer
		.from("webinar_attendance")
		.select("id, user_id, submission_id, status")
		.eq("webinar_id", webinarId);

	if (attError) {
		throw new Error(`Failed to fetch attendance: ${attError.message}`);
	}

	// Get conversions
	const { data: conversions, error: convError } = await supabaseServer
		.from("webinar_conversions")
		.select("user_id")
		.eq("webinar_id", webinarId)
		.eq("conversion_type", "purchase");

	if (convError) {
		throw new Error(`Failed to fetch conversions: ${convError.message}`);
	}

	const conversionUserIds = new Set(conversions?.map((c) => c.user_id) || []);
	const attendanceMap = new Map(
		attendance?.map((a) => [a.user_id || a.submission_id, a]) || [],
	);

	const crmTags: CRMTag[] = [];

	for (const reg of registrations || []) {
		const userId = reg.user_id || reg.id;
		const tags: string[] = [];
		const tagData: Record<string, unknown> = {};

		const att = attendanceMap.get(userId);
		const hasConverted = conversionUserIds.has(userId);

		if (!att) {
			tags.push("registered_no_attend");
		} else {
			if (att.status === "attended") {
				tags.push("attended");
				if (!hasConverted) {
					tags.push("attended_no_purchase");
				}
			} else if (att.status === "partial") {
				tags.push("partial_attendance");
			}
		}

		if (hasConverted) {
			tags.push("purchased");
		}

		// Check engagement
		if (att && att.status === "attended") {
			const { data: events } = await supabaseServer
				.from("webinar_analytics_events")
				.select("event_type")
				.eq("webinar_id", webinarId)
				.eq("user_id", userId);

			const eventCount = events?.length || 0;
			if (eventCount > 5) {
				tags.push("high_engagement");
			}
		}

		if (tags.length > 0) {
			crmTags.push({
				id: "", // Will be set by database
				webinar_id: webinarId,
				user_id: userId,
				submission_id: reg.id,
				tags,
				tag_data: tagData,
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
			});
		}
	}

	// Upsert tags
	const tagsToUpsert = crmTags.map((tag) => ({
		webinar_id: tag.webinar_id,
		user_id: tag.user_id,
		submission_id: tag.submission_id,
		tags: tag.tags,
		tag_data: tag.tag_data,
		updated_at: new Date().toISOString(),
	}));

	if (tagsToUpsert.length > 0) {
		// Use upsert to handle conflicts
		for (const tag of tagsToUpsert) {
			await supabaseServer
				.from("crm_tags")
				.upsert(tag, {
					onConflict: "webinar_id,user_id",
				});
		}
	}

	// Fetch updated tags
	const { data: updatedTags, error: fetchError } = await supabaseServer
		.from("crm_tags")
		.select("*")
		.eq("webinar_id", webinarId);

	if (fetchError) {
		throw new Error(`Failed to fetch CRM tags: ${fetchError.message}`);
	}

	return updatedTags || [];
}

