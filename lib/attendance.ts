import { supabaseServer } from "@/lib/supabase";
import type { WebinarAttendance } from "@/lib/supabase";

export interface TrackJoinParams {
	webinarId: string;
	submissionId: string;
	userId?: string;
	email: string;
	dailySessionId?: string;
}

export interface TrackLeaveParams {
	attendanceId: string;
}

/**
 * Track user join event
 */
export async function trackJoin(
	params: TrackJoinParams,
): Promise<WebinarAttendance> {
	const { data, error } = await supabaseServer
		.from("webinar_attendance")
		.insert([
			{
				webinar_id: params.webinarId,
				submission_id: params.submissionId,
				user_id: params.userId || null,
				email: params.email,
				daily_session_id: params.dailySessionId || null,
				status: "attended",
			},
		])
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to track join: ${error.message}`);
	}

	return data;
}

/**
 * Track user leave event
 */
export async function trackLeave(
	params: TrackLeaveParams,
): Promise<WebinarAttendance> {
	const { data: existing, error: fetchError } = await supabaseServer
		.from("webinar_attendance")
		.select("*")
		.eq("id", params.attendanceId)
		.single();

	if (fetchError || !existing) {
		throw new Error("Attendance record not found");
	}

	const leftAt = new Date().toISOString();
	const joinedAt = new Date(existing.joined_at);
	const durationMinutes = Math.floor(
		(new Date(leftAt).getTime() - joinedAt.getTime()) / (1000 * 60),
	);

	// Determine status based on duration (5 minutes threshold)
	let status: "attended" | "missed" | "partial" = "attended";
	if (durationMinutes < 5) {
		status = durationMinutes > 0 ? "partial" : "missed";
	}

	const { data, error } = await supabaseServer
		.from("webinar_attendance")
		.update({
			left_at: leftAt,
			duration_minutes: durationMinutes,
			status,
		})
		.eq("id", params.attendanceId)
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to track leave: ${error.message}`);
	}

	return data;
}

/**
 * Calculate attendance status based on duration
 */
export function calculateAttendanceStatus(
	durationMinutes: number | null,
	thresholdMinutes: number = 5,
): "attended" | "missed" | "partial" {
	if (durationMinutes === null || durationMinutes === 0) {
		return "missed";
	}
	if (durationMinutes >= thresholdMinutes) {
		return "attended";
	}
	return "partial";
}

/**
 * Get attendance records for a webinar
 */
export async function getAttendanceForWebinar(
	webinarId: string,
): Promise<WebinarAttendance[]> {
	const { data, error } = await supabaseServer
		.from("webinar_attendance")
		.select("*")
		.eq("webinar_id", webinarId)
		.order("joined_at", { ascending: false });

	if (error) {
		throw new Error(`Failed to fetch attendance: ${error.message}`);
	}

	return data || [];
}

/**
 * Get attendance for a specific registration
 */
export async function getAttendanceForSubmission(
	submissionId: string,
): Promise<WebinarAttendance | null> {
	const { data, error } = await supabaseServer
		.from("webinar_attendance")
		.select("*")
		.eq("submission_id", submissionId)
		.order("joined_at", { ascending: false })
		.limit(1)
		.single();

	if (error) {
		if (error.code === "PGRST116") {
			// No rows returned
			return null;
		}
		throw new Error(`Failed to fetch attendance: ${error.message}`);
	}

	return data;
}

