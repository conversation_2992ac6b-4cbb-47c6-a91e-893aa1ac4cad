import type { Webinar } from "@/lib/supabase";

export interface RegistrationData {
	name: string;
	email: string;
	phone?: string;
}

/**
 * Generate .ics file content for calendar integration
 * Supports Google Calendar, Outlook, and iCloud
 */
export function generateICSFile(
	webinar: Webinar,
	registrationData: RegistrationData,
): string {
	const startDate = new Date(webinar.scheduled_at);
	const endDate = new Date(
		startDate.getTime() + (webinar.duration_minutes || 60) * 60 * 1000,
	);

	// Format dates in ICS format (YYYYMMDDTHHmmssZ)
	const formatICSDate = (date: Date): string => {
		const year = date.getUTCFullYear();
		const month = String(date.getUTCMonth() + 1).padStart(2, "0");
		const day = String(date.getUTCDate()).padStart(2, "0");
		const hours = String(date.getUTCHours()).padStart(2, "0");
		const minutes = String(date.getUTCMinutes()).padStart(2, "0");
		const seconds = String(date.getUTCSeconds()).padStart(2, "0");
		return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
	};

	const startDateStr = formatICSDate(startDate);
	const endDateStr = formatICSDate(endDate);
	const now = formatICSDate(new Date());

	// Escape special characters in ICS content
	const escapeICS = (text: string): string => {
		return text
			.replace(/\\/g, "\\\\")
			.replace(/;/g, "\\;")
			.replace(/,/g, "\\,")
			.replace(/\n/g, "\\n");
	};

	const description = webinar.description
		? escapeICS(webinar.description)
		: "";
	const summary = escapeICS(webinar.title);
	const location = escapeICS(
		`Webinar Registration - ${registrationData.email}`,
	);

	// Build ICS content
	const icsContent = [
		"BEGIN:VCALENDAR",
		"VERSION:2.0",
		"PRODID:-//Webinar Registration//EN",
		"CALSCALE:GREGORIAN",
		"METHOD:REQUEST",
		"BEGIN:VEVENT",
		`UID:${webinar.id}-${registrationData.email}-${now}@webinar`,
		`DTSTAMP:${now}`,
		`DTSTART:${startDateStr}`,
		`DTEND:${endDateStr}`,
		`SUMMARY:${summary}`,
		`DESCRIPTION:${description}`,
		`LOCATION:${location}`,
		"STATUS:CONFIRMED",
		"SEQUENCE:0",
		"BEGIN:VALARM",
		"TRIGGER:-PT15M",
		"ACTION:DISPLAY",
		`DESCRIPTION:Reminder: ${summary}`,
		"END:VALARM",
		"END:VEVENT",
		"END:VCALENDAR",
	].join("\r\n");

	return icsContent;
}

/**
 * Trigger download of .ics file
 */
export function downloadICSFile(content: string, filename: string): void {
	const blob = new Blob([content], { type: "text/calendar;charset=utf-8" });
	const url = URL.createObjectURL(blob);
	const link = document.createElement("a");
	link.href = url;
	link.download = filename;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	URL.revokeObjectURL(url);
}

/**
 * Generate calendar download URLs for different providers
 */
export function getCalendarUrls(
	webinar: Webinar,
	registrationData: RegistrationData,
): {
	google: string;
	outlook: string;
	ical: string;
} {
	const startDate = new Date(webinar.scheduled_at);
	const endDate = new Date(
		startDate.getTime() + (webinar.duration_minutes || 60) * 60 * 1000,
	);

	const formatGoogleDate = (date: Date): string => {
		return date.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";
	};

	const formatOutlookDate = (date: Date): string => {
		return date.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";
	};

	const title = encodeURIComponent(webinar.title);
	const details = encodeURIComponent(
		webinar.description || `Webinar: ${webinar.title}`,
	);
	const location = encodeURIComponent(
		`Webinar Registration - ${registrationData.email}`,
	);
	const startStr = formatGoogleDate(startDate);
	const endStr = formatGoogleDate(endDate);

	// Google Calendar URL
	const googleUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startStr}/${endStr}&details=${details}&location=${location}`;

	// Outlook URL
	const outlookUrl = `https://outlook.live.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startDate.toISOString()}&enddt=${endDate.toISOString()}&body=${details}&location=${location}`;

	// iCal file download (will use downloadICSFile function)
	const icalUrl = "#"; // Placeholder, will be handled by downloadICSFile

	return {
		google: googleUrl,
		outlook: outlookUrl,
		ical: icalUrl,
	};
}

