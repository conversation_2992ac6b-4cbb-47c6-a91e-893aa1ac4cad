import type { RegistrationSubmission } from "@/lib/supabase";

export interface CSVExportOptions {
	includeStandardFields?: boolean;
	customFields?: string[];
	includeTags?: boolean;
}

/**
 * Generate CSV content from registration submissions
 */
export function generateCSV(
	registrations: RegistrationSubmission[],
	options: CSVExportOptions = {},
): string {
	const {
		includeStandardFields = true,
		customFields = [],
		includeTags = false,
	} = options;

	if (registrations.length === 0) {
		return "";
	}

	// Collect all unique custom field keys across all submissions
	const allCustomFieldKeys = new Set<string>();
	registrations.forEach((reg) => {
		if (reg.custom_field_data) {
			Object.keys(reg.custom_field_data).forEach((key) => {
				allCustomFieldKeys.add(key);
			});
		}
	});

	// Build header row
	const headers: string[] = [];
	if (includeStandardFields) {
		headers.push("Name", "Email", "Phone", "Source", "Registration Date");
	}

	// Add custom fields
	const fieldsToInclude = customFields.length > 0
		? customFields.filter((f) => allCustomFieldKeys.has(f))
		: Array.from(allCustomFieldKeys);

	fieldsToInclude.forEach((field) => {
		headers.push(`Custom: ${field}`);
	});

	if (includeTags) {
		headers.push("Tags");
	}

	// Build CSV rows
	const rows: string[][] = [headers];

	registrations.forEach((reg) => {
		const row: string[] = [];

		if (includeStandardFields) {
			row.push(
				escapeCSV(reg.name),
				escapeCSV(reg.email),
				escapeCSV(reg.phone || ""),
				escapeCSV(reg.source),
				escapeCSV(new Date(reg.created_at).toISOString()),
			);
		}

		// Add custom field values
		fieldsToInclude.forEach((field) => {
			const value = reg.custom_field_data?.[field];
			if (value !== undefined && value !== null) {
				row.push(escapeCSV(String(value)));
			} else {
				row.push("");
			}
		});

		if (includeTags) {
			// Tags would need to be joined from a separate query
			row.push(""); // Placeholder
		}

		rows.push(row);
	});

	// Convert to CSV string
	return rows.map((row) => row.join(",")).join("\n");
}

/**
 * Escape CSV values (handle commas, quotes, newlines)
 */
function escapeCSV(value: string): string {
	if (value.includes(",") || value.includes('"') || value.includes("\n")) {
		return `"${value.replace(/"/g, '""')}"`;
	}
	return value;
}

/**
 * Trigger download of CSV file
 */
export function downloadCSV(content: string, filename: string): void {
	const blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
	const url = URL.createObjectURL(blob);
	const link = document.createElement("a");
	link.href = url;
	link.download = filename;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	URL.revokeObjectURL(url);
}

/**
 * Generate filename with timestamp
 */
export function generateCSVFilename(webinarTitle: string): string {
	const sanitized = webinarTitle
		.replace(/[^a-z0-9]/gi, "_")
		.toLowerCase()
		.substring(0, 50);
	const timestamp = new Date().toISOString().split("T")[0];
	return `registrations_${sanitized}_${timestamp}.csv`;
}

