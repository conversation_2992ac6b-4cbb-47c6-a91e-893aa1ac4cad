const DAILY_API_BASE_URL = "https://api.daily.co/v1";

class DailyApiError extends Error {
	status: number;
	data: unknown;

	constructor(message: string, status: number, data: unknown) {
		super(message);
		this.name = "DailyApiError";
		this.status = status;
		this.data = data;
	}
}

interface DailyRoomProperties {
	max_participants?: number;
	enable_chat?: boolean;
	enable_screenshare?: boolean;
	enable_recording?: string;
	exp?: number;
	nbf?: number;
}

interface DailyRoom {
	name: string;
	url: string;
	config?: Record<string, unknown>;
	privacy?: string;
}

interface MeetingTokenProperties {
	room_name?: string;
	is_owner?: boolean;
	user_id?: string;
	user_name?: string;
	domain?: string;
	exp?: number;
	nbf?: number;
}

interface MeetingTokenPayload {
	properties?: MeetingTokenProperties;
}

const DEFAULT_ROOM_PROPERTIES: DailyRoomProperties = {
	enable_chat: true,
	enable_screenshare: true,
	enable_recording: "cloud",
	max_participants: 100,
};

type FetchOptions = Omit<RequestInit, "headers"> & {
	apiKey?: string;
	ignoreCodes?: number[];
};

const jsonHeaders = {
	"Content-Type": "application/json",
} as const;

function resolveApiKey(provided?: string) {
	const apiKey = provided ?? process.env.DAILY_API_KEY;
	if (!apiKey) {
		throw new Error("DAILY_API_KEY environment variable is not set");
	}
	return apiKey;
}

async function dailyFetch(path: string, { apiKey, ignoreCodes = [], ...init }: FetchOptions = {}) {
	const resolvedKey = resolveApiKey(apiKey);
	const response = await fetch(`${DAILY_API_BASE_URL}${path}`, {
		...init,
		headers: {
			Authorization: `Bearer ${resolvedKey}`,
			...jsonHeaders,
			...init.headers,
		},
	});

	if (!response.ok && !ignoreCodes.includes(response.status)) {
		let errorPayload: unknown;
		try {
			errorPayload = await response.clone().json();
		} catch {
			errorPayload = await response.text();
		}

		throw new DailyApiError(
			`Daily API request to ${path} failed with status ${response.status}`,
			response.status,
			errorPayload,
		);
	}

	return response;
}

export async function getOrCreateRoom(
	roomName: string,
	options: { apiKey?: string; properties?: DailyRoomProperties; privacy?: "public" | "private" } = {},
) {
	const properties = { ...DEFAULT_ROOM_PROPERTIES, ...options.properties };

	const roomResponse = await dailyFetch(`/rooms/${encodeURIComponent(roomName)}`, {
		method: "GET",
		apiKey: options.apiKey,
		ignoreCodes: [404],
	});

	if (roomResponse.status === 200) {
		return {
			room: (await roomResponse.json()) as DailyRoom,
			created: false,
		};
	}

	const createResponse = await dailyFetch("/rooms", {
		method: "POST",
		apiKey: options.apiKey,
		body: JSON.stringify({
			name: roomName,
			privacy: options.privacy ?? "public",
			properties,
		}),
	});

	return {
		room: (await createResponse.json()) as DailyRoom,
		created: true,
	};
}

export interface MeetingTokenOptions {
	roomName: string;
	userName?: string;
	userId?: string;
	isHost?: boolean;
	apiKey?: string;
	exp?: number;
	nbf?: number;
	domain?: string;
}

interface MeetingTokenResponse {
	token: string;
}

export async function createMeetingToken({
	roomName,
	userName,
	userId,
	isHost,
	apiKey,
	exp,
	nbf,
	domain,
}: MeetingTokenOptions) {
	const payload: MeetingTokenPayload = {
		properties: {
			room_name: roomName,
			is_owner: Boolean(isHost),
			user_name: userName,
			user_id: userId,
			...(exp ? { exp } : {}),
			...(nbf ? { nbf } : {}),
			...(domain ? { domain } : {}),
		},
	};

	const response = await dailyFetch("/meeting-tokens", {
		method: "POST",
		apiKey,
		body: JSON.stringify(payload),
	});

	return (await response.json()) as MeetingTokenResponse;
}

export async function createMeetingTokenWithDomainFallback(options: MeetingTokenOptions) {
	try {
		return await createMeetingToken(options);
	} catch (error) {
		const shouldRetryWithoutDomain =
			error instanceof DailyApiError &&
			options.domain &&
			[400, 401, 403, 422].includes(error.status);

		if (shouldRetryWithoutDomain) {
			const dataString = JSON.stringify(error.data ?? "").toLowerCase();
			if (dataString.includes("domain")) {
				console.warn(
					"Daily meeting-token request rejected for domain override; retrying without domain.",
					{
						domain: options.domain,
						status: error.status,
						data: error.data,
					},
				);
				return await createMeetingToken({ ...options, domain: undefined });
			}
		}

		throw error;
	}
}

export { DailyApiError };

export type { DailyRoom };

