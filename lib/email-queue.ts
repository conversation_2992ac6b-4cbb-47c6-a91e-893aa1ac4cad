import { supabaseServer } from "@/lib/supabase";
import type { EmailQueue, EmailTemplate, Webinar, RegistrationSubmission } from "@/lib/supabase";
import { renderHTMLTemplate, renderTextTemplate, buildTemplateVariables } from "@/lib/template-renderer";
import type { TemplateVariables } from "@/lib/template-variables";

/**
 * Queue an email for sending
 */
export async function queueEmail(
	params: {
		webinarId: string;
		submissionId: string;
		recipientEmail: string;
		recipientUserId?: string;
		templateId?: string;
		subject: string;
		bodyHtml: string;
		bodyText?: string;
		reminderType: "confirmation" | "reminder" | "attended" | "missed";
		scheduledFor: Date;
	},
): Promise<EmailQueue> {
	const { data, error } = await supabaseServer
		.from("email_queue")
		.insert([
			{
				webinar_id: params.webinarId,
				submission_id: params.submissionId,
				template_id: params.templateId || null,
				recipient_email: params.recipientEmail,
				recipient_user_id: params.recipientUserId || null,
				subject: params.subject,
				body_html: params.bodyHtml,
				body_text: params.bodyText || null,
				reminder_type: params.reminderType,
				scheduled_for: params.scheduledFor.toISOString(),
				status: "pending",
			},
		])
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to queue email: ${error.message}`);
	}

	return data;
}

/**
 * Queue confirmation email after registration
 */
export async function queueConfirmationEmail(
	submission: RegistrationSubmission,
	webinar: Webinar,
	template?: EmailTemplate,
): Promise<void> {
	// Build template variables
	const variables = buildTemplateVariables(
		{
			name: submission.name,
			email: submission.email,
			custom_field_data: submission.custom_field_data,
			created_at: submission.created_at,
		},
		{
			title: webinar.title,
			description: webinar.description,
			scheduled_at: webinar.scheduled_at,
			duration_minutes: webinar.duration_minutes,
			timezone: webinar.timezone,
		},
	);

	// Use template if provided, otherwise use default
	let subject: string;
	let bodyHtml: string;
	let bodyText: string | undefined;

	if (template) {
		subject = renderHTMLTemplate(template.subject, variables);
		bodyHtml = renderHTMLTemplate(template.body_html, variables);
		bodyText = template.body_text
			? renderTextTemplate(template.body_text, variables)
			: undefined;
	} else {
		// Default confirmation email
		subject = `Registration Confirmed: ${webinar.title}`;
		bodyHtml = `
			<h1>Thank you for registering, ${submission.name}!</h1>
			<p>Your registration for <strong>${webinar.title}</strong> has been confirmed.</p>
			<p><strong>Date:</strong> ${variables.webinar_date}</p>
			<p><strong>Time:</strong> ${variables.webinar_time}</p>
			${webinar.description ? `<p>${webinar.description}</p>` : ""}
			<p>We'll send you a reminder before the webinar starts.</p>
		`;
		bodyText = `
			Thank you for registering, ${submission.name}!
			
			Your registration for ${webinar.title} has been confirmed.
			
			Date: ${variables.webinar_date}
			Time: ${variables.webinar_time}
			
			${webinar.description ? webinar.description + "\n\n" : ""}
			We'll send you a reminder before the webinar starts.
		`;
	}

	// Queue email to be sent immediately
	await queueEmail({
		webinarId: webinar.id,
		submissionId: submission.id,
		recipientEmail: submission.email,
		recipientUserId: submission.user_id || undefined,
		templateId: template?.id,
		subject,
		bodyHtml,
		bodyText,
		reminderType: "confirmation",
		scheduledFor: new Date(), // Send immediately
	});
}

/**
 * Queue reminder emails based on schedules
 */
export async function queueReminderEmails(
	webinarId: string,
): Promise<void> {
	// Get active reminder schedules for this webinar
	const { data: schedules } = await supabaseServer
		.from("reminder_schedules")
		.select("*")
		.eq("webinar_id", webinarId)
		.eq("is_active", true);

	if (!schedules || schedules.length === 0) {
		return;
	}

	// Get webinar
	const { data: webinar } = await supabaseServer
		.from("webinars")
		.select("*")
		.eq("id", webinarId)
		.single();

	if (!webinar) {
		return;
	}

	// Get all registrations for this webinar
	const { data: submissions } = await supabaseServer
		.from("registration_submissions")
		.select("*")
		.eq("webinar_id", webinarId);

	if (!submissions || submissions.length === 0) {
		return;
	}

	// Get templates if needed
	const templateIds = schedules
		.map((s) => s.template_id)
		.filter((id): id is string => id !== null);
	const { data: templates } =
		templateIds.length > 0
			? await supabaseServer
					.from("email_templates")
					.select("*")
					.in("id", templateIds)
			: { data: null };

	const templatesMap = new Map(
		(templates || []).map((t) => [t.id, t]),
	);

	const scheduledDate = new Date(webinar.scheduled_at);

	// Queue emails for each schedule and submission
	for (const schedule of schedules) {
		const reminderTime = new Date(
			scheduledDate.getTime() - schedule.timing_hours_before * 60 * 60 * 1000,
		);

		// Only queue if reminder time is in the future
		if (reminderTime <= new Date()) {
			continue;
		}

		const template = schedule.template_id
			? templatesMap.get(schedule.template_id)
			: undefined;

		for (const submission of submissions) {
			const variables = buildTemplateVariables(
				{
					name: submission.name,
					email: submission.email,
					custom_field_data: submission.custom_field_data,
					created_at: submission.created_at,
				},
				{
					title: webinar.title,
					description: webinar.description,
					scheduled_at: webinar.scheduled_at,
					duration_minutes: webinar.duration_minutes,
					timezone: webinar.timezone,
				},
			);

			let subject: string;
			let bodyHtml: string;
			let bodyText: string | undefined;

			if (template) {
				subject = renderHTMLTemplate(template.subject, variables);
				bodyHtml = renderHTMLTemplate(template.body_html, variables);
				bodyText = template.body_text
					? renderTextTemplate(template.body_text, variables)
					: undefined;
			} else {
				// Default reminder email
				subject = `Reminder: ${webinar.title} starting soon`;
				bodyHtml = `
					<h1>Reminder: ${webinar.title}</h1>
					<p>Hi ${submission.name},</p>
					<p>This is a reminder that <strong>${webinar.title}</strong> is starting in ${schedule.timing_hours_before} hour(s).</p>
					<p><strong>Date:</strong> ${variables.webinar_date}</p>
					<p><strong>Time:</strong> ${variables.webinar_time}</p>
					<p>We look forward to seeing you there!</p>
				`;
				bodyText = `
					Reminder: ${webinar.title}
					
					Hi ${submission.name},
					
					This is a reminder that ${webinar.title} is starting in ${schedule.timing_hours_before} hour(s).
					
					Date: ${variables.webinar_date}
					Time: ${variables.webinar_time}
					
					We look forward to seeing you there!
				`;
			}

			// Queue email if reminder type includes email
			if (
				schedule.reminder_type === "email" ||
				schedule.reminder_type === "both"
			) {
				await queueEmail({
					webinarId: webinar.id,
					submissionId: submission.id,
					recipientEmail: submission.email,
					recipientUserId: submission.user_id || undefined,
					templateId: template?.id,
					subject,
					bodyHtml,
					bodyText,
					reminderType: "reminder",
					scheduledFor: reminderTime,
				});
			}

			// Queue DM if reminder type includes DM
			if (
				schedule.reminder_type === "dm" ||
				schedule.reminder_type === "both"
			) {
				if (submission.user_id) {
					await queueEmail({
						webinarId: webinar.id,
						submissionId: submission.id,
						recipientEmail: submission.email,
						recipientUserId: submission.user_id,
						templateId: template?.id,
						subject,
						bodyHtml,
						bodyText,
						reminderType: "reminder",
						scheduledFor: reminderTime,
					});
				}
			}
		}
	}
}

