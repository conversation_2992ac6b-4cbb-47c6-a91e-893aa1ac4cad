import { Resend } from "resend";
import type { EmailQueue } from "@/lib/supabase";

const resendApiKey = process.env.RESEND_API_KEY;
const resendFromEmail = process.env.RESEND_FROM_EMAIL || "<EMAIL>";

if (!resendApiKey) {
	console.warn("RESEND_API_KEY is not set. Email sending will be disabled.");
}

const resend = resendApiKey ? new Resend(resendApiKey) : null;

export interface SendEmailOptions {
	to: string;
	subject: string;
	html: string;
	text?: string;
	from?: string;
	replyTo?: string;
}

/**
 * Send email via Resend API
 */
export async function sendEmailViaResend(
	options: SendEmailOptions,
): Promise<{ id: string; error: Error | null }> {
	if (!resend) {
		return {
			id: "",
			error: new Error("Resend API key not configured"),
		};
	}

	try {
		const result = await resend.emails.send({
			from: options.from || resendFromEmail,
			to: options.to,
			subject: options.subject,
			html: options.html,
			text: options.text,
			replyTo: options.replyTo,
		});

		if (result.error) {
			return {
				id: "",
				error: new Error(result.error.message || "Failed to send email"),
			};
		}

		return {
			id: result.data?.id || "",
			error: null,
		};
	} catch (error) {
		return {
			id: "",
			error: error instanceof Error ? error : new Error("Unknown error"),
		};
	}
}

/**
 * Send email from queue item
 */
export async function sendEmailFromQueue(
	queueItem: EmailQueue,
): Promise<{ success: boolean; messageId?: string; error?: string }> {
	const result = await sendEmailViaResend({
		to: queueItem.recipient_email,
		subject: queueItem.subject,
		html: queueItem.body_html,
		text: queueItem.body_text || undefined,
	});

	if (result.error) {
		return {
			success: false,
			error: result.error.message,
		};
	}

	return {
		success: true,
		messageId: result.id,
	};
}

