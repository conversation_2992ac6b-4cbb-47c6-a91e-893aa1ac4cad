"use client";

import { useQuery } from "@tanstack/react-query";

async function fetchAnalytics(
	webinarId: string,
	metric: string,
): Promise<unknown> {
	const response = await fetch(
		`/api/analytics/${webinarId}?metric=${metric}`,
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch analytics",
		}));
		throw new Error(error.error || "Failed to fetch analytics");
	}
	return response.json();
}

export function useRegistrationStats(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "registration"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "registration")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000, // 1 minute
	});
}

export function useAttendanceStats(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "attendance"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "attendance")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useWatchTimeStats(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "watch-time"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "watch-time")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useDropoffAnalysis(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "dropoffs"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "dropoffs")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useDeviceAnalytics(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "devices"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "devices")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useOfferAnalytics(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "offers"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "offers")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useConversionAnalytics(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "conversions"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "conversions")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useRevenueAnalytics(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "revenue"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "revenue")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useLiveVsReplayComparison(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "live-vs-replay"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "live-vs-replay")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useCRMTags(webinarId: string | null) {
	return useQuery({
		queryKey: ["analytics", webinarId, "crm-tags"],
		queryFn: () =>
			webinarId
				? fetchAnalytics(webinarId, "crm-tags")
				: Promise.resolve(null),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

