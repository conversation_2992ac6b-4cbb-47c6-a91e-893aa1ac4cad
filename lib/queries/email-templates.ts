"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type {
	EmailTemplate,
	ReminderSchedule,
	WebinarAttendance,
	EmailQueue,
} from "@/lib/supabase";

// Response types
export interface EmailTemplatesResponse {
	templates: EmailTemplate[];
}

export interface EmailTemplateResponse {
	template: EmailTemplate;
}

export interface ReminderSchedulesResponse {
	schedules: ReminderSchedule[];
}

export interface ReminderScheduleResponse {
	schedule: ReminderSchedule;
}

export interface AttendanceResponse {
	attendance: WebinarAttendance | WebinarAttendance[];
}

export interface EmailQueueResponse {
	queue: EmailQueue[];
}

// Query functions
async function fetchEmailTemplates(companyId: string): Promise<EmailTemplate[]> {
	const response = await fetch(`/api/email-templates?companyId=${companyId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch email templates",
		}));
		throw new Error(error.error || "Failed to fetch email templates");
	}

	const data: EmailTemplatesResponse = await response.json();
	return data.templates;
}

async function fetchEmailTemplate(templateId: string): Promise<EmailTemplate> {
	const response = await fetch(`/api/email-templates?templateId=${templateId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch email template",
		}));
		throw new Error(error.error || "Failed to fetch email template");
	}

	const data: EmailTemplateResponse = await response.json();
	return data.template;
}

async function fetchReminderSchedules(
	webinarId: string,
): Promise<ReminderSchedule[]> {
	const response = await fetch(`/api/reminder-schedules?webinarId=${webinarId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch reminder schedules",
		}));
		throw new Error(error.error || "Failed to fetch reminder schedules");
	}

	const data: ReminderSchedulesResponse = await response.json();
	return data.schedules;
}

async function fetchAttendance(
	webinarId?: string,
	submissionId?: string,
): Promise<WebinarAttendance | WebinarAttendance[] | null> {
	const searchParams = new URLSearchParams();
	if (webinarId) searchParams.append("webinarId", webinarId);
	if (submissionId) searchParams.append("submissionId", submissionId);

	const response = await fetch(`/api/attendance?${searchParams.toString()}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch attendance",
		}));
		throw new Error(error.error || "Failed to fetch attendance");
	}

	const data: AttendanceResponse = await response.json();
	return data.attendance;
}

async function fetchEmailQueue(webinarId: string): Promise<EmailQueue[]> {
	const response = await fetch(`/api/email-queue?webinarId=${webinarId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch email queue",
		}));
		throw new Error(error.error || "Failed to fetch email queue");
	}

	const data: EmailQueueResponse = await response.json();
	return data.queue;
}

// Mutation functions
async function createEmailTemplate(
	data: Partial<EmailTemplate>,
): Promise<EmailTemplate> {
	const response = await fetch("/api/email-templates", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to create email template",
		}));
		throw new Error(error.error || "Failed to create email template");
	}

	const result: EmailTemplateResponse = await response.json();
	return result.template;
}

async function updateEmailTemplate(
	data: Partial<EmailTemplate> & { id: string },
): Promise<EmailTemplate> {
	const { id, ...updateData } = data;
	const response = await fetch(`/api/email-templates/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(updateData),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to update email template",
		}));
		throw new Error(error.error || "Failed to update email template");
	}

	const result: EmailTemplateResponse = await response.json();
	return result.template;
}

async function deleteEmailTemplate(templateId: string): Promise<void> {
	const response = await fetch(`/api/email-templates/${templateId}`, {
		method: "DELETE",
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to delete email template",
		}));
		throw new Error(error.error || "Failed to delete email template");
	}
}

async function createReminderSchedule(
	data: Partial<ReminderSchedule>,
): Promise<ReminderSchedule> {
	const response = await fetch("/api/reminder-schedules", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to create reminder schedule",
		}));
		throw new Error(error.error || "Failed to create reminder schedule");
	}

	const result: ReminderScheduleResponse = await response.json();
	return result.schedule;
}

async function updateReminderSchedule(
	data: Partial<ReminderSchedule> & { id: string },
): Promise<ReminderSchedule> {
	const { id, ...updateData } = data;
	const response = await fetch(`/api/reminder-schedules/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(updateData),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to update reminder schedule",
		}));
		throw new Error(error.error || "Failed to update reminder schedule");
	}

	const result: ReminderScheduleResponse = await response.json();
	return result.schedule;
}

async function deleteReminderSchedule(scheduleId: string): Promise<void> {
	const response = await fetch(`/api/reminder-schedules/${scheduleId}`, {
		method: "DELETE",
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to delete reminder schedule",
		}));
		throw new Error(error.error || "Failed to delete reminder schedule");
	}
}

// Query hooks
export function useEmailTemplates(companyId: string | null) {
	return useQuery({
		queryKey: ["email-templates", companyId],
		queryFn: () => (companyId ? fetchEmailTemplates(companyId) : []),
		enabled: !!companyId,
		staleTime: 60 * 1000, // 1 minute
	});
}

export function useEmailTemplate(templateId: string | null) {
	return useQuery({
		queryKey: ["email-template", templateId],
		queryFn: () => (templateId ? fetchEmailTemplate(templateId) : null),
		enabled: !!templateId,
		staleTime: 60 * 1000, // 1 minute
	});
}

export function useReminderSchedules(webinarId: string | null) {
	return useQuery({
		queryKey: ["reminder-schedules", webinarId],
		queryFn: () => (webinarId ? fetchReminderSchedules(webinarId) : []),
		enabled: !!webinarId,
		staleTime: 60 * 1000, // 1 minute
	});
}

export function useAttendance(webinarId?: string, submissionId?: string) {
	return useQuery({
		queryKey: ["attendance", webinarId, submissionId],
		queryFn: () => fetchAttendance(webinarId, submissionId),
		enabled: !!webinarId || !!submissionId,
		staleTime: 30 * 1000, // 30 seconds
	});
}

export function useEmailQueue(webinarId: string | null) {
	return useQuery({
		queryKey: ["email-queue", webinarId],
		queryFn: () => (webinarId ? fetchEmailQueue(webinarId) : []),
		enabled: !!webinarId,
		staleTime: 30 * 1000, // 30 seconds
	});
}

// Mutation hooks
export function useCreateEmailTemplate() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createEmailTemplate,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["email-templates", data.company_id],
			});
		},
	});
}

export function useUpdateEmailTemplate() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateEmailTemplate,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["email-templates", data.company_id],
			});
			queryClient.setQueryData(["email-template", data.id], { template: data });
		},
	});
}

export function useDeleteEmailTemplate() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteEmailTemplate,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["email-templates"] });
		},
	});
}

export function useCreateReminderSchedule() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createReminderSchedule,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["reminder-schedules", data.webinar_id],
			});
		},
	});
}

export function useUpdateReminderSchedule() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateReminderSchedule,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["reminder-schedules", data.webinar_id],
			});
		},
	});
}

export function useDeleteReminderSchedule() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteReminderSchedule,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["reminder-schedules"] });
		},
	});
}

