"use client";

import { useQuery } from "@tanstack/react-query";
import type { ExperiencesResponse } from "./types";

// Fetch experiences query function
async function fetchExperiences(companyId: string, currentExperienceId?: string): Promise<ExperiencesResponse["experiences"]> {
	const searchParams = new URLSearchParams({ companyId });
	if (currentExperienceId) {
		searchParams.append("experienceId", currentExperienceId);
	}

	const response = await fetch(`/api/experiences?${searchParams.toString()}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({ error: "Failed to fetch experiences" }));
		throw new Error(error.error || "Failed to fetch experiences");
	}

	const data: ExperiencesResponse = await response.json();
	return data.experiences;
}

// Query hook
export function useExperiences(companyId: string | null, currentExperienceId?: string) {
	return useQuery({
		queryKey: ["experiences", companyId, currentExperienceId],
		queryFn: () => (companyId ? fetchExperiences(companyId, currentExperienceId) : []),
		enabled: !!companyId,
		staleTime: 5 * 60 * 1000, // 5 minutes (experiences don't change often)
	});
}

