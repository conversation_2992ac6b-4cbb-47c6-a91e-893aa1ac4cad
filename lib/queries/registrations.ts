"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type {
	RegistrationPage,
	RegistrationSubmission,
	RegistrationTag,
	CustomField,
	ThankYouPageConfig,
} from "@/lib/supabase";

// Response types
export interface RegistrationPagesResponse {
	registrationPages: RegistrationPage[];
}

export interface RegistrationPageResponse {
	registrationPage: RegistrationPage;
}

export interface RegistrationsResponse {
	registrations: RegistrationSubmission[];
}

export interface RegistrationResponse {
	registration: RegistrationSubmission;
}

export interface TagsResponse {
	tags: RegistrationTag[];
}

export interface TagResponse {
	tag: RegistrationTag;
}

// Fetch params
export interface FetchRegistrationPagesParams {
	webinarId?: string;
	companyId?: string;
}

export interface CreateRegistrationPageData {
	webinar_id: string;
	experience_id: string;
	company_id: string;
	slug: string;
	template_id?: string;
	title: string;
	description?: string;
	custom_fields?: CustomField[];
	thank_you_page_config?: ThankYouPageConfig;
	is_active?: boolean;
	requires_auth?: boolean;
}

export interface UpdateRegistrationPageData extends Partial<CreateRegistrationPageData> {
	id: string;
}

export interface CreateRegistrationData {
	registration_page_id: string;
	webinar_id: string;
	email: string;
	name: string;
	phone?: string;
	custom_field_data?: Record<string, unknown>;
	source: "public" | "internal";
}

export interface CreateTagData {
	company_id: string;
	name: string;
	color?: string;
}

export interface UpdateTagData {
	id: string;
	name?: string;
	color?: string;
}

export interface AssignTagsData {
	submissionId: string;
	tag_ids: string[];
}

// Query functions
async function fetchRegistrationPages(
	params: FetchRegistrationPagesParams,
): Promise<RegistrationPagesResponse["registrationPages"]> {
	const searchParams = new URLSearchParams();
	if (params.webinarId) {
		searchParams.append("webinarId", params.webinarId);
	}
	if (params.companyId) {
		searchParams.append("companyId", params.companyId);
	}

	const response = await fetch(`/api/registration-pages?${searchParams.toString()}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch registration pages",
		}));
		throw new Error(error.error || "Failed to fetch registration pages");
	}

	const data: RegistrationPagesResponse = await response.json();
	return data.registrationPages;
}

async function fetchRegistrationPage(
	pageId: string | null,
): Promise<RegistrationPageResponse["registrationPage"] | null> {
	if (!pageId) return null;

	const response = await fetch(`/api/registration-pages/${pageId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch registration page",
		}));
		throw new Error(error.error || "Failed to fetch registration page");
	}

	const data: RegistrationPageResponse = await response.json();
	return data.registrationPage;
}

async function fetchRegistrationPageBySlug(
	slug: string | null,
): Promise<RegistrationPageResponse["registrationPage"] | null> {
	if (!slug) return null;

	const response = await fetch(`/api/registration-pages?slug=${slug}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch registration page",
		}));
		throw new Error(error.error || "Failed to fetch registration page");
	}

	const data: RegistrationPageResponse = await response.json();
	return data.registrationPage;
}

async function fetchRegistrations(
	pageId?: string,
	webinarId?: string,
): Promise<RegistrationsResponse["registrations"]> {
	const searchParams = new URLSearchParams();
	if (pageId) {
		searchParams.append("pageId", pageId);
	}
	if (webinarId) {
		searchParams.append("webinarId", webinarId);
	}

	const response = await fetch(`/api/registrations?${searchParams.toString()}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch registrations",
		}));
		throw new Error(error.error || "Failed to fetch registrations");
	}

	const data: RegistrationsResponse = await response.json();
	return data.registrations;
}

async function fetchRegistrationTags(
	companyId: string | null,
): Promise<TagsResponse["tags"]> {
	if (!companyId) return [];

	const response = await fetch(`/api/registration-tags?companyId=${companyId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch tags",
		}));
		throw new Error(error.error || "Failed to fetch tags");
	}

	const data: TagsResponse = await response.json();
	return data.tags;
}

// Mutation functions
async function createRegistrationPage(
	data: CreateRegistrationPageData,
): Promise<RegistrationPageResponse["registrationPage"]> {
	const response = await fetch("/api/registration-pages", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to create registration page",
		}));
		throw new Error(error.error || "Failed to create registration page");
	}

	const result: RegistrationPageResponse = await response.json();
	return result.registrationPage;
}

async function updateRegistrationPage(
	data: UpdateRegistrationPageData,
): Promise<RegistrationPageResponse["registrationPage"]> {
	const { id, ...updateData } = data;
	const response = await fetch(`/api/registration-pages/${id}`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(updateData),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to update registration page",
		}));
		throw new Error(error.error || "Failed to update registration page");
	}

	const result: RegistrationPageResponse = await response.json();
	return result.registrationPage;
}

async function deleteRegistrationPage(pageId: string): Promise<void> {
	const response = await fetch(`/api/registration-pages/${pageId}`, {
		method: "DELETE",
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to delete registration page",
		}));
		throw new Error(error.error || "Failed to delete registration page");
	}
}

async function createRegistration(
	data: CreateRegistrationData,
): Promise<RegistrationResponse["registration"]> {
	const response = await fetch("/api/registrations", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to submit registration",
		}));
		throw new Error(error.error || "Failed to submit registration");
	}

	const result: RegistrationResponse = await response.json();
	return result.registration;
}

async function createTag(data: CreateTagData): Promise<TagResponse["tag"]> {
	const response = await fetch("/api/registration-tags", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to create tag",
		}));
		throw new Error(error.error || "Failed to create tag");
	}

	const result: TagResponse = await response.json();
	return result.tag;
}

async function updateTag(data: UpdateTagData): Promise<TagResponse["tag"]> {
	const { id, ...updateData } = data;
	const response = await fetch(`/api/registration-tags/${id}`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(updateData),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to update tag",
		}));
		throw new Error(error.error || "Failed to update tag");
	}

	const result: TagResponse = await response.json();
	return result.tag;
}

async function deleteTag(tagId: string): Promise<void> {
	const response = await fetch(`/api/registration-tags/${tagId}`, {
		method: "DELETE",
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to delete tag",
		}));
		throw new Error(error.error || "Failed to delete tag");
	}
}

async function assignTagsToSubmission(
	data: AssignTagsData,
): Promise<void> {
	const { submissionId, tag_ids } = data;
	const response = await fetch(`/api/registrations/${submissionId}/tags`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({ tag_ids }),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to assign tags",
		}));
		throw new Error(error.error || "Failed to assign tags");
	}
}

// Query hooks
export function useRegistrationPages(params: FetchRegistrationPagesParams) {
	return useQuery({
		queryKey: ["registration-pages", params.webinarId, params.companyId],
		queryFn: () => fetchRegistrationPages(params),
		enabled: !!(params.webinarId || params.companyId),
		staleTime: 60 * 1000, // 1 minute
	});
}

export function useRegistrationPage(pageId: string | null) {
	return useQuery({
		queryKey: ["registration-page", pageId],
		queryFn: () => fetchRegistrationPage(pageId),
		enabled: !!pageId,
		staleTime: 60 * 1000, // 1 minute
	});
}

export function useRegistrationPageBySlug(slug: string | null) {
	return useQuery({
		queryKey: ["registration-page-slug", slug],
		queryFn: () => fetchRegistrationPageBySlug(slug),
		enabled: !!slug,
		staleTime: 60 * 1000, // 1 minute
	});
}

export function useRegistrations(pageId?: string, webinarId?: string) {
	return useQuery({
		queryKey: ["registrations", pageId, webinarId],
		queryFn: () => fetchRegistrations(pageId, webinarId),
		enabled: !!(pageId || webinarId),
		staleTime: 30 * 1000, // 30 seconds
	});
}

export function useRegistrationTags(companyId: string | null) {
	return useQuery({
		queryKey: ["registration-tags", companyId],
		queryFn: () => fetchRegistrationTags(companyId),
		enabled: !!companyId,
		staleTime: 60 * 1000, // 1 minute
	});
}

// Mutation hooks
export function useCreateRegistrationPage() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createRegistrationPage,
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["registration-pages"] });
			queryClient.setQueryData(["registration-page", data.id], {
				registrationPage: data,
			});
		},
	});
}

export function useUpdateRegistrationPage() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateRegistrationPage,
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["registration-pages"] });
			queryClient.setQueryData(["registration-page", data.id], {
				registrationPage: data,
			});
		},
	});
}

export function useDeleteRegistrationPage() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteRegistrationPage,
		onSuccess: (_, pageId) => {
			queryClient.invalidateQueries({ queryKey: ["registration-pages"] });
			queryClient.removeQueries({ queryKey: ["registration-page", pageId] });
		},
	});
}

export function useCreateRegistration() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createRegistration,
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["registrations"] });
		},
	});
}

export function useCreateTag() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createTag,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["registration-tags", data.company_id],
			});
		},
	});
}

export function useUpdateTag() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateTag,
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: ["registration-tags"] });
		},
	});
}

export function useDeleteTag() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteTag,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["registration-tags"] });
		},
	});
}

export function useAssignTagsToSubmission() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: assignTagsToSubmission,
		onSuccess: (_, data) => {
			queryClient.invalidateQueries({
				queryKey: ["registrations", data.submissionId],
			});
			queryClient.invalidateQueries({ queryKey: ["registrations"] });
		},
	});
}

