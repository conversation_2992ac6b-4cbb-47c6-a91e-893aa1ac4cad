"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { ReminderSchedule } from "@/lib/supabase";

// Response types
export interface ReminderSchedulesResponse {
	schedules: ReminderSchedule[];
}

export interface ReminderScheduleResponse {
	schedule: ReminderSchedule;
}

// Query functions
async function fetchReminderSchedules(
	webinarId: string,
): Promise<ReminderSchedulesResponse["schedules"]> {
	const response = await fetch(`/api/reminder-schedules?webinarId=${webinarId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch reminder schedules",
		}));
		throw new Error(error.error || "Failed to fetch reminder schedules");
	}

	const data: ReminderSchedulesResponse = await response.json();
	return data.schedules;
}

// Mutation functions
async function createReminderSchedule(
	data: Partial<ReminderSchedule>,
): Promise<ReminderScheduleResponse["schedule"]> {
	const response = await fetch("/api/reminder-schedules", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to create reminder schedule",
		}));
		throw new Error(error.error || "Failed to create reminder schedule");
	}

	const result: ReminderScheduleResponse = await response.json();
	return result.schedule;
}

async function updateReminderSchedule(
	data: Partial<ReminderSchedule> & { id: string },
): Promise<ReminderScheduleResponse["schedule"]> {
	const { id, ...updateData } = data;
	const response = await fetch(`/api/reminder-schedules/${id}`, {
		method: "PATCH",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(updateData),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to update reminder schedule",
		}));
		throw new Error(error.error || "Failed to update reminder schedule");
	}

	const result: ReminderScheduleResponse = await response.json();
	return result.schedule;
}

async function deleteReminderSchedule(scheduleId: string): Promise<void> {
	const response = await fetch(`/api/reminder-schedules/${scheduleId}`, {
		method: "DELETE",
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to delete reminder schedule",
		}));
		throw new Error(error.error || "Failed to delete reminder schedule");
	}
}

// Query hooks
export function useReminderSchedules(webinarId: string | null) {
	return useQuery({
		queryKey: ["reminder-schedules", webinarId],
		queryFn: () => (webinarId ? fetchReminderSchedules(webinarId) : []),
		enabled: !!webinarId,
		staleTime: 60 * 1000, // 1 minute
	});
}

// Mutation hooks
export function useCreateReminderSchedule() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createReminderSchedule,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["reminder-schedules", data.webinar_id],
			});
		},
	});
}

export function useUpdateReminderSchedule() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateReminderSchedule,
		onSuccess: (data) => {
			queryClient.invalidateQueries({
				queryKey: ["reminder-schedules", data.webinar_id],
			});
		},
	});
}

export function useDeleteReminderSchedule() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteReminderSchedule,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["reminder-schedules"] });
		},
	});
}

