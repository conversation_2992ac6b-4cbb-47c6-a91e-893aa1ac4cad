"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

async function fetchTeamMembers(
	companyId: string,
	experienceId?: string,
): Promise<unknown[]> {
	const params = new URLSearchParams();
	if (experienceId) {
		params.append("experienceId", experienceId);
	}
	const response = await fetch(
		`/api/team/${companyId}/members?${params.toString()}`,
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch team members",
		}));
		throw new Error(error.error || "Failed to fetch team members");
	}
	const data = await response.json();
	return data.members || [];
}

async function fetchBranding(companyId: string): Promise<unknown | null> {
	const response = await fetch(`/api/team/${companyId}/branding`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch branding",
		}));
		throw new Error(error.error || "Failed to fetch branding");
	}
	const data = await response.json();
	return data.branding || null;
}

async function addTeamMember(
	companyId: string,
	user_id: string,
	role: string,
	experience_id?: string,
	permissions?: Record<string, unknown>,
): Promise<unknown> {
	const response = await fetch(`/api/team/${companyId}/members`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({
			user_id,
			role,
			experience_id,
			permissions,
		}),
	});
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to add team member",
		}));
		throw new Error(error.error || "Failed to add team member");
	}
	const data = await response.json();
	return data.member;
}

async function updateTeamMember(
	companyId: string,
	memberId: string,
	role?: string,
	permissions?: Record<string, unknown>,
): Promise<unknown> {
	const response = await fetch(`/api/team/${companyId}/members/${memberId}`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({ role, permissions }),
	});
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to update team member",
		}));
		throw new Error(error.error || "Failed to update team member");
	}
	const data = await response.json();
	return data.member;
}

async function removeTeamMember(
	companyId: string,
	memberId: string,
): Promise<void> {
	const response = await fetch(
		`/api/team/${companyId}/members/${memberId}`,
		{
			method: "DELETE",
		},
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to remove team member",
		}));
		throw new Error(error.error || "Failed to remove team member");
	}
}

async function updateBranding(
	companyId: string,
	branding: {
		logo_url?: string;
		background_color?: string;
		text_color?: string;
		accent_color?: string;
		favicon_url?: string;
		custom_css?: string;
	},
): Promise<unknown> {
	const response = await fetch(`/api/team/${companyId}/branding`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(branding),
	});
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to update branding",
		}));
		throw new Error(error.error || "Failed to update branding");
	}
	const data = await response.json();
	return data.branding;
}

export function useTeamMembers(
	companyId: string | null,
	experienceId?: string | null,
) {
	return useQuery({
		queryKey: ["team-members", companyId, experienceId],
		queryFn: () =>
			companyId ? fetchTeamMembers(companyId, experienceId || undefined) : [],
		enabled: !!companyId,
		staleTime: 60 * 1000,
	});
}

export function useBranding(companyId: string | null) {
	return useQuery({
		queryKey: ["branding", companyId],
		queryFn: () => (companyId ? fetchBranding(companyId) : null),
		enabled: !!companyId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
}

export function useAddTeamMember() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			companyId,
			user_id,
			role,
			experience_id,
			permissions,
		}: {
			companyId: string;
			user_id: string;
			role: string;
			experience_id?: string;
			permissions?: Record<string, unknown>;
		}) => addTeamMember(companyId, user_id, role, experience_id, permissions),
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["team-members", variables.companyId],
			});
		},
	});
}

export function useUpdateTeamMember() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			companyId,
			memberId,
			role,
			permissions,
		}: {
			companyId: string;
			memberId: string;
			role?: string;
			permissions?: Record<string, unknown>;
		}) => updateTeamMember(companyId, memberId, role, permissions),
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["team-members", variables.companyId],
			});
		},
	});
}

export function useRemoveTeamMember() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			companyId,
			memberId,
		}: {
			companyId: string;
			memberId: string;
		}) => removeTeamMember(companyId, memberId),
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["team-members", variables.companyId],
			});
		},
	});
}

export function useUpdateBranding() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			companyId,
			...branding
		}: {
			companyId: string;
			logo_url?: string;
			background_color?: string;
			text_color?: string;
			accent_color?: string;
			favicon_url?: string;
			custom_css?: string;
		}) => updateBranding(companyId, branding),
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["branding", variables.companyId],
			});
		},
	});
}

