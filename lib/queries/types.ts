import type { Webinar } from "@/lib/supabase";

export interface WebinarsResponse {
	webinars: Webinar[];
}

export interface WebinarResponse {
	webinar: Webinar;
}

export interface ExperiencesResponse {
	experiences: { id: string; name: string }[];
}

export interface FetchWebinarsParams {
	experienceId?: string;
	companyId?: string;
}

export interface CreateWebinarData {
	experience_id: string;
	company_id?: string;
	title: string;
	description?: string | null;
	scheduled_at?: string;
	duration_minutes?: number;
	host_ids?: string[];
	event_type?: "live" | "right_now" | "recurring" | "always_on" | "evergreen_room";
	recurrence_pattern?: "daily" | "weekly" | "monthly" | null;
	recurrence_interval?: number | null;
	recurrence_end_date?: string | null;
	recurrence_count?: number | null;
	room_name?: string | null;
	timezone?: string | null;
	password?: string | null;
	is_password_protected?: boolean | null;
	price_amount?: number | null;
	price_currency?: string | null;
	is_paid?: boolean | null;
	custom_branding_logo_url?: string | null;
	custom_branding_background_color?: string | null;
	custom_branding_text_color?: string | null;
	custom_branding_accent_color?: string | null;
	presenter_roles?: Record<string, string> | null;
}

export interface UpdateWebinarData extends Partial<CreateWebinarData> {
	id: string;
}

