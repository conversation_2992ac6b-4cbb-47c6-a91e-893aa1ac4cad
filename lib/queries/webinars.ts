"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type {
	CreateWebinarData,
	FetchWebinarsParams,
	UpdateWebinarData,
	WebinarResponse,
	WebinarsResponse,
} from "./types";

// Fetch webinars query function
async function fetchWebinars(params: FetchWebinarsParams): Promise<WebinarsResponse["webinars"]> {
	const searchParams = new URLSearchParams();
	if (params.experienceId) {
		searchParams.append("experienceId", params.experienceId);
	}
	if (params.companyId) {
		searchParams.append("companyId", params.companyId);
	}

	const response = await fetch(`/api/webinars?${searchParams.toString()}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({ error: "Failed to fetch webinars" }));
		throw new Error(error.error || "Failed to fetch webinars");
	}

	const data: WebinarsResponse = await response.json();
	return data.webinars;
}

// Fetch single webinar query function
async function fetchWebinar(webinarId: string): Promise<WebinarResponse["webinar"]> {
	const response = await fetch(`/api/webinars/${webinarId}`);
	if (!response.ok) {
		const error = await response.json().catch(() => ({ error: "Failed to fetch webinar" }));
		throw new Error(error.error || "Failed to fetch webinar");
	}

	const data: WebinarResponse = await response.json();
	return data.webinar;
}

// Create webinar mutation function
async function createWebinar(data: CreateWebinarData): Promise<WebinarResponse["webinar"]> {
	const response = await fetch("/api/webinars", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({ error: "Failed to create webinar" }));
		throw new Error(error.error || "Failed to create webinar");
	}

	const result: WebinarResponse = await response.json();
	return result.webinar;
}

// Update webinar mutation function
async function updateWebinar(data: UpdateWebinarData): Promise<WebinarResponse["webinar"]> {
	const { id, ...updateData } = data;
	const response = await fetch(`/api/webinars/${id}`, {
		method: "PATCH",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(updateData),
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({ error: "Failed to update webinar" }));
		throw new Error(error.error || "Failed to update webinar");
	}

	const result: WebinarResponse = await response.json();
	return result.webinar;
}

// Delete webinar mutation function
async function deleteWebinar(webinarId: string): Promise<void> {
	const response = await fetch(`/api/webinars/${webinarId}`, {
		method: "DELETE",
	});

	if (!response.ok) {
		const error = await response.json().catch(() => ({ error: "Failed to delete webinar" }));
		throw new Error(error.error || "Failed to delete webinar");
	}
}

// Query hooks
export function useWebinars(params: FetchWebinarsParams) {
	return useQuery({
		queryKey: ["webinars", params.experienceId, params.companyId],
		queryFn: () => fetchWebinars(params),
		enabled: !!(params.experienceId || params.companyId),
		staleTime: 60 * 1000, // 1 minute
		refetchInterval: 60 * 1000, // Refetch every minute to update status
	});
}

export function useWebinar(webinarId: string | null) {
	return useQuery({
		queryKey: ["webinar", webinarId],
		queryFn: () => (webinarId ? fetchWebinar(webinarId) : null),
		enabled: !!webinarId,
		staleTime: 60 * 1000, // 1 minute
	});
}

// Mutation hooks
export function useCreateWebinar() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: createWebinar,
		onSuccess: (data) => {
			// Invalidate webinars queries
			queryClient.invalidateQueries({ queryKey: ["webinars"] });
			// Optionally update the cache with the new webinar
			queryClient.setQueryData(["webinar", data.id], { webinar: data });
		},
	});
}

export function useUpdateWebinar() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: updateWebinar,
		onSuccess: (data) => {
			// Invalidate webinars queries
			queryClient.invalidateQueries({ queryKey: ["webinars"] });
			// Update the specific webinar cache
			queryClient.setQueryData(["webinar", data.id], { webinar: data });
		},
	});
}

export function useDeleteWebinar() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: deleteWebinar,
		onSuccess: (_, webinarId) => {
			// Invalidate webinars queries
			queryClient.invalidateQueries({ queryKey: ["webinars"] });
			// Remove the specific webinar from cache
			queryClient.removeQueries({ queryKey: ["webinar", webinarId] });
		},
	});
}

