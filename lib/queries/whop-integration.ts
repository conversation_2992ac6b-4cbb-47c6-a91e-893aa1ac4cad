"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

async function fetchWhopProducts(companyId: string): Promise<unknown[]> {
	const response = await fetch(
		`/api/whop-integration/products?companyId=${companyId}`,
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch products",
		}));
		throw new Error(error.error || "Failed to fetch products");
	}
	const data = await response.json();
	return data.products || [];
}

async function fetchLinkedProducts(webinarId: string): Promise<unknown[]> {
	const response = await fetch(
		`/api/whop-integration/products/linked?webinarId=${webinarId}`,
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch linked products",
		}));
		throw new Error(error.error || "Failed to fetch linked products");
	}
	const data = await response.json();
	return data.products || [];
}

async function fetchTransactions(
	webinarId: string,
	status?: string,
): Promise<unknown[]> {
	const params = new URLSearchParams({ webinarId });
	if (status) {
		params.append("status", status);
	}
	const response = await fetch(
		`/api/whop-integration/transactions?${params.toString()}`,
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to fetch transactions",
		}));
		throw new Error(error.error || "Failed to fetch transactions");
	}
	const data = await response.json();
	return data.transactions || [];
}

async function linkProduct(
	productId: string,
	webinarId: string,
	whopCompanyId: string,
	isRequiredForAccess: boolean,
): Promise<unknown> {
	const response = await fetch(
		`/api/whop-integration/products/${productId}/link`,
		{
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				webinarId,
				whopCompanyId,
				isRequiredForAccess,
			}),
		},
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to link product",
		}));
		throw new Error(error.error || "Failed to link product");
	}
	const data = await response.json();
	return data.product;
}

async function unlinkProduct(
	productId: string,
	webinarId: string,
): Promise<void> {
	const response = await fetch(
		`/api/whop-integration/products/${productId}/link?webinarId=${webinarId}`,
		{
			method: "DELETE",
		},
	);
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to unlink product",
		}));
		throw new Error(error.error || "Failed to unlink product");
	}
}

async function createCheckout(
	webinarId: string,
	productId: string,
): Promise<{ checkout_url: string; session_id: string }> {
	const response = await fetch("/api/whop-integration/checkout", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({ webinarId, productId }),
	});
	if (!response.ok) {
		const error = await response.json().catch(() => ({
			error: "Failed to create checkout",
		}));
		throw new Error(error.error || "Failed to create checkout");
	}
	return response.json();
}

export function useWhopProducts(companyId: string | null) {
	return useQuery({
		queryKey: ["whop-products", companyId],
		queryFn: () => (companyId ? fetchWhopProducts(companyId) : []),
		enabled: !!companyId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
}

export function useLinkedProducts(webinarId: string | null) {
	return useQuery({
		queryKey: ["linked-products", webinarId],
		queryFn: () => (webinarId ? fetchLinkedProducts(webinarId) : []),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useWhopTransactions(
	webinarId: string | null,
	status?: string,
) {
	return useQuery({
		queryKey: ["whop-transactions", webinarId, status],
		queryFn: () => (webinarId ? fetchTransactions(webinarId, status) : []),
		enabled: !!webinarId,
		staleTime: 60 * 1000,
	});
}

export function useLinkProduct() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			productId,
			webinarId,
			whopCompanyId,
			isRequiredForAccess,
		}: {
			productId: string;
			webinarId: string;
			whopCompanyId: string;
			isRequiredForAccess: boolean;
		}) =>
			linkProduct(productId, webinarId, whopCompanyId, isRequiredForAccess),
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["linked-products", variables.webinarId],
			});
		},
	});
}

export function useUnlinkProduct() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			productId,
			webinarId,
		}: {
			productId: string;
			webinarId: string;
		}) => unlinkProduct(productId, webinarId),
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({
				queryKey: ["linked-products", variables.webinarId],
			});
		},
	});
}

export function useCreateCheckout() {
	return useMutation({
		mutationFn: ({
			webinarId,
			productId,
		}: {
			webinarId: string;
			productId: string;
		}) => createCheckout(webinarId, productId),
	});
}

