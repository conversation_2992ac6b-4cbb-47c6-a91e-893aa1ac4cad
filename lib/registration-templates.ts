import type { CustomField, ThankYouPageConfig } from "@/lib/supabase";

export interface RegistrationTemplate {
	id: string;
	name: string;
	description: string;
	defaultFields: CustomField[];
	defaultThankYouConfig: ThankYouPageConfig;
}

/**
 * Registration page templates
 */
export const REGISTRATION_TEMPLATES: RegistrationTemplate[] = [
	{
		id: "minimal",
		name: "Mini<PERSON>",
		description: "Just the essentials - name and email only",
		defaultFields: [
			{
				id: "name",
				type: "text",
				label: "Full Name",
				placeholder: "Enter your full name",
				required: true,
				order: 0,
			},
			{
				id: "email",
				type: "text",
				label: "Email Address",
				placeholder: "<EMAIL>",
				required: true,
				validation: {
					pattern: "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$",
				},
				order: 1,
			},
		],
		defaultThankYouConfig: {
			title: "Thank You!",
			message: "You're all set! We'll send you a reminder before the webinar.",
			showCalendarDownload: true,
		},
	},
	{
		id: "standard",
		name: "Standard",
		description: "Name, email, and phone number",
		defaultFields: [
			{
				id: "name",
				type: "text",
				label: "Full Name",
				placeholder: "Enter your full name",
				required: true,
				order: 0,
			},
			{
				id: "email",
				type: "text",
				label: "Email Address",
				placeholder: "<EMAIL>",
				required: true,
				validation: {
					pattern: "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$",
				},
				order: 1,
			},
			{
				id: "phone",
				type: "text",
				label: "Phone Number",
				placeholder: "(*************",
				required: false,
				order: 2,
			},
		],
		defaultThankYouConfig: {
			title: "Thank You for Registering!",
			message:
				"Your registration is confirmed. We'll send you a reminder before the webinar starts.",
			showCalendarDownload: true,
		},
	},
	{
		id: "detailed",
		name: "Detailed",
		description: "Comprehensive form with company and role fields",
		defaultFields: [
			{
				id: "name",
				type: "text",
				label: "Full Name",
				placeholder: "Enter your full name",
				required: true,
				order: 0,
			},
			{
				id: "email",
				type: "text",
				label: "Email Address",
				placeholder: "<EMAIL>",
				required: true,
				validation: {
					pattern: "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$",
				},
				order: 1,
			},
			{
				id: "phone",
				type: "text",
				label: "Phone Number",
				placeholder: "(*************",
				required: false,
				order: 2,
			},
			{
				id: "company",
				type: "text",
				label: "Company",
				placeholder: "Your company name",
				required: false,
				order: 3,
			},
			{
				id: "role",
				type: "select",
				label: "Job Role",
				placeholder: "Select your role",
				required: false,
				options: [
					"CEO/Founder",
					"Director",
					"Manager",
					"Individual Contributor",
					"Student",
					"Other",
				],
				order: 4,
			},
		],
		defaultThankYouConfig: {
			title: "Registration Confirmed!",
			message:
				"Thank you for registering. We're excited to have you join us! Check your email for a confirmation and calendar invite.",
			showCalendarDownload: true,
		},
	},
];

/**
 * Get template by ID
 */
export function getTemplate(templateId: string): RegistrationTemplate | null {
	return (
		REGISTRATION_TEMPLATES.find((t) => t.id === templateId) || null
	);
}

/**
 * Get default template (standard)
 */
export function getDefaultTemplate(): RegistrationTemplate {
	return REGISTRATION_TEMPLATES.find((t) => t.id === "standard")!;
}

