import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
	throw new Error(
		"Missing Supabase environment variables. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY",
	);
}

// Client for client-side operations (uses anon key)
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Client for server-side operations (uses service role key if available, otherwise anon key)
export const supabaseServer = createClient(
	supabaseUrl,
	supabaseServiceRoleKey || supabaseAnonKey,
	{
		auth: {
			autoRefreshToken: false,
			persistSession: false,
		},
	},
);

export interface Webinar {
	id: string;
	experience_id: string;
	company_id: string;
	title: string;
	description: string | null;
	scheduled_at: string;
	duration_minutes: number;
	host_ids: string[];
	created_at: string;
	created_by: string;
	status: "scheduled" | "live" | "completed" | "cancelled";
	event_type: "live" | "right_now" | "recurring" | "always_on" | "evergreen_room";
	recurrence_pattern?: "daily" | "weekly" | "monthly" | null;
	recurrence_interval?: number | null;
	recurrence_end_date?: string | null;
	recurrence_count?: number | null;
	is_evergreen?: boolean | null;
	room_name?: string | null;
	timezone?: string | null;
	password?: string | null;
	is_password_protected?: boolean | null;
	price_amount?: number | null;
	price_currency?: string | null;
	is_paid?: boolean | null;
	custom_branding_logo_url?: string | null;
	custom_branding_background_color?: string | null;
	custom_branding_text_color?: string | null;
	custom_branding_accent_color?: string | null;
	presenter_roles?: Record<string, string> | null; // Map of host_id -> role
}

export type PresenterRole = "host" | "moderator" | "presenter" | "panelist" | "co-host";

// Registration Page Builder Types

export interface CustomField {
	id: string;
	type: "text" | "textarea" | "select" | "checkbox" | "radio" | "number" | "date" | "url";
	label: string;
	placeholder?: string;
	required: boolean;
	options?: string[]; // For select, radio types
	validation?: {
		min?: number;
		max?: number;
		pattern?: string;
	};
	order: number;
}

export interface ThankYouPageConfig {
	title?: string;
	message?: string;
	showCalendarDownload?: boolean;
	redirectUrl?: string;
	redirectDelay?: number;
	customContent?: string; // HTML content
	backgroundColor?: string;
	textColor?: string;
}

export interface RegistrationPage {
	id: string;
	webinar_id: string;
	experience_id: string;
	company_id: string;
	slug: string;
	template_id: string;
	title: string;
	description: string | null;
	custom_fields: CustomField[];
	thank_you_page_config: ThankYouPageConfig;
	is_active: boolean;
	requires_auth: boolean;
	created_at: string;
	created_by: string;
	updated_at: string;
}

export interface RegistrationSubmission {
	id: string;
	registration_page_id: string;
	webinar_id: string;
	email: string;
	name: string;
	phone: string | null;
	custom_field_data: Record<string, unknown>;
	source: "public" | "internal";
	created_at: string;
	user_id: string | null;
}

export interface RegistrationTag {
	id: string;
	company_id: string;
	name: string;
	color: string;
	created_at: string;
	created_by: string;
}

export interface RegistrationTagAssignment {
	id: string;
	submission_id: string;
	tag_id: string;
	created_at: string;
}

// Email Reminders and Follow-up System Types

export interface EmailTemplate {
	id: string;
	company_id: string;
	name: string;
	type: "confirmation" | "reminder" | "attended" | "missed" | "custom";
	subject: string;
	body_html: string;
	body_text: string | null;
	variables: string[];
	is_active: boolean;
	created_at: string;
	created_by: string;
	updated_at: string;
}

export interface ReminderSchedule {
	id: string;
	webinar_id: string;
	registration_page_id: string | null;
	reminder_type: "email" | "dm" | "both";
	timing_hours_before: number;
	is_active: boolean;
	template_id: string | null;
	created_at: string;
	created_by: string;
}

export interface EmailQueue {
	id: string;
	webinar_id: string;
	submission_id: string;
	template_id: string | null;
	recipient_email: string;
	recipient_user_id: string | null;
	subject: string;
	body_html: string;
	body_text: string | null;
	status: "pending" | "sent" | "failed" | "cancelled";
	scheduled_for: string;
	sent_at: string | null;
	error_message: string | null;
	reminder_type: "confirmation" | "reminder" | "attended" | "missed";
	created_at: string;
}

export interface WebinarAttendance {
	id: string;
	webinar_id: string;
	submission_id: string;
	user_id: string | null;
	email: string;
	joined_at: string;
	left_at: string | null;
	duration_minutes: number | null;
	status: "attended" | "missed" | "partial";
	daily_session_id: string | null;
	created_at: string;
}

export interface EmailSendsLog {
	id: string;
	email_queue_id: string;
	provider: "resend" | "whop_dm";
	provider_message_id: string | null;
	status: "sent" | "delivered" | "bounced" | "failed";
	status_updated_at: string;
	metadata: Record<string, unknown>;
	created_at: string;
}

// Analytics & Reporting Types

export interface WebinarAnalyticsEvent {
	id: string;
	webinar_id: string;
	user_id: string | null;
	submission_id: string | null;
	event_type: "join" | "leave" | "cta_click" | "offer_click" | "purchase" | "replay_view" | "poll_response" | "question_asked" | "reaction" | "handout_download";
	event_data: Record<string, unknown>;
	device_info: Record<string, unknown>;
	browser_info: Record<string, unknown>;
	timestamp: string;
	created_at: string;
}

export interface WebinarDropoff {
	id: string;
	attendance_id: string;
	webinar_id: string;
	user_id: string | null;
	submission_id: string | null;
	left_at_minute: number;
	webinar_duration_minutes: number | null;
	reason: string | null;
	created_at: string;
}

export interface WebinarConversion {
	id: string;
	webinar_id: string;
	user_id: string;
	submission_id: string | null;
	conversion_type: "purchase" | "signup" | "subscription" | "download" | "link_click";
	product_id: string | null;
	amount: number;
	currency: string;
	whop_payment_id: string | null;
	metadata: Record<string, unknown>;
	created_at: string;
}

export interface WebinarRevenue {
	id: string;
	webinar_id: string;
	total_revenue: number;
	currency: string;
	total_purchases: number;
	platform_cut_percent: number;
	platform_cut_amount: number;
	creator_revenue: number;
	created_at: string;
	updated_at: string;
}

export interface WebinarDeviceAnalytics {
	id: string;
	attendance_id: string;
	webinar_id: string;
	user_id: string | null;
	submission_id: string | null;
	device_type: "desktop" | "mobile" | "tablet" | "unknown";
	platform: "iOS" | "Android" | "Windows" | "macOS" | "Linux" | "Chrome OS" | "unknown" | null;
	browser: "Chrome" | "Firefox" | "Safari" | "Edge" | "Opera" | "Brave" | "Other" | "unknown" | null;
	user_agent: string | null;
	screen_width: number | null;
	screen_height: number | null;
	created_at: string;
}

export interface CRMTag {
	id: string;
	webinar_id: string;
	user_id: string;
	submission_id: string | null;
	tags: string[];
	tag_data: Record<string, unknown>;
	created_at: string;
	updated_at: string;
}

// Team Management Types

export interface WebinarTeamMember {
	id: string;
	company_id: string;
	experience_id: string | null;
	user_id: string;
	role: "owner" | "host" | "moderator" | "admin";
	permissions: Record<string, unknown>;
	created_at: string;
	updated_at: string;
	created_by: string;
}

export interface WebinarBranding {
	id: string;
	company_id: string;
	logo_url: string | null;
	background_color: string;
	text_color: string;
	accent_color: string;
	favicon_url: string | null;
	custom_css: string | null;
	created_at: string;
	updated_at: string;
	updated_by: string;
}

// Whop Integration Types

export interface WebinarWhopProduct {
	id: string;
	webinar_id: string;
	whop_product_id: string;
	whop_company_id: string;
	is_required_for_access: boolean;
	product_name: string | null;
	product_price: number | null;
	product_currency: string;
	created_at: string;
	created_by: string;
}

export interface WebinarWhopTransaction {
	id: string;
	webinar_id: string;
	user_id: string;
	submission_id: string | null;
	whop_payment_id: string;
	whop_product_id: string;
	amount: number;
	currency: string;
	platform_cut_percent: number;
	platform_cut_amount: number;
	creator_revenue: number;
	status: "pending" | "completed" | "refunded" | "failed";
	metadata: Record<string, unknown>;
	created_at: string;
	updated_at: string;
}


