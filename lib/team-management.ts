import { supabaseServer } from "@/lib/supabase";
import type {
	WebinarTeamMember,
	WebinarBranding,
} from "@/lib/supabase";

export interface AddTeamMemberParams {
	companyId: string;
	experienceId?: string;
	userId: string;
	role: WebinarTeamMember["role"];
	permissions?: Record<string, unknown>;
	createdBy: string;
}

export interface UpdateTeamMemberParams {
	memberId: string;
	role?: WebinarTeamMember["role"];
	permissions?: Record<string, unknown>;
}

export interface UpdateBrandingParams {
	companyId: string;
	logoUrl?: string;
	backgroundColor?: string;
	textColor?: string;
	accentColor?: string;
	faviconUrl?: string;
	customCss?: string;
	updatedBy: string;
}

/**
 * Get team members for a company
 */
export async function getTeamMembers(
	companyId: string,
	experienceId?: string,
): Promise<WebinarTeamMember[]> {
	let query = supabaseServer
		.from("webinar_team_members")
		.select("*")
		.eq("company_id", companyId);

	if (experienceId) {
		query = query.or(`experience_id.is.null,experience_id.eq.${experienceId}`);
	} else {
		query = query.is("experience_id", null);
	}

	const { data, error } = await query.order("created_at", { ascending: false });

	if (error) {
		throw new Error(`Failed to fetch team members: ${error.message}`);
	}

	return data || [];
}

/**
 * Add team member
 */
export async function addTeamMember(
	params: AddTeamMemberParams,
): Promise<WebinarTeamMember> {
	const { data, error } = await supabaseServer
		.from("webinar_team_members")
		.insert([
			{
				company_id: params.companyId,
				experience_id: params.experienceId || null,
				user_id: params.userId,
				role: params.role,
				permissions: params.permissions || {},
				created_by: params.createdBy,
			},
		])
		.select()
		.single();

	if (error) {
		if (error.code === "23505") {
			// Unique constraint violation - member already exists
			const { data: existing } = await supabaseServer
				.from("webinar_team_members")
				.select("*")
				.eq("company_id", params.companyId)
				.eq("experience_id", params.experienceId || null)
				.eq("user_id", params.userId)
				.single();

			if (existing) {
				return existing;
			}
		}
		throw new Error(`Failed to add team member: ${error.message}`);
	}

	return data;
}

/**
 * Update team member role/permissions
 */
export async function updateTeamMember(
	params: UpdateTeamMemberParams,
): Promise<WebinarTeamMember> {
	const updateData: Partial<WebinarTeamMember> = {};

	if (params.role !== undefined) {
		updateData.role = params.role;
	}
	if (params.permissions !== undefined) {
		updateData.permissions = params.permissions;
	}

	const { data, error } = await supabaseServer
		.from("webinar_team_members")
		.update(updateData)
		.eq("id", params.memberId)
		.select()
		.single();

	if (error) {
		throw new Error(`Failed to update team member: ${error.message}`);
	}

	return data;
}

/**
 * Remove team member
 */
export async function removeTeamMember(memberId: string): Promise<void> {
	const { error } = await supabaseServer
		.from("webinar_team_members")
		.delete()
		.eq("id", memberId);

	if (error) {
		throw new Error(`Failed to remove team member: ${error.message}`);
	}
}

/**
 * Check if user has team permission
 */
export async function checkTeamPermission(
	userId: string,
	companyId: string,
	experienceId: string | null,
	requiredPermission?: string,
): Promise<{ hasAccess: boolean; role?: WebinarTeamMember["role"] }> {
	let query = supabaseServer
		.from("webinar_team_members")
		.select("*")
		.eq("company_id", companyId)
		.eq("user_id", userId);

	if (experienceId) {
		query = query.or(`experience_id.is.null,experience_id.eq.${experienceId}`);
	} else {
		query = query.is("experience_id", null);
	}

	const { data, error } = await query;

	if (error || !data || data.length === 0) {
		return { hasAccess: false };
	}

	const member = data[0];

	// Check role-based access
	const roles: WebinarTeamMember["role"][] = ["owner", "admin", "host", "moderator"];
	const userRoleIndex = roles.indexOf(member.role);
	const hasRoleAccess = userRoleIndex >= 0;

	// Check specific permission if required
	if (requiredPermission) {
		const permissions = member.permissions as Record<string, unknown>;
		const hasPermission =
			permissions[requiredPermission] === true ||
			permissions[requiredPermission] === "true";

		if (!hasPermission && !hasRoleAccess) {
			return { hasAccess: false, role: member.role };
		}
	}

	return { hasAccess: true, role: member.role };
}

/**
 * Get branding settings for a company
 */
export async function getBranding(
	companyId: string,
): Promise<WebinarBranding | null> {
	const { data, error } = await supabaseServer
		.from("webinar_branding")
		.select("*")
		.eq("company_id", companyId)
		.single();

	if (error) {
		if (error.code === "PGRST116") {
			// No rows returned - return default branding
			return null;
		}
		throw new Error(`Failed to fetch branding: ${error.message}`);
	}

	return data;
}

/**
 * Update branding settings
 */
export async function updateBranding(
	params: UpdateBrandingParams,
): Promise<WebinarBranding> {
	// Check if branding exists
	const existing = await getBranding(params.companyId);

	const brandingData: Partial<WebinarBranding> = {
		company_id: params.companyId,
		updated_by: params.updatedBy,
	};

	if (params.logoUrl !== undefined) {
		brandingData.logo_url = params.logoUrl;
	}
	if (params.backgroundColor !== undefined) {
		brandingData.background_color = params.backgroundColor;
	}
	if (params.textColor !== undefined) {
		brandingData.text_color = params.textColor;
	}
	if (params.accentColor !== undefined) {
		brandingData.accent_color = params.accentColor;
	}
	if (params.faviconUrl !== undefined) {
		brandingData.favicon_url = params.faviconUrl;
	}
	if (params.customCss !== undefined) {
		brandingData.custom_css = params.customCss;
	}

	if (existing) {
		// Update existing
		const { data, error } = await supabaseServer
			.from("webinar_branding")
			.update(brandingData)
			.eq("company_id", params.companyId)
			.select()
			.single();

		if (error) {
			throw new Error(`Failed to update branding: ${error.message}`);
		}

		return data;
	} else {
		// Create new
		const { data, error } = await supabaseServer
			.from("webinar_branding")
			.insert([
				{
					...brandingData,
					logo_url: params.logoUrl || null,
					background_color: params.backgroundColor || "#FFFFFF",
					text_color: params.textColor || "#000000",
					accent_color: params.accentColor || "#3B82F6",
					favicon_url: params.faviconUrl || null,
					custom_css: params.customCss || null,
				},
			])
			.select()
			.single();

		if (error) {
			throw new Error(`Failed to create branding: ${error.message}`);
		}

		return data;
	}
}

/**
 * Get default branding settings
 */
export function getDefaultBranding(): Omit<WebinarBranding, "id" | "company_id" | "created_at" | "updated_at" | "updated_by"> {
	return {
		logo_url: null,
		background_color: "#FFFFFF",
		text_color: "#000000",
		accent_color: "#3B82F6",
		favicon_url: null,
		custom_css: null,
	};
}

