import type { TemplateVariables } from "./template-variables";

/**
 * Ren<PERSON> template with variables
 */
export function renderTemplate(
	template: string,
	variables: TemplateVariables,
): string {
	let rendered = template;

	// Replace all {{variable}} patterns
	for (const [key, value] of Object.entries(variables)) {
		const regex = new RegExp(`\\{\\{${key}\\}\\}`, "g");
		rendered = rendered.replace(regex, value || "");
	}

	// Handle custom field variables ({{custom_field.field_id}})
	const customFieldRegex = /\{\{custom_field\.([^}]+)\}\}/g;
	rendered = rendered.replace(customFieldRegex, (match, fieldId) => {
		const customValue = variables[`custom_field_${fieldId}`];
		return customValue || "";
	});

	return rendered;
}

/**
 * Render HTML template
 */
export function renderHTMLTemplate(
	htmlTemplate: string,
	variables: TemplateVariables,
): string {
	return renderTemplate(htmlTemplate, variables);
}

/**
 * Render text template (strips HTML tags)
 */
export function renderTextTemplate(
	textTemplate: string,
	variables: TemplateVariables,
): string {
	let rendered = renderTemplate(textTemplate, variables);
	
	// Strip HTML tags for plain text
	rendered = rendered.replace(/<[^>]*>/g, "");
	
	// Decode HTML entities
	rendered = rendered
		.replace(/&nbsp;/g, " ")
		.replace(/&amp;/g, "&")
		.replace(/&lt;/g, "<")
		.replace(/&gt;/g, ">")
		.replace(/&quot;/g, '"')
		.replace(/&#39;/g, "'");

	return rendered.trim();
}

/**
 * Build template variables from registration submission and webinar data
 */
export function buildTemplateVariables(
	submission: {
		name: string;
		email: string;
		custom_field_data?: Record<string, unknown>;
		created_at: string;
	},
	webinar: {
		title: string;
		description?: string | null;
		scheduled_at: string;
		duration_minutes: number;
		timezone?: string | null;
	},
	options?: {
		joinUrl?: string;
		companyName?: string;
		experienceName?: string;
	},
): TemplateVariables {
	const scheduledDate = new Date(webinar.scheduled_at);
	const registrationDate = new Date(submission.created_at);

	const variables: TemplateVariables = {
		name: submission.name,
		email: submission.email,
		webinar_title: webinar.title,
		webinar_description: webinar.description || "",
		webinar_date: scheduledDate.toLocaleDateString("en-US", {
			month: "long",
			day: "numeric",
			year: "numeric",
			timeZone: webinar.timezone || "UTC",
		}),
		webinar_time: scheduledDate.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			timeZone: webinar.timezone || "UTC",
		}),
		webinar_duration: `${webinar.duration_minutes} minutes`,
		join_url: options?.joinUrl || "",
		registration_date: registrationDate.toLocaleDateString("en-US", {
			month: "long",
			day: "numeric",
			year: "numeric",
		}),
		company_name: options?.companyName || "",
		experience_name: options?.experienceName || "",
	};

	// Add custom fields
	if (submission.custom_field_data) {
		for (const [fieldId, value] of Object.entries(submission.custom_field_data)) {
			variables[`custom_field_${fieldId}`] = String(value);
		}
	}

	return variables;
}

