/**
 * Standard template variables available in email templates
 */
export interface TemplateVariables {
	name: string;
	email: string;
	webinar_title: string;
	webinar_description?: string;
	webinar_date: string;
	webinar_time: string;
	webinar_duration?: string;
	join_url: string;
	registration_date: string;
	company_name?: string;
	experience_name?: string;
	[key: string]: string | undefined; // For custom fields
}

/**
 * Extract template variables from template text
 */
export function parseTemplateVariables(template: string): string[] {
	const variableRegex = /\{\{([^}]+)\}\}/g;
	const variables: string[] = [];
	let match;

	while ((match = variableRegex.exec(template)) !== null) {
		const varName = match[1].trim();
		if (!variables.includes(varName)) {
			variables.push(varName);
		}
	}

	return variables;
}

/**
 * Standard template variables and their descriptions
 */
export const STANDARD_TEMPLATE_VARIABLES: Record<string, string> = {
	name: "Registrant's full name",
	email: "Registrant's email address",
	webinar_title: "Webinar title",
	webinar_description: "Webinar description",
	webinar_date: "Webinar date (formatted)",
	webinar_time: "Webinar time (formatted)",
	webinar_duration: "Webinar duration in minutes",
	join_url: "URL to join the webinar",
	registration_date: "Date when user registered",
	company_name: "Company name",
	experience_name: "Experience name",
	custom_field: "Custom field value (use {{custom_field.field_id}})",
};

/**
 * Get all available template variables
 */
export function getAvailableTemplateVariables(): string[] {
	return Object.keys(STANDARD_TEMPLATE_VARIABLES);
}

