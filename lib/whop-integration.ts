import { whopsdk } from "@/lib/whop-sdk";
import { supabaseServer } from "@/lib/supabase";
import type {
	WebinarWhopProduct,
	WebinarWhopTransaction,
} from "@/lib/supabase";

export interface WhopProduct {
	id: string;
	name: string;
	price: number;
	currency: string;
	company_id: string;
}

export interface LinkProductParams {
	webinarId: string;
	whopProductId: string;
	whopCompanyId: string;
	isRequiredForAccess: boolean;
	createdBy: string;
}

export interface CreateCheckoutParams {
	webinarId: string;
	productId: string;
	userId: string;
}

export interface PlatformCutConfig {
	platformCutPercent: number;
}

const DEFAULT_PLATFORM_CUT_PERCENT = 10.0; // Default 10% platform cut

/**
 * Get platform cut percentage from environment or use default
 */
function getPlatformCutPercent(): number {
	const envCut = process.env.PLATFORM_CUT_PERCENT;
	if (envCut) {
		const parsed = parseFloat(envCut);
		if (!isNaN(parsed) && parsed >= 0 && parsed <= 100) {
			return parsed;
		}
	}
	return DEFAULT_PLATFORM_CUT_PERCENT;
}

/**
 * Fetch products from Whop API for a company
 */
export async function getWhopProducts(
	companyId: string,
): Promise<WhopProduct[]> {
	try {
		// Note: This is a placeholder implementation
		// The actual Whop SDK method may vary based on the SDK version
		// Check Whop SDK documentation for the correct method
		
		// Example implementation (adjust based on actual SDK):
		const products = await whopsdk.products.list({
			company_id: companyId,
		});

		return products.data?.map((product: any) => ({
			id: product.id,
			name: product.title || "Unnamed Product",
			price: product.price?.amount || 0,
			currency: product.price?.currency || "USD",
			company_id: companyId,
		})) || [];
	} catch (error) {
		console.error("Error fetching Whop products:", error);
		throw new Error(
			`Failed to fetch Whop products: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * Link Whop product to webinar
 */
export async function linkProductToWebinar(
	params: LinkProductParams,
): Promise<WebinarWhopProduct> {
	// Fetch product details from Whop API
	let productName: string | null = null;
	let productPrice: number | null = null;
	let productCurrency = "USD";

	try {
		const product = await whopsdk.products.retrieve(params.whopProductId);
		if (product) {
			productName = product.title || null;
			// Handle price access safely - the Product type might not have price property
			const productAny = product as any;
			productPrice = productAny.price?.amount || null;
			productCurrency = productAny.price?.currency || "USD";
		}
	} catch (error) {
		console.warn("Could not fetch product details from Whop:", error);
		// Continue without product details
	}

	const { data, error } = await supabaseServer
		.from("webinar_whop_products")
		.insert([
			{
				webinar_id: params.webinarId,
				whop_product_id: params.whopProductId,
				whop_company_id: params.whopCompanyId,
				is_required_for_access: params.isRequiredForAccess,
				product_name: productName,
				product_price: productPrice,
				product_currency: productCurrency,
				created_by: params.createdBy,
			},
		])
		.select()
		.single();

	if (error) {
		if (error.code === "23505") {
			// Unique constraint violation - product already linked
			// Fetch existing record
			const { data: existing } = await supabaseServer
				.from("webinar_whop_products")
				.select("*")
				.eq("webinar_id", params.webinarId)
				.eq("whop_product_id", params.whopProductId)
				.single();

			if (existing) {
				return existing;
			}
		}
		throw new Error(`Failed to link product: ${error.message}`);
	}

	return data;
}

/**
 * Create checkout session using Whop API
 */
export async function createCheckoutSession(
	params: CreateCheckoutParams,
): Promise<{ checkout_url: string; session_id: string }> {
	try {
		// Get product details
		const { data: productLink, error: productError } = await supabaseServer
			.from("webinar_whop_products")
			.select("*")
			.eq("webinar_id", params.webinarId)
			.eq("whop_product_id", params.productId)
			.single();

		if (productError || !productLink) {
			throw new Error("Product not linked to webinar");
		}

		// Create checkout configuration using Whop SDK
		// Note: This is a placeholder implementation
		// The actual Whop SDK method may vary based on the SDK version
		// Check Whop SDK documentation for the correct method
		
		// Handle checkout creation with proper parameter names
		const checkoutParams: any = {
			productId: params.productId,
			userId: params.userId,
			// Additional metadata can be passed here
			metadata: {
				webinar_id: params.webinarId,
			},
		};

		const checkout = await whopsdk.checkoutConfigurations.create(checkoutParams);

		// Handle checkout response safely - the CheckoutConfiguration type might not have expected properties
		const checkoutAny = checkout as any;
		return {
			checkout_url: checkoutAny.url || checkoutAny.checkout_url || checkoutAny.checkoutUrl || "",
			session_id: checkoutAny.id || checkoutAny.session_id || checkoutAny.sessionId || "",
		};
	} catch (error) {
		console.error("Error creating checkout session:", error);
		throw new Error(
			`Failed to create checkout session: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * Handle Whop payment webhook
 */
export async function handleWhopPayment(
	paymentId: string,
	paymentData: any,
): Promise<WebinarWhopTransaction> {
	// Extract webinar_id from metadata
	const webinarId = paymentData.metadata?.webinar_id;
	if (!webinarId) {
		throw new Error("Webinar ID not found in payment metadata");
	}

	// Get product link
	const { data: productLink, error: productError } = await supabaseServer
		.from("webinar_whop_products")
		.select("*")
		.eq("webinar_id", webinarId)
		.eq("whop_product_id", paymentData.product_id)
		.single();

	if (productError || !productLink) {
		throw new Error("Product not linked to webinar");
	}

	// Calculate platform cut
	const platformCutPercent = getPlatformCutPercent();
	const amount = paymentData.amount || paymentData.price?.amount || 0;
	const platformCutAmount = (amount * platformCutPercent) / 100;
	const creatorRevenue = amount - platformCutAmount;

	// Check if transaction already exists (idempotency)
	const { data: existing } = await supabaseServer
		.from("webinar_whop_transactions")
		.select("*")
		.eq("whop_payment_id", paymentId)
		.single();

	if (existing) {
		return existing;
	}

	// Create transaction record
	const { data: transaction, error: transactionError } = await supabaseServer
		.from("webinar_whop_transactions")
		.insert([
			{
				webinar_id: webinarId,
				user_id: paymentData.user_id || paymentData.customer_id || "",
				whop_payment_id: paymentId,
				whop_product_id: paymentData.product_id || productLink.whop_product_id,
				amount: amount,
				currency: paymentData.currency || paymentData.price?.currency || "USD",
				platform_cut_percent: platformCutPercent,
				platform_cut_amount: platformCutAmount,
				creator_revenue: creatorRevenue,
				status: paymentData.status === "succeeded" ? "completed" : "pending",
				metadata: paymentData,
			},
		])
		.select()
		.single();

	if (transactionError) {
		throw new Error(`Failed to create transaction: ${transactionError.message}`);
	}

	// Track conversion
	if (paymentData.status === "succeeded") {
		try {
			const { trackConversion } = await import("@/lib/analytics");
			await trackConversion({
				webinarId,
				userId: paymentData.user_id || paymentData.customer_id || "",
				conversionType: "purchase",
				productId: paymentData.product_id || productLink.whop_product_id,
				amount: amount,
				currency: paymentData.currency || paymentData.price?.currency || "USD",
				whopPaymentId: paymentId,
			});
		} catch (error) {
			console.error("Failed to track conversion:", error);
			// Don't throw - transaction is already created
		}
	}

	return transaction;
}

/**
 * Calculate platform revenue share
 */
export function calculatePlatformCut(
	amount: number,
	platformCutPercent?: number,
): { platformCut: number; creatorRevenue: number } {
	const cutPercent = platformCutPercent || getPlatformCutPercent();
	const platformCut = (amount * cutPercent) / 100;
	const creatorRevenue = amount - platformCut;

	return {
		platformCut: Math.round(platformCut * 100) / 100,
		creatorRevenue: Math.round(creatorRevenue * 100) / 100,
	};
}

/**
 * Get transactions for a webinar
 */
export async function getWhopTransactions(
	webinarId: string,
	status?: WebinarWhopTransaction["status"],
): Promise<WebinarWhopTransaction[]> {
	let query = supabaseServer
		.from("webinar_whop_transactions")
		.select("*")
		.eq("webinar_id", webinarId)
		.order("created_at", { ascending: false });

	if (status) {
		query = query.eq("status", status);
	}

	const { data, error } = await query;

	if (error) {
		throw new Error(`Failed to fetch transactions: ${error.message}`);
	}

	return data || [];
}

/**
 * Get linked products for a webinar
 */
export async function getLinkedProducts(
	webinarId: string,
): Promise<WebinarWhopProduct[]> {
	const { data, error } = await supabaseServer
		.from("webinar_whop_products")
		.select("*")
		.eq("webinar_id", webinarId)
		.order("created_at", { ascending: false });

	if (error) {
		throw new Error(`Failed to fetch linked products: ${error.message}`);
	}

	return data || [];
}

/**
 * Unlink product from webinar
 */
export async function unlinkProductFromWebinar(
	webinarId: string,
	productId: string,
): Promise<void> {
	const { error } = await supabaseServer
		.from("webinar_whop_products")
		.delete()
		.eq("webinar_id", webinarId)
		.eq("whop_product_id", productId);

	if (error) {
		throw new Error(`Failed to unlink product: ${error.message}`);
	}
}

