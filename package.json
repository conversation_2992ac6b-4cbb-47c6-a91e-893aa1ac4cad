{"name": "whop-nextjs-app-template", "version": "0.2.0", "private": true, "type": "module", "scripts": {"dev": "whop-proxy --proxyPort=3000 --command=\"next dev\"", "build": "next build", "start": "next start", "lint": "biome lint"}, "dependencies": {"@daily-co/daily-js": "^0.85.0", "@rc-component/color-picker": "^3.0.2", "@supabase/supabase-js": "^2.78.0", "@tanstack/react-query": "^5.62.0", "@vercel/functions": "^3.1.4", "@whop/react": "0.3.0", "@whop/sdk": "0.0.3", "lucide-react": "^0.552.0", "next": "16.0.0", "react": "19.2.0", "react-dom": "19.2.0", "resend": "^6.4.0"}, "devDependencies": {"@biomejs/biome": "2.2.6", "@tailwindcss/postcss": "^4.1.14", "@tanstack/react-query-devtools": "^5.62.0", "@types/node": "^20.19.21", "@types/react": "19.2.2", "@types/react-dom": "19.2.2", "@whop-apps/dev-proxy": "0.0.1-canary.117", "dotenv-cli": "^10.0.0", "tailwindcss": "^4.1.14", "typescript": "^5.9.3"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531", "pnpm": {"overrides": {"@types/react": "19.2.2", "@types/react-dom": "19.2.2"}}}