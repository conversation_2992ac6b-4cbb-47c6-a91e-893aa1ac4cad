-- Analytics & Reporting Schema
-- Run this SQL in your Supabase SQL Editor

-- Analytics Events Table - Track all user events
CREATE TABLE IF NOT EXISTS webinar_analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  user_id TEXT,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE SET NULL,
  event_type TEXT NOT NULL CHECK (event_type IN ('join', 'leave', 'cta_click', 'offer_click', 'purchase', 'replay_view', 'poll_response', 'question_asked', 'reaction', 'handout_download')),
  event_data JSONB DEFAULT '{}'::jsonb,
  device_info JSONB DEFAULT '{}'::jsonb,
  browser_info JSONB DEFAULT '{}'::jsonb,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Dropoffs Table - Track when users leave during webinar
CREATE TABLE IF NOT EXISTS webinar_dropoffs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  attendance_id UUID REFERENCES webinar_attendance(id) ON DELETE CASCADE,
  webinar_id UUID NOT NULL,
  user_id TEXT,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE SET NULL,
  left_at_minute INTEGER NOT NULL, -- Minute in webinar when user left
  webinar_duration_minutes INTEGER, -- Total webinar duration
  reason TEXT, -- Optional reason for leaving
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversions Table - Track purchases and conversions
CREATE TABLE IF NOT EXISTS webinar_conversions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  user_id TEXT NOT NULL,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE SET NULL,
  conversion_type TEXT NOT NULL CHECK (conversion_type IN ('purchase', 'signup', 'subscription', 'download', 'link_click')),
  product_id TEXT, -- Whop product ID
  amount DECIMAL(10, 2),
  currency TEXT DEFAULT 'USD',
  whop_payment_id TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Revenue Table - Track revenue per webinar
CREATE TABLE IF NOT EXISTS webinar_revenue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL UNIQUE,
  total_revenue DECIMAL(10, 2) DEFAULT 0,
  currency TEXT DEFAULT 'USD',
  total_purchases INTEGER DEFAULT 0,
  platform_cut_percent DECIMAL(5, 2) DEFAULT 0, -- Platform revenue share percentage
  platform_cut_amount DECIMAL(10, 2) DEFAULT 0,
  creator_revenue DECIMAL(10, 2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Device Analytics Table - Track device/platform information
CREATE TABLE IF NOT EXISTS webinar_device_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  attendance_id UUID REFERENCES webinar_attendance(id) ON DELETE CASCADE,
  webinar_id UUID NOT NULL,
  user_id TEXT,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE SET NULL,
  device_type TEXT NOT NULL CHECK (device_type IN ('desktop', 'mobile', 'tablet', 'unknown')),
  platform TEXT CHECK (platform IN ('iOS', 'Android', 'Windows', 'macOS', 'Linux', 'Chrome OS', 'unknown')),
  browser TEXT CHECK (browser IN ('Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Brave', 'Other', 'unknown')),
  user_agent TEXT,
  screen_width INTEGER,
  screen_height INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- CRM Tags Table - Store behavioral tags for users
CREATE TABLE IF NOT EXISTS crm_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  user_id TEXT NOT NULL,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE SET NULL,
  tags TEXT[] DEFAULT '{}'::text[], -- Array of tag strings
  tag_data JSONB DEFAULT '{}'::jsonb, -- Additional tag metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(webinar_id, user_id)
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_analytics_events_webinar_id ON webinar_analytics_events(webinar_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON webinar_analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON webinar_analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON webinar_analytics_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_events_submission_id ON webinar_analytics_events(submission_id);

CREATE INDEX IF NOT EXISTS idx_dropoffs_webinar_id ON webinar_dropoffs(webinar_id);
CREATE INDEX IF NOT EXISTS idx_dropoffs_attendance_id ON webinar_dropoffs(attendance_id);
CREATE INDEX IF NOT EXISTS idx_dropoffs_left_at_minute ON webinar_dropoffs(left_at_minute);
CREATE INDEX IF NOT EXISTS idx_dropoffs_user_id ON webinar_dropoffs(user_id);

CREATE INDEX IF NOT EXISTS idx_conversions_webinar_id ON webinar_conversions(webinar_id);
CREATE INDEX IF NOT EXISTS idx_conversions_user_id ON webinar_conversions(user_id);
CREATE INDEX IF NOT EXISTS idx_conversions_conversion_type ON webinar_conversions(conversion_type);
CREATE INDEX IF NOT EXISTS idx_conversions_product_id ON webinar_conversions(product_id);
CREATE INDEX IF NOT EXISTS idx_conversions_created_at ON webinar_conversions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversions_whop_payment_id ON webinar_conversions(whop_payment_id);

CREATE INDEX IF NOT EXISTS idx_revenue_webinar_id ON webinar_revenue(webinar_id);

CREATE INDEX IF NOT EXISTS idx_device_analytics_webinar_id ON webinar_device_analytics(webinar_id);
CREATE INDEX IF NOT EXISTS idx_device_analytics_attendance_id ON webinar_device_analytics(attendance_id);
CREATE INDEX IF NOT EXISTS idx_device_analytics_device_type ON webinar_device_analytics(device_type);
CREATE INDEX IF NOT EXISTS idx_device_analytics_platform ON webinar_device_analytics(platform);
CREATE INDEX IF NOT EXISTS idx_device_analytics_browser ON webinar_device_analytics(browser);
CREATE INDEX IF NOT EXISTS idx_device_analytics_user_id ON webinar_device_analytics(user_id);

CREATE INDEX IF NOT EXISTS idx_crm_tags_webinar_id ON crm_tags(webinar_id);
CREATE INDEX IF NOT EXISTS idx_crm_tags_user_id ON crm_tags(user_id);
CREATE INDEX IF NOT EXISTS idx_crm_tags_submission_id ON crm_tags(submission_id);

-- Enable Row Level Security (RLS)
ALTER TABLE webinar_analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_dropoffs ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_conversions ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_revenue ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_device_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE crm_tags ENABLE ROW LEVEL SECURITY;

-- RLS Policies for webinar_analytics_events
CREATE POLICY "Allow read access to analytics events"
  ON webinar_analytics_events
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to analytics events"
  ON webinar_analytics_events
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_dropoffs
CREATE POLICY "Allow read access to dropoffs"
  ON webinar_dropoffs
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to dropoffs"
  ON webinar_dropoffs
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_conversions
CREATE POLICY "Allow read access to conversions"
  ON webinar_conversions
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to conversions"
  ON webinar_conversions
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to conversions"
  ON webinar_conversions
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_revenue
CREATE POLICY "Allow read access to revenue"
  ON webinar_revenue
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to revenue"
  ON webinar_revenue
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to revenue"
  ON webinar_revenue
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_device_analytics
CREATE POLICY "Allow read access to device analytics"
  ON webinar_device_analytics
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to device analytics"
  ON webinar_device_analytics
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for crm_tags
CREATE POLICY "Allow read access to crm tags"
  ON crm_tags
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to crm tags"
  ON crm_tags
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to crm tags"
  ON crm_tags
  FOR UPDATE
  USING (true);

-- Function to update revenue when conversion is created
CREATE OR REPLACE FUNCTION update_webinar_revenue()
RETURNS TRIGGER AS $$
DECLARE
  platform_cut_pct DECIMAL(5, 2);
  platform_cut_amt DECIMAL(10, 2);
  creator_rev DECIMAL(10, 2);
BEGIN
  -- Get platform cut percentage (default to 10% if not set)
  SELECT COALESCE(NEW.amount * 0.10, 0) INTO platform_cut_amt;
  platform_cut_pct := 10.00; -- Default platform cut percentage
  
  -- Calculate creator revenue
  creator_rev := NEW.amount - platform_cut_amt;
  
  -- Insert or update revenue record
  INSERT INTO webinar_revenue (
    webinar_id,
    total_revenue,
    currency,
    total_purchases,
    platform_cut_percent,
    platform_cut_amount,
    creator_revenue,
    updated_at
  )
  VALUES (
    NEW.webinar_id,
    NEW.amount,
    NEW.currency,
    1,
    platform_cut_pct,
    platform_cut_amt,
    creator_rev,
    NOW()
  )
  ON CONFLICT (webinar_id) DO UPDATE SET
    total_revenue = webinar_revenue.total_revenue + NEW.amount,
    total_purchases = webinar_revenue.total_purchases + 1,
    platform_cut_amount = webinar_revenue.platform_cut_amount + platform_cut_amt,
    creator_revenue = webinar_revenue.creator_revenue + creator_rev,
    updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update revenue on conversion
CREATE TRIGGER trigger_update_revenue_on_conversion
  AFTER INSERT ON webinar_conversions
  FOR EACH ROW
  WHEN (NEW.conversion_type = 'purchase' AND NEW.amount > 0)
  EXECUTE FUNCTION update_webinar_revenue();

