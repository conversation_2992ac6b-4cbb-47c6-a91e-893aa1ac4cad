-- Email Reminders and Follow-up System Schema for Supabase
-- Run this SQL in your Supabase SQL Editor

-- <PERSON>ail Templates Table
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id TEXT NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('confirmation', 'reminder', 'attended', 'missed', 'custom')),
  subject TEXT NOT NULL,
  body_html TEXT NOT NULL,
  body_text TEXT,
  variables JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reminder Schedules Table
CREATE TABLE IF NOT EXISTS reminder_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  registration_page_id UUID REFERENCES registration_pages(id) ON DELETE CASCADE,
  reminder_type TEXT NOT NULL CHECK (reminder_type IN ('email', 'dm', 'both')),
  timing_hours_before INTEGER NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL
);

-- Email Queue Table
CREATE TABLE IF NOT EXISTS email_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE CASCADE,
  template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
  recipient_email TEXT NOT NULL,
  recipient_user_id TEXT,
  subject TEXT NOT NULL,
  body_html TEXT NOT NULL,
  body_text TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  reminder_type TEXT NOT NULL CHECK (reminder_type IN ('confirmation', 'reminder', 'attended', 'missed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Attendance Tracking Table
CREATE TABLE IF NOT EXISTS webinar_attendance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE CASCADE,
  user_id TEXT,
  email TEXT NOT NULL,
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  left_at TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER,
  status TEXT DEFAULT 'attended' CHECK (status IN ('attended', 'missed', 'partial')),
  daily_session_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Sends Log Table
CREATE TABLE IF NOT EXISTS email_sends_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email_queue_id UUID REFERENCES email_queue(id) ON DELETE CASCADE,
  provider TEXT NOT NULL CHECK (provider IN ('resend', 'whop_dm')),
  provider_message_id TEXT,
  status TEXT NOT NULL DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'bounced', 'failed')),
  status_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_email_templates_company_id ON email_templates(company_id);
CREATE INDEX IF NOT EXISTS idx_email_templates_type ON email_templates(type);
CREATE INDEX IF NOT EXISTS idx_email_templates_is_active ON email_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_reminder_schedules_webinar_id ON reminder_schedules(webinar_id);
CREATE INDEX IF NOT EXISTS idx_reminder_schedules_registration_page_id ON reminder_schedules(registration_page_id);
CREATE INDEX IF NOT EXISTS idx_reminder_schedules_is_active ON reminder_schedules(is_active);

CREATE INDEX IF NOT EXISTS idx_email_queue_webinar_id ON email_queue(webinar_id);
CREATE INDEX IF NOT EXISTS idx_email_queue_submission_id ON email_queue(submission_id);
CREATE INDEX IF NOT EXISTS idx_email_queue_status ON email_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_queue_scheduled_for ON email_queue(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_email_queue_recipient_email ON email_queue(recipient_email);

CREATE INDEX IF NOT EXISTS idx_webinar_attendance_webinar_id ON webinar_attendance(webinar_id);
CREATE INDEX IF NOT EXISTS idx_webinar_attendance_submission_id ON webinar_attendance(submission_id);
CREATE INDEX IF NOT EXISTS idx_webinar_attendance_email ON webinar_attendance(email);
CREATE INDEX IF NOT EXISTS idx_webinar_attendance_status ON webinar_attendance(status);
CREATE INDEX IF NOT EXISTS idx_webinar_attendance_joined_at ON webinar_attendance(joined_at);

CREATE INDEX IF NOT EXISTS idx_email_sends_log_email_queue_id ON email_sends_log(email_queue_id);
CREATE INDEX IF NOT EXISTS idx_email_sends_log_provider ON email_sends_log(provider);
CREATE INDEX IF NOT EXISTS idx_email_sends_log_status ON email_sends_log(status);

-- Enable Row Level Security (RLS)
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE reminder_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_sends_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies for email_templates
CREATE POLICY "Allow authenticated users to read email templates"
  ON email_templates
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to email templates"
  ON email_templates
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to email templates"
  ON email_templates
  FOR UPDATE
  USING (true);

CREATE POLICY "Allow delete access to email templates"
  ON email_templates
  FOR DELETE
  USING (true);

-- RLS Policies for reminder_schedules
CREATE POLICY "Allow authenticated users to read reminder schedules"
  ON reminder_schedules
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to reminder schedules"
  ON reminder_schedules
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to reminder schedules"
  ON reminder_schedules
  FOR UPDATE
  USING (true);

CREATE POLICY "Allow delete access to reminder schedules"
  ON reminder_schedules
  FOR DELETE
  USING (true);

-- RLS Policies for email_queue
CREATE POLICY "Allow authenticated users to read email queue"
  ON email_queue
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to email queue"
  ON email_queue
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to email queue"
  ON email_queue
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_attendance
CREATE POLICY "Allow public insert to webinar attendance"
  ON webinar_attendance
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to read webinar attendance"
  ON webinar_attendance
  FOR SELECT
  USING (true);

CREATE POLICY "Allow update access to webinar attendance"
  ON webinar_attendance
  FOR UPDATE
  USING (true);

-- RLS Policies for email_sends_log
CREATE POLICY "Allow authenticated users to read email sends log"
  ON email_sends_log
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to email sends log"
  ON email_sends_log
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to email sends log"
  ON email_sends_log
  FOR UPDATE
  USING (true);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_email_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_email_templates_updated_at
  BEFORE UPDATE ON email_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_email_templates_updated_at();

-- Function to calculate duration_minutes when left_at is updated
CREATE OR REPLACE FUNCTION calculate_attendance_duration()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.left_at IS NOT NULL AND NEW.joined_at IS NOT NULL THEN
    NEW.duration_minutes = EXTRACT(EPOCH FROM (NEW.left_at - NEW.joined_at)) / 60;
    
    -- Determine status based on duration (5 minutes threshold)
    IF NEW.duration_minutes >= 5 THEN
      NEW.status = 'attended';
    ELSIF NEW.duration_minutes > 0 THEN
      NEW.status = 'partial';
    ELSE
      NEW.status = 'missed';
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to calculate duration
CREATE TRIGGER calculate_attendance_duration_trigger
  BEFORE UPDATE ON webinar_attendance
  FOR EACH ROW
  EXECUTE FUNCTION calculate_attendance_duration();

