-- Registration Page Builder Schema for Supabase
-- Run this SQL in your Supabase SQL Editor

-- Registration Pages Table
CREATE TABLE IF NOT EXISTS registration_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  experience_id TEXT NOT NULL,
  company_id TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  template_id TEXT NOT NULL DEFAULT 'standard',
  title TEXT NOT NULL,
  description TEXT,
  custom_fields JSONB DEFAULT '[]'::jsonb,
  thank_you_page_config JSONB DEFAULT '{}'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  requires_auth BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Registration Submissions Table
CREATE TABLE IF NOT EXISTS registration_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  registration_page_id UUID NOT NULL REFERENCES registration_pages(id) ON DELETE CASCADE,
  webinar_id UUID NOT NULL,
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  phone TEXT,
  custom_field_data JSONB DEFAULT '{}'::jsonb,
  source TEXT NOT NULL CHECK (source IN ('public', 'internal')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id TEXT
);

-- Registration Tags Table
CREATE TABLE IF NOT EXISTS registration_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id TEXT NOT NULL,
  name TEXT NOT NULL,
  color TEXT NOT NULL DEFAULT '#3B82F6',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL,
  UNIQUE(company_id, name)
);

-- Registration Tag Assignments Table
CREATE TABLE IF NOT EXISTS registration_tag_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  submission_id UUID NOT NULL REFERENCES registration_submissions(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES registration_tags(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(submission_id, tag_id)
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_registration_pages_webinar_id ON registration_pages(webinar_id);
CREATE INDEX IF NOT EXISTS idx_registration_pages_experience_id ON registration_pages(experience_id);
CREATE INDEX IF NOT EXISTS idx_registration_pages_company_id ON registration_pages(company_id);
CREATE INDEX IF NOT EXISTS idx_registration_pages_slug ON registration_pages(slug);
CREATE INDEX IF NOT EXISTS idx_registration_pages_is_active ON registration_pages(is_active);

CREATE INDEX IF NOT EXISTS idx_registration_submissions_page_id ON registration_submissions(registration_page_id);
CREATE INDEX IF NOT EXISTS idx_registration_submissions_webinar_id ON registration_submissions(webinar_id);
CREATE INDEX IF NOT EXISTS idx_registration_submissions_email ON registration_submissions(email);
CREATE INDEX IF NOT EXISTS idx_registration_submissions_created_at ON registration_submissions(created_at);

CREATE INDEX IF NOT EXISTS idx_registration_tags_company_id ON registration_tags(company_id);

CREATE INDEX IF NOT EXISTS idx_registration_tag_assignments_submission_id ON registration_tag_assignments(submission_id);
CREATE INDEX IF NOT EXISTS idx_registration_tag_assignments_tag_id ON registration_tag_assignments(tag_id);

-- Enable Row Level Security (RLS)
ALTER TABLE registration_pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE registration_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE registration_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE registration_tag_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for registration_pages
-- Allow public read access to active pages
CREATE POLICY "Allow public read access to active registration pages"
  ON registration_pages
  FOR SELECT
  USING (is_active = TRUE);

-- Allow authenticated users to read all pages they have access to
CREATE POLICY "Allow authenticated users to read registration pages"
  ON registration_pages
  FOR SELECT
  USING (true);

-- Allow insert for authenticated users (admin check in API layer)
CREATE POLICY "Allow insert access to registration pages"
  ON registration_pages
  FOR INSERT
  WITH CHECK (true);

-- Allow update for authenticated users (admin check in API layer)
CREATE POLICY "Allow update access to registration pages"
  ON registration_pages
  FOR UPDATE
  USING (true);

-- Allow delete for authenticated users (admin check in API layer)
CREATE POLICY "Allow delete access to registration pages"
  ON registration_pages
  FOR DELETE
  USING (true);

-- RLS Policies for registration_submissions
-- Allow public insert (for public registrations)
CREATE POLICY "Allow public insert to registration submissions"
  ON registration_submissions
  FOR INSERT
  WITH CHECK (true);

-- Allow authenticated users to read submissions (admin check in API layer)
CREATE POLICY "Allow authenticated users to read registration submissions"
  ON registration_submissions
  FOR SELECT
  USING (true);

-- RLS Policies for registration_tags
-- Allow authenticated users to read tags (admin check in API layer)
CREATE POLICY "Allow authenticated users to read registration tags"
  ON registration_tags
  FOR SELECT
  USING (true);

-- Allow insert for authenticated users (admin check in API layer)
CREATE POLICY "Allow insert access to registration tags"
  ON registration_tags
  FOR INSERT
  WITH CHECK (true);

-- Allow update for authenticated users (admin check in API layer)
CREATE POLICY "Allow update access to registration tags"
  ON registration_tags
  FOR UPDATE
  USING (true);

-- Allow delete for authenticated users (admin check in API layer)
CREATE POLICY "Allow delete access to registration tags"
  ON registration_tags
  FOR DELETE
  USING (true);

-- RLS Policies for registration_tag_assignments
-- Allow authenticated users to read assignments (admin check in API layer)
CREATE POLICY "Allow authenticated users to read registration tag assignments"
  ON registration_tag_assignments
  FOR SELECT
  USING (true);

-- Allow insert for authenticated users (admin check in API layer)
CREATE POLICY "Allow insert access to registration tag assignments"
  ON registration_tag_assignments
  FOR INSERT
  WITH CHECK (true);

-- Allow delete for authenticated users (admin check in API layer)
CREATE POLICY "Allow delete access to registration tag assignments"
  ON registration_tag_assignments
  FOR DELETE
  USING (true);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_registration_pages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_registration_pages_updated_at
  BEFORE UPDATE ON registration_pages
  FOR EACH ROW
  EXECUTE FUNCTION update_registration_pages_updated_at();

