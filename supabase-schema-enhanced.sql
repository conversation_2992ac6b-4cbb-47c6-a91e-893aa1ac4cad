-- Webinar table schema updates for Supabase
-- Run this SQL in your Supabase SQL Editor

-- Add new columns for enhanced features
ALTER TABLE webinars 
ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC',
ADD COLUMN IF NOT EXISTS password TEXT,
ADD COLUMN IF NOT EXISTS is_password_protected BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS price_amount INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS price_currency TEXT DEFAULT 'USD',
ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS custom_branding_logo_url TEXT,
ADD COLUMN IF NOT EXISTS custom_branding_background_color TEXT,
ADD COLUMN IF NOT EXISTS custom_branding_text_color TEXT,
ADD COLUMN IF NOT EXISTS custom_branding_accent_color TEXT,
ADD COLUMN IF NOT EXISTS presenter_roles JSONB DEFAULT '{}';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_webinars_timezone ON webinars(timezone);
CREATE INDEX IF NOT EXISTS idx_webinars_is_paid ON webinars(is_paid);
CREATE INDEX IF NOT EXISTS idx_webinars_is_password_protected ON webinars(is_password_protected);

-- Note: The presenter_roles JSONB field will store a map like:
-- {"host_id_1": "host", "host_id_2": "moderator", "host_id_3": "panelist"}
-- Where roles can be: "host", "moderator", "presenter", "panelist", "co-host"

