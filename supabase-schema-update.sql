-- Webinar table schema for Supabase
-- Run this SQL in your Supabase SQL Editor

-- First, add new columns if they don't exist
ALTER TABLE webinars 
ADD COLUMN IF NOT EXISTS event_type TEXT NOT NULL DEFAULT 'live' CHECK (event_type IN ('live', 'right_now', 'recurring', 'always_on', 'evergreen_room')),
ADD COLUMN IF NOT EXISTS recurrence_pattern TEXT CHECK (recurrence_pattern IN ('daily', 'weekly', 'monthly')),
ADD COLUMN IF NOT EXISTS recurrence_interval INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS recurrence_end_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS recurrence_count INTEGER,
ADD COLUMN IF NOT EXISTS is_evergreen BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS room_name TEXT;

-- Update existing rows to have event_type
UPDATE webinars SET event_type = 'live' WHERE event_type IS NULL;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_webinars_event_type ON webinars(event_type);
CREATE INDEX IF NOT EXISTS idx_webinars_is_evergreen ON webinars(is_evergreen);

-- Note: The original table creation SQL is below for reference
-- If you're creating the table fresh, use this instead:

/*
CREATE TABLE IF NOT EXISTS webinars (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  experience_id TEXT NOT NULL,
  company_id TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER NOT NULL DEFAULT 60,
  host_ids TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'completed', 'cancelled')),
  event_type TEXT NOT NULL DEFAULT 'live' CHECK (event_type IN ('live', 'right_now', 'recurring', 'always_on', 'evergreen_room')),
  recurrence_pattern TEXT CHECK (recurrence_pattern IN ('daily', 'weekly', 'monthly')),
  recurrence_interval INTEGER DEFAULT 1,
  recurrence_end_date TIMESTAMP WITH TIME ZONE,
  recurrence_count INTEGER,
  is_evergreen BOOLEAN DEFAULT FALSE,
  room_name TEXT
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_webinars_experience_id ON webinars(experience_id);
CREATE INDEX IF NOT EXISTS idx_webinars_company_id ON webinars(company_id);
CREATE INDEX IF NOT EXISTS idx_webinars_scheduled_at ON webinars(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_webinars_status ON webinars(status);
CREATE INDEX IF NOT EXISTS idx_webinars_event_type ON webinars(event_type);
CREATE INDEX IF NOT EXISTS idx_webinars_is_evergreen ON webinars(is_evergreen);

-- Enable Row Level Security (RLS)
ALTER TABLE webinars ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read webinars for experiences they have access to
CREATE POLICY "Allow read access to webinars"
  ON webinars
  FOR SELECT
  USING (true);

-- Policy: Allow users to create webinars (admin check should be done in API layer)
CREATE POLICY "Allow insert access to webinars"
  ON webinars
  FOR INSERT
  WITH CHECK (true);

-- Policy: Allow users to update webinars they created
CREATE POLICY "Allow update access to own webinars"
  ON webinars
  FOR UPDATE
  USING (true);

-- Policy: Allow users to delete webinars they created
CREATE POLICY "Allow delete access to own webinars"
  ON webinars
  FOR DELETE
  USING (true);
*/

