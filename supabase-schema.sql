-- Webinar table schema for Supabase
-- Run this SQL in your Supabase SQL Editor

CREATE TABLE IF NOT EXISTS webinars (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  experience_id TEXT NOT NULL,
  company_id TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER NOT NULL DEFAULT 60,
  host_ids TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'completed', 'cancelled'))
);

-- Indexes for better query performance
-- Note: Foreign key constraints removed as <PERSON><PERSON> manages experiences/companies externally
CREATE INDEX IF NOT EXISTS idx_webinars_experience_id ON webinars(experience_id);
CREATE INDEX IF NOT EXISTS idx_webinars_company_id ON webinars(company_id);
CREATE INDEX IF NOT EXISTS idx_webinars_scheduled_at ON webinars(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_webinars_status ON webinars(status);

-- Enable Row Level Security (RLS)
ALTER TABLE webinars ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read webinars for experiences they have access to
-- Note: This is a basic policy. You may need to adjust based on your Whop integration
CREATE POLICY "Allow read access to webinars"
  ON webinars
  FOR SELECT
  USING (true);

-- Policy: Allow users to create webinars (admin check should be done in API layer)
CREATE POLICY "Allow insert access to webinars"
  ON webinars
  FOR INSERT
  WITH CHECK (true);

-- Policy: Allow users to update webinars they created
CREATE POLICY "Allow update access to own webinars"
  ON webinars
  FOR UPDATE
  USING (true);

-- Policy: Allow users to delete webinars they created
CREATE POLICY "Allow delete access to own webinars"
  ON webinars
  FOR DELETE
  USING (true);

