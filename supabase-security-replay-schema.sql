-- Security, Recording, and Replay Schema
-- Run this SQL in your Supabase SQL Editor

-- Join Links Table
CREATE TABLE IF NOT EXISTS webinar_join_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE CASCADE,
  token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  access_window_start TIMESTAMP WITH TIME ZONE, -- Minutes before webinar start when access begins
  access_window_end TIMESTAMP WITH TIME ZONE, -- Minutes after webinar start when access ends
  is_one_time_use BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP WITH TIME ZONE,
  use_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wait Room Table
CREATE TABLE IF NOT EXISTS webinar_wait_room (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  user_id TEXT NOT NULL,
  user_email TEXT NOT NULL,
  user_name TEXT NOT NULL,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  admitted_at TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'waiting' CHECK (status IN ('waiting', 'admitted', 'denied')),
  queue_position INTEGER,
  daily_session_id TEXT,
  UNIQUE(webinar_id, user_id)
);

-- Recordings Table
CREATE TABLE IF NOT EXISTS webinar_recordings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  daily_recording_id TEXT,
  recording_url TEXT,
  recording_type TEXT NOT NULL DEFAULT 'cloud' CHECK (recording_type IN ('cloud', 'raw-tracks', 'local')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'recording', 'processing', 'completed', 'failed')),
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  file_size_bytes BIGINT,
  thumbnail_url TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recording Segments Table
CREATE TABLE IF NOT EXISTS webinar_recording_segments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recording_id UUID NOT NULL REFERENCES webinar_recordings(id) ON DELETE CASCADE,
  segment_type TEXT NOT NULL CHECK (segment_type IN ('video', 'audio', 'screen_share')),
  segment_url TEXT NOT NULL,
  start_time DECIMAL(10, 2) NOT NULL, -- Seconds from recording start
  duration_seconds DECIMAL(10, 2),
  file_size_bytes BIGINT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Replays Table
CREATE TABLE IF NOT EXISTS webinar_replays (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  recording_id UUID NOT NULL REFERENCES webinar_recordings(id) ON DELETE CASCADE,
  replay_mode TEXT NOT NULL DEFAULT 'on-demand' CHECK (replay_mode IN ('on-demand', 'scheduled')),
  scheduled_start_time TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  access_settings JSONB DEFAULT '{}'::jsonb,
  view_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Replay Interactions Table (stores timing data for synchronized replay)
CREATE TABLE IF NOT EXISTS webinar_replay_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  replay_id UUID REFERENCES webinar_replays(id) ON DELETE CASCADE,
  interaction_type TEXT NOT NULL CHECK (interaction_type IN ('chat', 'poll', 'poll_response', 'cta', 'cta_interaction', 'reaction', 'question', 'answer')),
  interaction_id UUID NOT NULL, -- ID of the original interaction (chat message, poll, etc.)
  timestamp_offset DECIMAL(10, 2) NOT NULL, -- Seconds from webinar start
  interaction_data JSONB NOT NULL, -- Full interaction data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_join_links_webinar_id ON webinar_join_links(webinar_id);
CREATE INDEX IF NOT EXISTS idx_join_links_submission_id ON webinar_join_links(submission_id);
CREATE INDEX IF NOT EXISTS idx_join_links_token ON webinar_join_links(token);
CREATE INDEX IF NOT EXISTS idx_join_links_expires_at ON webinar_join_links(expires_at);

CREATE INDEX IF NOT EXISTS idx_wait_room_webinar_id ON webinar_wait_room(webinar_id);
CREATE INDEX IF NOT EXISTS idx_wait_room_user_id ON webinar_wait_room(user_id);
CREATE INDEX IF NOT EXISTS idx_wait_room_status ON webinar_wait_room(status);
CREATE INDEX IF NOT EXISTS idx_wait_room_queue_position ON webinar_wait_room(queue_position);

CREATE INDEX IF NOT EXISTS idx_recordings_webinar_id ON webinar_recordings(webinar_id);
CREATE INDEX IF NOT EXISTS idx_recordings_status ON webinar_recordings(status);
CREATE INDEX IF NOT EXISTS idx_recordings_daily_recording_id ON webinar_recordings(daily_recording_id);

CREATE INDEX IF NOT EXISTS idx_recording_segments_recording_id ON webinar_recording_segments(recording_id);
CREATE INDEX IF NOT EXISTS idx_recording_segments_type ON webinar_recording_segments(segment_type);

CREATE INDEX IF NOT EXISTS idx_replays_webinar_id ON webinar_replays(webinar_id);
CREATE INDEX IF NOT EXISTS idx_replays_recording_id ON webinar_replays(recording_id);
CREATE INDEX IF NOT EXISTS idx_replays_replay_mode ON webinar_replays(replay_mode);
CREATE INDEX IF NOT EXISTS idx_replays_expires_at ON webinar_replays(expires_at);

CREATE INDEX IF NOT EXISTS idx_replay_interactions_webinar_id ON webinar_replay_interactions(webinar_id);
CREATE INDEX IF NOT EXISTS idx_replay_interactions_replay_id ON webinar_replay_interactions(replay_id);
CREATE INDEX IF NOT EXISTS idx_replay_interactions_type ON webinar_replay_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_replay_interactions_timestamp ON webinar_replay_interactions(timestamp_offset);

-- Enable Row Level Security (RLS)
ALTER TABLE webinar_join_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_wait_room ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_recordings ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_recording_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_replays ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_replay_interactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for webinar_join_links
CREATE POLICY "Allow read access to join links"
  ON webinar_join_links
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to join links"
  ON webinar_join_links
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to join links"
  ON webinar_join_links
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_wait_room
CREATE POLICY "Allow read access to wait room"
  ON webinar_wait_room
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to wait room"
  ON webinar_wait_room
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to wait room"
  ON webinar_wait_room
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_recordings
CREATE POLICY "Allow read access to recordings"
  ON webinar_recordings
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to recordings"
  ON webinar_recordings
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to recordings"
  ON webinar_recordings
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_recording_segments
CREATE POLICY "Allow read access to recording segments"
  ON webinar_recording_segments
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to recording segments"
  ON webinar_recording_segments
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_replays
CREATE POLICY "Allow read access to replays"
  ON webinar_replays
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to replays"
  ON webinar_replays
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to replays"
  ON webinar_replays
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_replay_interactions
CREATE POLICY "Allow read access to replay interactions"
  ON webinar_replay_interactions
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to replay interactions"
  ON webinar_replay_interactions
  FOR INSERT
  WITH CHECK (true);

-- Function to generate secure join link token
CREATE OR REPLACE FUNCTION generate_join_link_token()
RETURNS TEXT AS $$
BEGIN
  RETURN encode(gen_random_bytes(32), 'base64url');
END;
$$ LANGUAGE plpgsql;

-- Function to update replay view count
CREATE OR REPLACE FUNCTION increment_replay_view_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE webinar_replays
  SET view_count = view_count + 1
  WHERE id = NEW.replay_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_increment_replay_view_count
  AFTER INSERT ON webinar_replay_interactions
  FOR EACH ROW
  WHEN (NEW.interaction_type = 'view')
  EXECUTE FUNCTION increment_replay_view_count();

