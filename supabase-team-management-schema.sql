-- Team Management Schema
-- Run this SQL in your Supabase SQL Editor

-- Team Members Table - Store team member roles per company/experience
CREATE TABLE IF NOT EXISTS webinar_team_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id TEXT NOT NULL,
  experience_id TEXT, -- Optional: can be null for company-wide roles
  user_id TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('owner', 'host', 'moderator', 'admin')),
  permissions JSONB DEFAULT '{}'::jsonb, -- Additional permissions like ['create_webinar', 'edit_webinar', 'view_analytics', etc.]
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL, -- User who added this team member
  UNIQUE(company_id, experience_id, user_id)
);

-- Branding Table - Store custom branding per account
CREATE TABLE IF NOT EXISTS webinar_branding (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id TEXT NOT NULL UNIQUE,
  logo_url TEXT,
  background_color TEXT DEFAULT '#FFFFFF',
  text_color TEXT DEFAULT '#000000',
  accent_color TEXT DEFAULT '#3B82F6',
  favicon_url TEXT,
  custom_css TEXT, -- Optional custom CSS
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by TEXT NOT NULL
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_team_members_company_id ON webinar_team_members(company_id);
CREATE INDEX IF NOT EXISTS idx_team_members_experience_id ON webinar_team_members(experience_id);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON webinar_team_members(user_id);
CREATE INDEX IF NOT EXISTS idx_team_members_role ON webinar_team_members(role);
CREATE INDEX IF NOT EXISTS idx_team_members_company_experience ON webinar_team_members(company_id, experience_id);

CREATE INDEX IF NOT EXISTS idx_branding_company_id ON webinar_branding(company_id);

-- Enable Row Level Security (RLS)
ALTER TABLE webinar_team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_branding ENABLE ROW LEVEL SECURITY;

-- RLS Policies for webinar_team_members
CREATE POLICY "Allow read access to team members"
  ON webinar_team_members
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to team members"
  ON webinar_team_members
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to team members"
  ON webinar_team_members
  FOR UPDATE
  USING (true);

CREATE POLICY "Allow delete access to team members"
  ON webinar_team_members
  FOR DELETE
  USING (true);

-- RLS Policies for webinar_branding
CREATE POLICY "Allow read access to branding"
  ON webinar_branding
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to branding"
  ON webinar_branding
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to branding"
  ON webinar_branding
  FOR UPDATE
  USING (true);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER trigger_update_team_members_updated_at
  BEFORE UPDATE ON webinar_team_members
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_branding_updated_at
  BEFORE UPDATE ON webinar_branding
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

