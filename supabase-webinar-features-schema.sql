-- Additional Webinar Room Features Schema
-- Run this SQL in your Supabase SQL Editor after the base schema

-- Polls Table
CREATE TABLE IF NOT EXISTS webinar_polls (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  created_by TEXT NOT NULL,
  title TEXT NOT NULL,
  question TEXT NOT NULL,
  poll_type TEXT NOT NULL DEFAULT 'single' CHECK (poll_type IN ('single', 'multiple', 'survey')),
  options JSONB NOT NULL DEFAULT '[]'::jsonb, -- Array of {id, text, order} objects
  is_active BOOLEAN DEFAULT FALSE,
  is_anonymous BOOLEAN DEFAULT FALSE,
  show_results BOOLEAN DEFAULT TRUE,
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Poll Responses Table
CREATE TABLE IF NOT EXISTS webinar_poll_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  poll_id UUID NOT NULL REFERENCES webinar_polls(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  user_name TEXT NOT NULL,
  selected_options JSONB NOT NULL, -- Array of option IDs
  responded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(poll_id, user_id)
);

-- Handouts/Files Table
CREATE TABLE IF NOT EXISTS webinar_handouts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  uploaded_by TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_type TEXT NOT NULL, -- 'pdf', 'zip', 'document', etc.
  file_size INTEGER, -- Size in bytes
  description TEXT,
  is_visible BOOLEAN DEFAULT TRUE,
  download_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Handout Downloads Tracking
CREATE TABLE IF NOT EXISTS webinar_handout_downloads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  handout_id UUID NOT NULL REFERENCES webinar_handouts(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  downloaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(handout_id, user_id)
);

-- Call-to-Action (CTA) Pop-ups Table
CREATE TABLE IF NOT EXISTS webinar_ctas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  created_by TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  button_text TEXT NOT NULL DEFAULT 'Buy Now',
  button_url TEXT NOT NULL,
  image_url TEXT,
  scheduled_time TIMESTAMP WITH TIME ZONE, -- When to auto-show (optional)
  duration_seconds INTEGER DEFAULT 30, -- How long to show
  countdown_seconds INTEGER DEFAULT 60, -- Countdown timer duration
  is_active BOOLEAN DEFAULT FALSE, -- Currently showing
  is_scheduled BOOLEAN DEFAULT FALSE,
  show_countdown BOOLEAN DEFAULT TRUE,
  trigger_type TEXT NOT NULL DEFAULT 'manual' CHECK (trigger_type IN ('manual', 'scheduled', 'both')),
  times_shown INTEGER DEFAULT 0,
  clicks INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- CTA Views/Clicks Tracking
CREATE TABLE IF NOT EXISTS webinar_cta_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cta_id UUID NOT NULL REFERENCES webinar_ctas(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  interaction_type TEXT NOT NULL CHECK (interaction_type IN ('viewed', 'clicked', 'dismissed')),
  interacted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reactions/Emojis Table
CREATE TABLE IF NOT EXISTS webinar_reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  user_id TEXT NOT NULL,
  user_name TEXT NOT NULL,
  reaction_type TEXT NOT NULL CHECK (reaction_type IN ('👍', '❤️', '😂', '🎉', '🔥', '👏', '💯', '🚀')),
  duration_ms INTEGER DEFAULT 3000, -- How long to show the reaction
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Pre-recorded Videos Table
CREATE TABLE IF NOT EXISTS webinar_videos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  uploaded_by TEXT NOT NULL,
  video_url TEXT NOT NULL,
  video_title TEXT NOT NULL,
  video_thumbnail_url TEXT,
  duration_seconds INTEGER,
  is_playing BOOLEAN DEFAULT FALSE,
  playback_position DECIMAL(10, 2) DEFAULT 0, -- Current playback position in seconds
  played_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video Playback Tracking
CREATE TABLE IF NOT EXISTS webinar_video_playbacks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  video_id UUID NOT NULL REFERENCES webinar_videos(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  watched_duration_seconds INTEGER DEFAULT 0
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_polls_webinar_id ON webinar_polls(webinar_id);
CREATE INDEX IF NOT EXISTS idx_polls_is_active ON webinar_polls(is_active);
CREATE INDEX IF NOT EXISTS idx_polls_created_at ON webinar_polls(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_poll_responses_poll_id ON webinar_poll_responses(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_responses_user_id ON webinar_poll_responses(user_id);

CREATE INDEX IF NOT EXISTS idx_handouts_webinar_id ON webinar_handouts(webinar_id);
CREATE INDEX IF NOT EXISTS idx_handouts_is_visible ON webinar_handouts(is_visible);
CREATE INDEX IF NOT EXISTS idx_handouts_created_at ON webinar_handouts(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_handout_downloads_handout_id ON webinar_handout_downloads(handout_id);
CREATE INDEX IF NOT EXISTS idx_handout_downloads_user_id ON webinar_handout_downloads(user_id);

CREATE INDEX IF NOT EXISTS idx_ctas_webinar_id ON webinar_ctas(webinar_id);
CREATE INDEX IF NOT EXISTS idx_ctas_is_active ON webinar_ctas(is_active);
CREATE INDEX IF NOT EXISTS idx_ctas_scheduled_time ON webinar_ctas(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_ctas_created_at ON webinar_ctas(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_cta_interactions_cta_id ON webinar_cta_interactions(cta_id);
CREATE INDEX IF NOT EXISTS idx_cta_interactions_user_id ON webinar_cta_interactions(user_id);

CREATE INDEX IF NOT EXISTS idx_reactions_webinar_id ON webinar_reactions(webinar_id);
CREATE INDEX IF NOT EXISTS idx_reactions_created_at ON webinar_reactions(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_videos_webinar_id ON webinar_videos(webinar_id);
CREATE INDEX IF NOT EXISTS idx_videos_is_playing ON webinar_videos(is_playing);
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON webinar_videos(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_video_playbacks_video_id ON webinar_video_playbacks(video_id);
CREATE INDEX IF NOT EXISTS idx_video_playbacks_user_id ON webinar_video_playbacks(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE webinar_polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_poll_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_handouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_handout_downloads ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_ctas ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_cta_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_video_playbacks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for webinar_polls
CREATE POLICY "Allow read access to polls"
  ON webinar_polls
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to polls"
  ON webinar_polls
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to polls"
  ON webinar_polls
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_poll_responses
CREATE POLICY "Allow read access to poll responses"
  ON webinar_poll_responses
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to poll responses"
  ON webinar_poll_responses
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_handouts
CREATE POLICY "Allow read access to handouts"
  ON webinar_handouts
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to handouts"
  ON webinar_handouts
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to handouts"
  ON webinar_handouts
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_handout_downloads
CREATE POLICY "Allow read access to handout downloads"
  ON webinar_handout_downloads
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to handout downloads"
  ON webinar_handout_downloads
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_ctas
CREATE POLICY "Allow read access to CTAs"
  ON webinar_ctas
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to CTAs"
  ON webinar_ctas
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to CTAs"
  ON webinar_ctas
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_cta_interactions
CREATE POLICY "Allow read access to CTA interactions"
  ON webinar_cta_interactions
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to CTA interactions"
  ON webinar_cta_interactions
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_reactions
CREATE POLICY "Allow read access to reactions"
  ON webinar_reactions
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to reactions"
  ON webinar_reactions
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_videos
CREATE POLICY "Allow read access to videos"
  ON webinar_videos
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to videos"
  ON webinar_videos
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to videos"
  ON webinar_videos
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_video_playbacks
CREATE POLICY "Allow read access to video playbacks"
  ON webinar_video_playbacks
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to video playbacks"
  ON webinar_video_playbacks
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to video playbacks"
  ON webinar_video_playbacks
  FOR UPDATE
  USING (true);

-- Function to update handout download count
CREATE OR REPLACE FUNCTION update_handout_download_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE webinar_handouts
  SET download_count = download_count + 1
  WHERE id = NEW.handout_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_handout_download_count
  AFTER INSERT ON webinar_handout_downloads
  FOR EACH ROW
  EXECUTE FUNCTION update_handout_download_count();

-- Function to update CTA click count
CREATE OR REPLACE FUNCTION update_cta_click_count()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.interaction_type = 'clicked' THEN
    UPDATE webinar_ctas
    SET clicks = clicks + 1
    WHERE id = NEW.cta_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_cta_click_count
  AFTER INSERT ON webinar_cta_interactions
  FOR EACH ROW
  EXECUTE FUNCTION update_cta_click_count();

