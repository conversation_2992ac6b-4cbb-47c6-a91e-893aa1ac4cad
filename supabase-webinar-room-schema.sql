-- Webinar Room Features Schema
-- Run this SQL in your Supabase SQL Editor

-- Chat Messages Table
CREATE TABLE IF NOT EXISTS webinar_chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  user_id TEXT NOT NULL,
  user_name TEXT NOT NULL,
  message TEXT NOT NULL,
  message_type TEXT NOT NULL DEFAULT 'public' CHECK (message_type IN ('public', 'private')),
  recipient_user_id TEXT, -- For private messages
  is_deleted BOOLEAN DEFAULT FALSE,
  deleted_at TIMESTAMP WITH TIME ZONE,
  deleted_by TEXT, -- User who deleted the message
  is_highlighted BOOLEAN DEFAULT FALSE, -- For host/moderator messages
  user_role TEXT DEFAULT 'attendee' CHECK (user_role IN ('host', 'co-host', 'moderator', 'attendee')),
  daily_session_id TEXT, -- Daily.co session ID
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Moderation Actions Table
CREATE TABLE IF NOT EXISTS webinar_moderation_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  target_user_id TEXT NOT NULL, -- User being moderated
  action_type TEXT NOT NULL CHECK (action_type IN ('mute', 'unmute', 'block', 'unblock', 'kick', 'ban')),
  performed_by TEXT NOT NULL, -- User who performed the action
  reason TEXT,
  expires_at TIMESTAMP WITH TIME ZONE, -- For temporary bans/blocks
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Private Chat Conversations Table
CREATE TABLE IF NOT EXISTS webinar_private_chats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  attendee_user_id TEXT NOT NULL, -- Attendee initiating the chat
  host_user_id TEXT NOT NULL, -- Host receiving the chat
  is_active BOOLEAN DEFAULT TRUE,
  last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(webinar_id, attendee_user_id, host_user_id)
);

-- Q&A Questions Table
CREATE TABLE IF NOT EXISTS webinar_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  asked_by_user_id TEXT NOT NULL,
  asked_by_name TEXT NOT NULL,
  question TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'answered', 'dismissed')),
  answered_by_user_id TEXT, -- Host who answered
  answered_by_name TEXT, -- Host name
  answer TEXT,
  answered_at TIMESTAMP WITH TIME ZONE,
  is_highlighted BOOLEAN DEFAULT FALSE, -- For important questions
  upvotes INTEGER DEFAULT 0, -- For question popularity
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Q&A Upvotes Table (to track who upvoted)
CREATE TABLE IF NOT EXISTS webinar_question_upvotes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID NOT NULL REFERENCES webinar_questions(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(question_id, user_id)
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_chat_messages_webinar_id ON webinar_chat_messages(webinar_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON webinar_chat_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_recipient ON webinar_chat_messages(recipient_user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON webinar_chat_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chat_messages_type ON webinar_chat_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_chat_messages_deleted ON webinar_chat_messages(is_deleted);

CREATE INDEX IF NOT EXISTS idx_moderation_webinar_id ON webinar_moderation_actions(webinar_id);
CREATE INDEX IF NOT EXISTS idx_moderation_target_user ON webinar_moderation_actions(target_user_id);
CREATE INDEX IF NOT EXISTS idx_moderation_active ON webinar_moderation_actions(is_active);
CREATE INDEX IF NOT EXISTS idx_moderation_created_at ON webinar_moderation_actions(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_private_chats_webinar_id ON webinar_private_chats(webinar_id);
CREATE INDEX IF NOT EXISTS idx_private_chats_attendee ON webinar_private_chats(attendee_user_id);
CREATE INDEX IF NOT EXISTS idx_private_chats_host ON webinar_private_chats(host_user_id);
CREATE INDEX IF NOT EXISTS idx_private_chats_active ON webinar_private_chats(is_active);

CREATE INDEX IF NOT EXISTS idx_questions_webinar_id ON webinar_questions(webinar_id);
CREATE INDEX IF NOT EXISTS idx_questions_status ON webinar_questions(status);
CREATE INDEX IF NOT EXISTS idx_questions_asked_by ON webinar_questions(asked_by_user_id);
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON webinar_questions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_questions_upvotes ON webinar_questions(upvotes DESC);

CREATE INDEX IF NOT EXISTS idx_question_upvotes_question_id ON webinar_question_upvotes(question_id);
CREATE INDEX IF NOT EXISTS idx_question_upvotes_user_id ON webinar_question_upvotes(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE webinar_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_moderation_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_private_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_question_upvotes ENABLE ROW LEVEL SECURITY;

-- RLS Policies for webinar_chat_messages
-- Allow authenticated users to read messages for webinars they have access to
CREATE POLICY "Allow read access to chat messages"
  ON webinar_chat_messages
  FOR SELECT
  USING (true);

-- Allow authenticated users to insert messages
CREATE POLICY "Allow insert access to chat messages"
  ON webinar_chat_messages
  FOR INSERT
  WITH CHECK (true);

-- Allow users to update/delete their own messages or admins/moderators to delete any message
CREATE POLICY "Allow update access to chat messages"
  ON webinar_chat_messages
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_moderation_actions
-- Allow authenticated users to read moderation actions
CREATE POLICY "Allow read access to moderation actions"
  ON webinar_moderation_actions
  FOR SELECT
  USING (true);

-- Allow authenticated users to insert moderation actions (admin check in API layer)
CREATE POLICY "Allow insert access to moderation actions"
  ON webinar_moderation_actions
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_private_chats
-- Allow users to read their own private chats
CREATE POLICY "Allow read access to own private chats"
  ON webinar_private_chats
  FOR SELECT
  USING (true);

-- Allow authenticated users to insert private chats
CREATE POLICY "Allow insert access to private chats"
  ON webinar_private_chats
  FOR INSERT
  WITH CHECK (true);

-- RLS Policies for webinar_questions
-- Allow authenticated users to read questions
CREATE POLICY "Allow read access to questions"
  ON webinar_questions
  FOR SELECT
  USING (true);

-- Allow authenticated users to insert questions
CREATE POLICY "Allow insert access to questions"
  ON webinar_questions
  FOR INSERT
  WITH CHECK (true);

-- Allow authenticated users to update questions (admin check in API layer)
CREATE POLICY "Allow update access to questions"
  ON webinar_questions
  FOR UPDATE
  USING (true);

-- RLS Policies for webinar_question_upvotes
-- Allow authenticated users to read upvotes
CREATE POLICY "Allow read access to question upvotes"
  ON webinar_question_upvotes
  FOR SELECT
  USING (true);

-- Allow authenticated users to insert upvotes
CREATE POLICY "Allow insert access to question upvotes"
  ON webinar_question_upvotes
  FOR INSERT
  WITH CHECK (true);

-- Allow authenticated users to delete upvotes (for unvoting)
CREATE POLICY "Allow delete access to question upvotes"
  ON webinar_question_upvotes
  FOR DELETE
  USING (true);

