-- Whop Integration Schema
-- Run this SQL in your Supabase SQL Editor

-- Whop Products Table - Link webinars to Whop products
CREATE TABLE IF NOT EXISTS webinar_whop_products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  whop_product_id TEXT NOT NULL,
  whop_company_id TEXT NOT NULL,
  is_required_for_access BOOLEAN DEFAULT TRUE, -- If true, user must purchase to access webinar
  product_name TEXT, -- Cache product name for display
  product_price DECIMAL(10, 2),
  product_currency TEXT DEFAULT 'USD',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by TEXT NOT NULL,
  UNIQUE(webinar_id, whop_product_id)
);

-- Whop Transactions Table - Track Whop transactions
CREATE TABLE IF NOT EXISTS webinar_whop_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webinar_id UUID NOT NULL,
  user_id TEXT NOT NULL,
  submission_id UUID REFERENCES registration_submissions(id) ON DELETE SET NULL,
  whop_payment_id TEXT NOT NULL UNIQUE,
  whop_product_id TEXT NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  platform_cut_percent DECIMAL(5, 2) DEFAULT 10.00, -- Platform revenue share percentage
  platform_cut_amount DECIMAL(10, 2) NOT NULL,
  creator_revenue DECIMAL(10, 2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'refunded', 'failed')),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_whop_products_webinar_id ON webinar_whop_products(webinar_id);
CREATE INDEX IF NOT EXISTS idx_whop_products_whop_product_id ON webinar_whop_products(whop_product_id);
CREATE INDEX IF NOT EXISTS idx_whop_products_whop_company_id ON webinar_whop_products(whop_company_id);
CREATE INDEX IF NOT EXISTS idx_whop_products_is_required ON webinar_whop_products(is_required_for_access);

CREATE INDEX IF NOT EXISTS idx_whop_transactions_webinar_id ON webinar_whop_transactions(webinar_id);
CREATE INDEX IF NOT EXISTS idx_whop_transactions_user_id ON webinar_whop_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_whop_transactions_whop_payment_id ON webinar_whop_transactions(whop_payment_id);
CREATE INDEX IF NOT EXISTS idx_whop_transactions_whop_product_id ON webinar_whop_transactions(whop_product_id);
CREATE INDEX IF NOT EXISTS idx_whop_transactions_status ON webinar_whop_transactions(status);
CREATE INDEX IF NOT EXISTS idx_whop_transactions_created_at ON webinar_whop_transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_whop_transactions_submission_id ON webinar_whop_transactions(submission_id);

-- Enable Row Level Security (RLS)
ALTER TABLE webinar_whop_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE webinar_whop_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for webinar_whop_products
CREATE POLICY "Allow read access to whop products"
  ON webinar_whop_products
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to whop products"
  ON webinar_whop_products
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to whop products"
  ON webinar_whop_products
  FOR UPDATE
  USING (true);

CREATE POLICY "Allow delete access to whop products"
  ON webinar_whop_products
  FOR DELETE
  USING (true);

-- RLS Policies for webinar_whop_transactions
CREATE POLICY "Allow read access to whop transactions"
  ON webinar_whop_transactions
  FOR SELECT
  USING (true);

CREATE POLICY "Allow insert access to whop transactions"
  ON webinar_whop_transactions
  FOR INSERT
  WITH CHECK (true);

CREATE POLICY "Allow update access to whop transactions"
  ON webinar_whop_transactions
  FOR UPDATE
  USING (true);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_whop_transactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER trigger_update_whop_transactions_updated_at
  BEFORE UPDATE ON webinar_whop_transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_whop_transactions_updated_at();

