import { supabaseServer } from "../lib/supabase";
import { sendEmailFromQueue } from "../lib/email";
import { sendDMFromQueue } from "../lib/whop-dm";

/**
 * Railway Worker: Process pending emails from queue
 * Runs every 15 minutes via Railway cron
 */
export default async function processEmailQueue() {
	console.log("Starting email queue processing...");

	try {
		// Get pending emails that are due to be sent
		const now = new Date().toISOString();
		const { data: pendingEmails, error } = await supabaseServer
			.from("email_queue")
			.select("*")
			.eq("status", "pending")
			.lte("scheduled_for", now)
			.order("scheduled_for", { ascending: true })
			.limit(100); // Process up to 100 emails per run

		if (error) {
			console.error("Error fetching pending emails:", error);
			return;
		}

		if (!pendingEmails || pendingEmails.length === 0) {
			console.log("No pending emails to process");
			return;
		}

		console.log(`Processing ${pendingEmails.length} pending emails...`);

		for (const email of pendingEmails) {
			try {
				let result: { success: boolean; messageId?: string; error?: string };
				let provider: "resend" | "whop_dm" = "resend";

				// Check if this is a DM (has recipient_user_id) or email
				if (email.recipient_user_id && email.reminder_type === "reminder") {
					// Try DM first for reminders with user_id
					result = await sendDMFromQueue(email);
					provider = "whop_dm";
					
					// If DM fails, fall back to email
					if (!result.success && email.recipient_email) {
						result = await sendEmailFromQueue(email);
						provider = "resend";
					}
				} else {
					// Send as email
					result = await sendEmailFromQueue(email);
				}

				// Update queue status
				if (result.success) {
					await supabaseServer
						.from("email_queue")
						.update({
							status: "sent",
							sent_at: new Date().toISOString(),
						})
						.eq("id", email.id);

					// Log the send
					await supabaseServer.from("email_sends_log").insert([
						{
							email_queue_id: email.id,
							provider,
							provider_message_id: result.messageId || null,
							status: "sent",
							metadata: {},
						},
					]);

					console.log(`Sent email ${email.id} via ${provider}`);
				} else {
					// Mark as failed
					await supabaseServer
						.from("email_queue")
						.update({
							status: "failed",
							error_message: result.error || "Unknown error",
						})
						.eq("id", email.id);

					console.error(`Failed to send email ${email.id}:`, result.error);
				}
			} catch (err) {
				console.error(`Error processing email ${email.id}:`, err);
				await supabaseServer
					.from("email_queue")
					.update({
						status: "failed",
						error_message: err instanceof Error ? err.message : "Unknown error",
					})
					.eq("id", email.id);
			}
		}

		console.log("Email queue processing completed");
	} catch (error) {
		console.error("Fatal error in email queue processing:", error);
		throw error;
	}
}

// For Railway cron, export the default function
if (require.main === module) {
	processEmailQueue()
		.then(() => {
			console.log("Worker completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Worker failed:", error);
			process.exit(1);
		});
}

