import { supabaseServer } from "../lib/supabase";
import { queueEmail } from "../lib/email-queue";
import { buildTemplateVariables } from "../lib/template-renderer";
import { renderHTMLTemplate, renderTextTemplate } from "../lib/template-renderer";
import type { EmailTemplate, Webinar, RegistrationSubmission, WebinarAttendance } from "../lib/supabase";

/**
 * Railway Worker: Process follow-up emails for completed webinars
 * Runs every hour via Railway cron
 */
export default async function processFollowUpEmails() {
	console.log("Starting follow-up email processing...");

	try {
		// Get webinars that ended in the last hour
		const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
		const now = new Date();

		const { data: completedWebinars, error: webinarError } = await supabaseServer
			.from("webinars")
			.select("*")
			.eq("status", "completed")
			.gte("scheduled_at", oneHourAgo.toISOString())
			.lte("scheduled_at", now.toISOString());

		if (webinarError) {
			console.error("Error fetching completed webinars:", webinarError);
			return;
		}

		if (!completedWebinars || completedWebinars.length === 0) {
			console.log("No completed webinars to process");
			return;
		}

		console.log(`Processing ${completedWebinars.length} completed webinars...`);

		for (const webinar of completedWebinars) {
			try {
				// Get all registrations for this webinar
				const { data: submissions } = await supabaseServer
					.from("registration_submissions")
					.select("*")
					.eq("webinar_id", webinar.id);

				if (!submissions || submissions.length === 0) {
					continue;
				}

				// Get attendance records
				const { data: attendanceRecords } = await supabaseServer
					.from("webinar_attendance")
					.select("*")
					.eq("webinar_id", webinar.id);

				const attendanceMap = new Map<string, WebinarAttendance>();
				(attendanceRecords || []).forEach((att) => {
					attendanceMap.set(att.submission_id, att);
				});

				// Get email templates
				const { data: templates } = await supabaseServer
					.from("email_templates")
					.select("*")
					.in("type", ["attended", "missed"])
					.eq("is_active", true);

				const templatesMap = new Map<string, EmailTemplate>();
				(templates || []).forEach((t) => {
					templatesMap.set(t.type, t);
				});

				// Process each submission
				for (const submission of submissions) {
					const attendance = attendanceMap.get(submission.id);
					const attended = attendance && attendance.status === "attended";

					// Determine template type
					const templateType = attended ? "attended" : "missed";
					const template = templatesMap.get(templateType);

					// Build variables
					const variables = buildTemplateVariables(
						{
							name: submission.name,
							email: submission.email,
							custom_field_data: submission.custom_field_data,
							created_at: submission.created_at,
						},
						{
							title: webinar.title,
							description: webinar.description,
							scheduled_at: webinar.scheduled_at,
							duration_minutes: webinar.duration_minutes,
							timezone: webinar.timezone,
						},
					);

					// Add attendance-specific variables
					if (attendance) {
						variables.duration_minutes = String(attendance.duration_minutes || 0);
					}

					// Render email content
					let subject: string;
					let bodyHtml: string;
					let bodyText: string | undefined;

					if (template) {
						subject = renderHTMLTemplate(template.subject, variables);
						bodyHtml = renderHTMLTemplate(template.body_html, variables);
						bodyText = template.body_text
							? renderTextTemplate(template.body_text, variables)
							: undefined;
					} else {
						// Default templates
						if (attended) {
							subject = `Thank you for attending: ${webinar.title}`;
							bodyHtml = `
								<h1>Thank you for attending!</h1>
								<p>Hi ${submission.name},</p>
								<p>Thank you for attending <strong>${webinar.title}</strong>.</p>
								${attendance.duration_minutes ? `<p>You were present for ${attendance.duration_minutes} minutes.</p>` : ""}
								<p>We hope you found it valuable!</p>
							`;
							bodyText = `
								Thank you for attending!
								
								Hi ${submission.name},
								
								Thank you for attending ${webinar.title}.
								${attendance.duration_minutes ? `You were present for ${attendance.duration_minutes} minutes.` : ""}
								
								We hope you found it valuable!
							`;
						} else {
							subject = `We missed you at: ${webinar.title}`;
							bodyHtml = `
								<h1>We missed you!</h1>
								<p>Hi ${submission.name},</p>
								<p>We noticed you weren't able to attend <strong>${webinar.title}</strong>.</p>
								<p>Would you like to be notified about future webinars?</p>
							`;
							bodyText = `
								We missed you!
								
								Hi ${submission.name},
								
								We noticed you weren't able to attend ${webinar.title}.
								
								Would you like to be notified about future webinars?
							`;
						}
					}

					// Queue follow-up email
					await queueEmail({
						webinarId: webinar.id,
						submissionId: submission.id,
						recipientEmail: submission.email,
						recipientUserId: submission.user_id || undefined,
						templateId: template?.id,
						subject,
						bodyHtml,
						bodyText,
						reminderType: templateType as "attended" | "missed",
						scheduledFor: new Date(), // Send immediately
					});

					console.log(`Queued ${templateType} email for ${submission.email}`);
				}
			} catch (err) {
				console.error(`Error processing follow-up emails for webinar ${webinar.id}:`, err);
			}
		}

		console.log("Follow-up email processing completed");
	} catch (error) {
		console.error("Fatal error in follow-up email processing:", error);
		throw error;
	}
}

// For Railway cron, export the default function
if (require.main === module) {
	processFollowUpEmails()
		.then(() => {
			console.log("Worker completed successfully");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Worker failed:", error);
			process.exit(1);
		});
}

